/**
 * Targon 集成验证脚本
 * 全面验证 Targon API 在聊天界面中的集成状态
 */

import { getAIModels, AI_MODEL_CATEGORIES } from '@/config/aiModels.js'
import { generateChatCompletion } from '@/services/chatService.js'
import targonApi from '@/services/targonApi.js'

/**
 * 验证模型配置集成
 */
export async function verifyModelConfiguration() {
  console.log('🔍 验证模型配置集成...')
  
  const results = {
    categories: null,
    models: null,
    targonModels: null
  }
  
  try {
    // 1. 检查分类配置
    const targonCategory = AI_MODEL_CATEGORIES.find(cat => cat.id === 'targon')
    results.categories = {
      hasTargonCategory: !!targonCategory,
      categoryInfo: targonCategory
    }
    
    // 2. 检查模型列表
    const allModels = await getAIModels()
    const targonModels = allModels.filter(model => model.category === 'targon')
    
    results.models = {
      totalModels: allModels.length,
      targonModelsCount: targonModels.length,
      hasTargonModels: targonModels.length > 0
    }
    
    results.targonModels = targonModels.map(model => ({
      id: model.id,
      name: model.name,
      description: model.description,
      keyPool: model.keyPool,
      isFree: model.isFree
    }))
    
    console.log('✅ 模型配置验证完成')
    return { success: true, results }
    
  } catch (error) {
    console.error('❌ 模型配置验证失败:', error)
    return { success: false, error: error.message, results }
  }
}

/**
 * 验证聊天服务集成
 */
export async function verifyChatServiceIntegration() {
  console.log('🔍 验证聊天服务集成...')
  
  const testCases = [
    {
      model: 'deepseek-ai/DeepSeek-V3',
      message: '你好，请简单回复"测试成功"'
    },
    {
      model: 'moonshot/Kimi-K2-Instruct',
      message: '请回复"集成正常"'
    }
  ]
  
  const results = []
  
  for (const testCase of testCases) {
    try {
      console.log(`🧪 测试模型: ${testCase.model}`)
      
      const response = await generateChatCompletion(
        [{ role: 'user', content: testCase.message }],
        { model: testCase.model, temperature: 0.3, max_tokens: 50 }
      )
      
      const success = response && response.choices && response.choices[0]
      const content = success ? response.choices[0].message.content : null
      
      results.push({
        model: testCase.model,
        success,
        response: content,
        error: success ? null : '响应格式不正确'
      })
      
      console.log(success ? '✅' : '❌', `${testCase.model}:`, success ? '成功' : '失败')
      
    } catch (error) {
      console.error('❌', `${testCase.model}:`, error.message)
      results.push({
        model: testCase.model,
        success: false,
        response: null,
        error: error.message
      })
    }
  }
  
  const successCount = results.filter(r => r.success).length
  const overallSuccess = successCount === testCases.length
  
  console.log(`📊 聊天服务测试结果: ${successCount}/${testCases.length} 成功`)
  
  return {
    success: overallSuccess,
    results,
    summary: {
      total: testCases.length,
      passed: successCount,
      failed: testCases.length - successCount
    }
  }
}

/**
 * 验证密钥池状态
 */
export async function verifyKeyPoolStatus() {
  console.log('🔍 验证密钥池状态...')
  
  try {
    const status = targonApi.getKeyPoolStatus()
    
    const isHealthy = status.totalKeys > 0 && status.healthyKeys > 0
    const healthRatio = status.totalKeys > 0 ? (status.healthyKeys / status.totalKeys) : 0
    
    const results = {
      totalKeys: status.totalKeys,
      healthyKeys: status.healthyKeys,
      currentKeyIndex: status.currentKeyIndex,
      healthRatio: Math.round(healthRatio * 100),
      isHealthy,
      lastRotation: status.lastRotation
    }
    
    console.log('📊 密钥池状态:')
    console.log(`  - 总密钥: ${results.totalKeys}`)
    console.log(`  - 健康密钥: ${results.healthyKeys}`)
    console.log(`  - 健康率: ${results.healthRatio}%`)
    console.log(`  - 状态: ${isHealthy ? '正常' : '异常'}`)
    
    return {
      success: isHealthy,
      results,
      message: isHealthy ? '密钥池状态正常' : '密钥池状态异常'
    }
    
  } catch (error) {
    console.error('❌ 密钥池状态验证失败:', error)
    return {
      success: false,
      error: error.message,
      message: '无法获取密钥池状态'
    }
  }
}

/**
 * 验证 API 连接
 */
export async function verifyApiConnection() {
  console.log('🔍 验证 API 连接...')
  
  try {
    const connectionResult = await targonApi.testConnection()
    
    if (connectionResult.success) {
      console.log('✅ API 连接正常')
      console.log(`📊 可用模型数: ${connectionResult.data.modelsCount}`)
      
      return {
        success: true,
        results: {
          connected: true,
          modelsCount: connectionResult.data.modelsCount,
          timestamp: connectionResult.data.timestamp,
          currentKey: connectionResult.data.currentKey
        },
        message: 'API 连接正常'
      }
    } else {
      console.log('❌ API 连接失败:', connectionResult.message)
      return {
        success: false,
        results: {
          connected: false,
          error: connectionResult.message
        },
        message: `API 连接失败: ${connectionResult.message}`
      }
    }
    
  } catch (error) {
    console.error('❌ API 连接验证异常:', error)
    return {
      success: false,
      error: error.message,
      message: 'API 连接验证异常'
    }
  }
}

/**
 * 运行完整的集成验证
 */
export async function runFullVerification() {
  console.log('🚀 开始运行 Targon 完整集成验证...\n')
  
  const verificationResults = {
    modelConfiguration: null,
    keyPoolStatus: null,
    apiConnection: null,
    chatServiceIntegration: null,
    summary: null,
    timestamp: new Date().toISOString()
  }
  
  // 1. 验证模型配置
  console.log('=== 1. 模型配置验证 ===')
  verificationResults.modelConfiguration = await verifyModelConfiguration()
  console.log('')
  
  // 2. 验证密钥池状态
  console.log('=== 2. 密钥池状态验证 ===')
  verificationResults.keyPoolStatus = await verifyKeyPoolStatus()
  console.log('')
  
  // 3. 验证 API 连接
  console.log('=== 3. API 连接验证 ===')
  verificationResults.apiConnection = await verifyApiConnection()
  console.log('')
  
  // 4. 验证聊天服务集成
  console.log('=== 4. 聊天服务集成验证 ===')
  verificationResults.chatServiceIntegration = await verifyChatServiceIntegration()
  console.log('')
  
  // 生成验证摘要
  const verifications = [
    verificationResults.modelConfiguration?.success,
    verificationResults.keyPoolStatus?.success,
    verificationResults.apiConnection?.success,
    verificationResults.chatServiceIntegration?.success
  ]
  
  const passedVerifications = verifications.filter(Boolean).length
  const totalVerifications = verifications.length
  
  verificationResults.summary = {
    totalVerifications,
    passedVerifications,
    failedVerifications: totalVerifications - passedVerifications,
    overallSuccess: passedVerifications === totalVerifications,
    successRate: `${Math.round((passedVerifications / totalVerifications) * 100)}%`,
    integrationStatus: passedVerifications === totalVerifications ? 'FULLY_INTEGRATED' : 'PARTIALLY_INTEGRATED'
  }
  
  console.log('=== 📋 验证摘要 ===')
  console.log(`总验证项: ${verificationResults.summary.totalVerifications}`)
  console.log(`通过验证: ${verificationResults.summary.passedVerifications}`)
  console.log(`失败验证: ${verificationResults.summary.failedVerifications}`)
  console.log(`成功率: ${verificationResults.summary.successRate}`)
  console.log(`集成状态: ${verificationResults.summary.integrationStatus}`)
  
  if (verificationResults.summary.overallSuccess) {
    console.log('🎉 所有验证通过！Targon 已完全集成到聊天界面！')
  } else {
    console.log('⚠️ 部分验证失败，请检查详细结果')
  }
  
  return verificationResults
}

/**
 * 生成集成报告
 */
export function generateIntegrationReport(verificationResults) {
  const report = {
    title: 'Targon API 聊天集成验证报告',
    timestamp: verificationResults.timestamp,
    summary: verificationResults.summary,
    details: {
      modelConfiguration: {
        status: verificationResults.modelConfiguration?.success ? 'PASS' : 'FAIL',
        targonModelsCount: verificationResults.modelConfiguration?.results?.models?.targonModelsCount || 0,
        hasTargonCategory: verificationResults.modelConfiguration?.results?.categories?.hasTargonCategory || false
      },
      keyPoolStatus: {
        status: verificationResults.keyPoolStatus?.success ? 'PASS' : 'FAIL',
        totalKeys: verificationResults.keyPoolStatus?.results?.totalKeys || 0,
        healthyKeys: verificationResults.keyPoolStatus?.results?.healthyKeys || 0,
        healthRatio: verificationResults.keyPoolStatus?.results?.healthRatio || 0
      },
      apiConnection: {
        status: verificationResults.apiConnection?.success ? 'PASS' : 'FAIL',
        connected: verificationResults.apiConnection?.results?.connected || false,
        modelsCount: verificationResults.apiConnection?.results?.modelsCount || 0
      },
      chatServiceIntegration: {
        status: verificationResults.chatServiceIntegration?.success ? 'PASS' : 'FAIL',
        testedModels: verificationResults.chatServiceIntegration?.summary?.total || 0,
        passedTests: verificationResults.chatServiceIntegration?.summary?.passed || 0
      }
    },
    recommendations: []
  }
  
  // 生成建议
  if (!verificationResults.modelConfiguration?.success) {
    report.recommendations.push('检查模型配置，确保 Targon 分类和模型正确加载')
  }
  
  if (!verificationResults.keyPoolStatus?.success) {
    report.recommendations.push('检查密钥池状态，确保有足够的健康密钥')
  }
  
  if (!verificationResults.apiConnection?.success) {
    report.recommendations.push('检查网络连接和 API 服务状态')
  }
  
  if (!verificationResults.chatServiceIntegration?.success) {
    report.recommendations.push('检查聊天服务集成，确保模型路由正确')
  }
  
  if (report.recommendations.length === 0) {
    report.recommendations.push('集成状态良好，可以正常使用 Targon 模型进行聊天')
  }
  
  return report
}

// 导出便捷的验证函数
export default {
  verifyModels: verifyModelConfiguration,
  verifyKeyPool: verifyKeyPoolStatus,
  verifyConnection: verifyApiConnection,
  verifyChat: verifyChatServiceIntegration,
  runFullVerification,
  generateReport: generateIntegrationReport
}
