<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">API密钥基础测试</h1>
      
      <!-- 配置状态 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">配置状态</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ config.totalKeys || 0 }}</div>
            <div class="text-sm text-gray-600">总密钥数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ config.activeKeys || 0 }}</div>
            <div class="text-sm text-gray-600">可用密钥</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600">{{ config.keyRotationEnabled ? '启用' : '禁用' }}</div>
            <div class="text-sm text-gray-600">密钥轮询</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ config.maxRetries || 0 }}</div>
            <div class="text-sm text-gray-600">最大重试</div>
          </div>
        </div>
      </div>

      <!-- 密钥统计 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">密钥统计</h2>
        <div v-if="keyStats.length === 0" class="text-center py-8 text-gray-500">
          暂无密钥统计信息
        </div>
        <div v-else class="space-y-3">
          <div v-for="(key, index) in keyStats" :key="index" class="flex items-center justify-between p-4 rounded-lg" :class="[
            key.isCurrent ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
          ]">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 rounded-full" :class="[
                key.isActive ? 'bg-green-500' : 'bg-red-500'
              ]"></div>
              <span class="font-medium">密钥 #{{ key.index }}</span>
              <span v-if="key.isCurrent" class="text-xs bg-blue-500 text-white px-2 py-1 rounded">当前</span>
            </div>
            <div class="text-sm text-gray-600">
              请求: {{ key.totalRequests }} | 成功率: {{ key.successRate }}%
              <span v-if="key.failureCount > 0" class="text-red-500 ml-2">
                失败: {{ key.failureCount }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 基础操作 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">基础操作</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            @click="refreshConfig"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            刷新配置
          </button>
          
          <button
            @click="rotateKey"
            :disabled="config.totalKeys <= 1"
            class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
          >
            轮换密钥
          </button>
          
          <button
            @click="resetKeys"
            class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
          >
            重置状态
          </button>
        </div>
      </div>

      <!-- 模拟测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">模拟测试</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">测试类型</label>
            <select v-model="testType" class="w-full p-3 border border-gray-300 rounded-lg">
              <option value="success">模拟成功请求</option>
              <option value="failure">模拟失败请求</option>
              <option value="timeout">模拟超时请求</option>
            </select>
          </div>
          
          <button
            @click="runSimulatedTest"
            :disabled="loading"
            class="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
          >
            {{ loading ? '测试中...' : '运行模拟测试' }}
          </button>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResults.length > 0" class="mt-6">
          <h3 class="font-semibold mb-3">测试结果</h3>
          <div class="max-h-64 overflow-y-auto space-y-2">
            <div v-for="result in testResults.slice().reverse()" :key="result.id" class="p-3 rounded-lg" :class="[
              result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            ]">
              <div class="flex justify-between items-center">
                <span class="font-medium">{{ result.type }}</span>
                <span class="text-sm text-gray-500">{{ formatTime(result.timestamp) }}</span>
              </div>
              <div class="text-sm mt-1" :class="[
                result.success ? 'text-green-700' : 'text-red-700'
              ]">
                {{ result.message }}
              </div>
            </div>
          </div>
          
          <button
            @click="clearResults"
            class="mt-3 px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空结果
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const config = ref({
  totalKeys: 0,
  activeKeys: 0,
  keyRotationEnabled: false,
  maxRetries: 0
})

const keyStats = ref([])
const testType = ref('success')
const loading = ref(false)
const testResults = ref([])

// 模拟API配置获取
const refreshConfig = () => {
  try {
    // 模拟从环境变量获取配置
    config.value = {
      totalKeys: 3,
      activeKeys: 3,
      keyRotationEnabled: true,
      maxRetries: 3,
      timeout: 60000,
      healthCheckInterval: 300000
    }
    
    // 模拟密钥统计
    keyStats.value = [
      {
        index: 1,
        isActive: true,
        isCurrent: true,
        totalRequests: 15,
        successfulRequests: 14,
        successRate: '93.3',
        failureCount: 1
      },
      {
        index: 2,
        isActive: true,
        isCurrent: false,
        totalRequests: 8,
        successfulRequests: 8,
        successRate: '100.0',
        failureCount: 0
      },
      {
        index: 3,
        isActive: true,
        isCurrent: false,
        totalRequests: 3,
        successfulRequests: 2,
        successRate: '66.7',
        failureCount: 1
      }
    ]
    
    ElMessage.success('配置已刷新')
  } catch (error) {
    ElMessage.error('刷新配置失败: ' + error.message)
  }
}

// 模拟密钥轮换
const rotateKey = () => {
  try {
    // 找到当前密钥并切换到下一个
    const currentIndex = keyStats.value.findIndex(key => key.isCurrent)
    if (currentIndex !== -1) {
      keyStats.value[currentIndex].isCurrent = false
      const nextIndex = (currentIndex + 1) % keyStats.value.length
      keyStats.value[nextIndex].isCurrent = true
      
      ElMessage.success(`已切换到密钥 #${keyStats.value[nextIndex].index}`)
    }
  } catch (error) {
    ElMessage.error('密钥轮换失败: ' + error.message)
  }
}

// 重置密钥状态
const resetKeys = () => {
  try {
    keyStats.value.forEach(key => {
      key.isActive = true
      key.failureCount = 0
    })
    ElMessage.success('密钥状态已重置')
  } catch (error) {
    ElMessage.error('重置失败: ' + error.message)
  }
}

// 运行模拟测试
const runSimulatedTest = async () => {
  loading.value = true
  
  try {
    // 模拟不同类型的测试
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const currentKey = keyStats.value.find(key => key.isCurrent)
    if (!currentKey) {
      throw new Error('没有可用的密钥')
    }
    
    let result
    
    switch (testType.value) {
      case 'success':
        currentKey.totalRequests++
        currentKey.successfulRequests++
        currentKey.successRate = ((currentKey.successfulRequests / currentKey.totalRequests) * 100).toFixed(1)
        
        result = {
          id: Date.now(),
          type: '成功测试',
          success: true,
          message: `密钥 #${currentKey.index} 请求成功`,
          timestamp: new Date()
        }
        break
        
      case 'failure':
        currentKey.totalRequests++
        currentKey.failureCount++
        currentKey.successRate = ((currentKey.successfulRequests / currentKey.totalRequests) * 100).toFixed(1)
        
        // 如果失败次数过多，切换密钥
        if (currentKey.failureCount >= 3) {
          currentKey.isActive = false
          rotateKey()
        }
        
        result = {
          id: Date.now(),
          type: '失败测试',
          success: false,
          message: `密钥 #${currentKey.index} 请求失败`,
          timestamp: new Date()
        }
        break
        
      case 'timeout':
        currentKey.totalRequests++
        currentKey.failureCount++
        currentKey.successRate = ((currentKey.successfulRequests / currentKey.totalRequests) * 100).toFixed(1)
        
        result = {
          id: Date.now(),
          type: '超时测试',
          success: false,
          message: `密钥 #${currentKey.index} 请求超时`,
          timestamp: new Date()
        }
        break
    }
    
    testResults.value.push(result)
    
    if (result.success) {
      ElMessage.success('模拟测试成功')
    } else {
      ElMessage.error('模拟测试失败')
    }
    
  } catch (error) {
    ElMessage.error('测试失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
  ElMessage.success('测试结果已清空')
}

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN')
}

// 组件挂载
onMounted(() => {
  refreshConfig()
})
</script>

<style scoped>
/* 组件样式 */
</style>
