<!--
  Targon 模型测试页面
  用于验证 Targon 模型列表和配置
-->
<template>
  <div class="targon-model-test">
    <div class="container">
      <h1 class="page-title">🎯 Targon 模型测试</h1>
      <p class="page-description">
        测试和验证 Targon 模型列表，确保 GLM-4.5-Air 和 GLM-4.5 等模型正确显示
      </p>

      <!-- 模型获取测试 -->
      <el-card class="test-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📋 模型列表测试</h3>
            <el-button @click="testGetModels" :loading="loading" type="primary">
              <el-icon><Refresh /></el-icon>
              获取模型列表
            </el-button>
          </div>
        </template>

        <div class="test-results">
          <div v-if="apiModels.length > 0" class="model-section">
            <h4>🌐 API 获取的模型 ({{ apiModels.length }} 个)</h4>
            <div class="model-grid">
              <div 
                v-for="model in apiModels" 
                :key="model.id" 
                class="model-card"
                :class="{ highlight: isGLMModel(model.id) }"
              >
                <div class="model-name">{{ model.name || model.id }}</div>
                <div class="model-id">{{ model.id }}</div>
                <div v-if="model.description" class="model-description">
                  {{ model.description }}
                </div>
              </div>
            </div>
          </div>

          <div class="model-section">
            <h4>🔧 预设模型列表 ({{ presetModels.length }} 个)</h4>
            <div class="model-grid">
              <div 
                v-for="model in presetModels" 
                :key="model.id" 
                class="model-card"
                :class="{ highlight: isGLMModel(model.id) }"
              >
                <div class="model-name">{{ model.name }}</div>
                <div class="model-id">{{ model.id }}</div>
                <div class="model-description">{{ model.description }}</div>
                <div class="model-meta">
                  <el-tag size="small">{{ model.maxTokens }} tokens</el-tag>
                  <el-tag v-if="model.supportsFunctions" type="success" size="small">
                    支持函数
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <div class="model-section">
            <h4>🎨 转换后的模型 ({{ transformedModels.length }} 个)</h4>
            <div class="model-grid">
              <div 
                v-for="model in transformedModels" 
                :key="model.id" 
                class="model-card transformed"
                :class="{ highlight: isGLMModel(model.id) }"
              >
                <div class="model-header">
                  <div class="model-name">{{ model.name }}</div>
                  <div class="model-badges">
                    <el-tag v-if="model.isNew" type="danger" size="small">新</el-tag>
                    <el-tag v-if="model.isFast" type="success" size="small">快</el-tag>
                    <el-tag v-if="model.isFree" type="info" size="small">免费</el-tag>
                  </div>
                </div>
                <div class="model-id">{{ model.id }}</div>
                <div class="model-description">{{ model.description }}</div>
                <div class="model-stats">
                  <div class="stat-item">
                    <span class="stat-label">参数:</span>
                    <span class="stat-value">{{ model.parameters }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">速度:</span>
                    <div class="rating">
                      <el-rate 
                        v-model="model.speed" 
                        disabled 
                        show-score 
                        text-color="#ff9900"
                        score-template="{value}"
                      />
                    </div>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">质量:</span>
                    <div class="rating">
                      <el-rate 
                        v-model="model.quality" 
                        disabled 
                        show-score 
                        text-color="#ff9900"
                        score-template="{value}"
                      />
                    </div>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">能力:</span>
                    <div class="capabilities">
                      <el-tag 
                        v-for="cap in model.capabilities" 
                        :key="cap" 
                        size="small" 
                        type="info"
                      >
                        {{ cap }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- GLM 模型检查 -->
      <el-card class="glm-check-card" shadow="hover">
        <template #header>
          <h3>🔍 GLM 模型检查</h3>
        </template>

        <div class="glm-check-results">
          <el-alert
            :title="glmCheckTitle"
            :type="glmCheckType"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="glm-details">
                <p><strong>GLM-4.5-Air:</strong> {{ glmModels.air ? '✅ 已找到' : '❌ 未找到' }}</p>
                <p><strong>GLM-4.5:</strong> {{ glmModels.standard ? '✅ 已找到' : '❌ 未找到' }}</p>
                <p><strong>GLM-4-Plus:</strong> {{ glmModels.plus ? '✅ 已找到' : '❌ 未找到' }}</p>
                <p><strong>总计 GLM 模型:</strong> {{ glmModelCount }} 个</p>
              </div>
            </template>
          </el-alert>
        </div>
      </el-card>

      <!-- 测试日志 -->
      <el-card class="log-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📝 测试日志</h3>
            <el-button @click="clearLogs" size="small">清除日志</el-button>
          </div>
        </template>

        <div class="logs-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            暂无日志记录
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import targonApi from '@/services/targonApi'
import { transformTargonModel } from '@/config/aiModels'

// 响应式数据
const loading = ref(false)
const apiModels = ref([])
const presetModels = ref([])
const logs = ref([])

// 计算属性
const transformedModels = computed(() => {
  return presetModels.value.map(model => transformTargonModel(model.id))
})

const glmModels = computed(() => {
  const allModels = [...apiModels.value, ...presetModels.value]
  return {
    air: allModels.some(m => m.id.includes('GLM-4.5-Air')),
    standard: allModels.some(m => m.id.includes('GLM-4.5') && !m.id.includes('Air')),
    plus: allModels.some(m => m.id.includes('GLM-4-Plus'))
  }
})

const glmModelCount = computed(() => {
  const allModels = [...apiModels.value, ...presetModels.value]
  return allModels.filter(m => m.id.includes('GLM')).length
})

const glmCheckTitle = computed(() => {
  const foundCount = Object.values(glmModels.value).filter(Boolean).length
  if (foundCount === 3) return 'GLM 模型检查通过'
  if (foundCount > 0) return 'GLM 模型部分找到'
  return 'GLM 模型未找到'
})

const glmCheckType = computed(() => {
  const foundCount = Object.values(glmModels.value).filter(Boolean).length
  if (foundCount === 3) return 'success'
  if (foundCount > 0) return 'warning'
  return 'error'
})

// 方法
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    message,
    type,
    timestamp: new Date()
  })
  
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

const formatTime = (time) => {
  return time.toLocaleTimeString()
}

const isGLMModel = (modelId) => {
  return modelId.includes('GLM')
}

const testGetModels = async () => {
  loading.value = true
  addLog('开始获取 Targon 模型列表...', 'info')
  
  try {
    // 获取预设模型
    presetModels.value = targonApi.getPresetModels()
    addLog(`获取到 ${presetModels.value.length} 个预设模型`, 'success')
    
    // 尝试从 API 获取模型
    const result = await targonApi.getModels()
    if (result.success && result.data) {
      apiModels.value = result.data
      addLog(`从 API 获取到 ${result.data.length} 个模型`, 'success')
    } else {
      apiModels.value = []
      addLog(`API 获取失败: ${result.message || '未知错误'}`, 'warning')
    }
    
    // 检查 GLM 模型
    const glmCount = glmModelCount.value
    if (glmCount > 0) {
      addLog(`找到 ${glmCount} 个 GLM 模型`, 'success')
    } else {
      addLog('未找到 GLM 模型', 'error')
    }
    
    ElMessage.success('模型列表获取完成')
    
  } catch (error) {
    addLog(`获取模型列表失败: ${error.message}`, 'error')
    ElMessage.error('获取模型列表失败')
  } finally {
    loading.value = false
  }
}

const clearLogs = () => {
  logs.value = []
}

// 生命周期
onMounted(() => {
  addLog('Targon 模型测试页面已加载', 'info')
  testGetModels()
})
</script>

<style lang="scss" scoped>
.targon-model-test {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    color: #2c3e50;
  }
}

.test-card, .glm-check-card, .log-card {
  margin-bottom: 1.5rem;
}

.model-section {
  margin-bottom: 2rem;
  
  h4 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.model-card {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  &.highlight {
    border-color: #f39c12;
    background: #fef9e7;
    
    .model-name {
      color: #e67e22;
      font-weight: 600;
    }
  }
  
  &.transformed {
    border-color: #3498db;
    background: #f8f9fa;
  }
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.model-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.model-id {
  font-family: monospace;
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.model-description {
  color: #495057;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.model-meta, .model-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.model-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.stat-label {
  font-weight: 500;
  color: #495057;
  min-width: 60px;
}

.stat-value {
  color: #212529;
  font-family: monospace;
}

.rating {
  display: flex;
  align-items: center;
}

.capabilities {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.glm-details {
  p {
    margin: 0.5rem 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 0.9rem;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.success {
    background: #d4edda;
    color: #155724;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
  }
  
  &.warning {
    background: #fff3cd;
    color: #856404;
  }
}

.log-time {
  color: #6c757d;
  min-width: 80px;
}

.no-logs {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .model-grid {
    grid-template-columns: 1fr;
  }
  
  .model-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
