<template>
  <div class="system-settings">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>配置系统参数和功能选项</p>
    </div>

    <!-- 基础设置 -->
    <div class="settings-section">
      <h2>基础设置</h2>
      <div class="settings-grid">
        <div class="setting-item">
          <label>系统名称</label>
          <input 
            type="text" 
            v-model="settings.systemName" 
            class="setting-input"
            placeholder="AI创意平台"
          />
        </div>
        
        <div class="setting-item">
          <label>系统描述</label>
          <textarea 
            v-model="settings.systemDescription" 
            class="setting-textarea"
            placeholder="基于AI的创意内容生成平台"
            rows="3"
          ></textarea>
        </div>
        
        <div class="setting-item">
          <label>默认语言</label>
          <select v-model="settings.defaultLanguage" class="setting-select">
            <option value="zh-CN">简体中文</option>
            <option value="en-US">English</option>
            <option value="ja-JP">日本語</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label>时区设置</label>
          <select v-model="settings.timezone" class="setting-select">
            <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
            <option value="America/New_York">America/New_York (UTC-5)</option>
            <option value="Europe/London">Europe/London (UTC+0)</option>
          </select>
        </div>
      </div>
    </div>

    <!-- API设置 -->
    <div class="settings-section">
      <h2>API设置</h2>
      <div class="settings-grid">
        <div class="setting-item">
          <label>API基础URL</label>
          <input 
            type="url" 
            v-model="settings.apiBaseUrl" 
            class="setting-input"
            placeholder="https://openrouter.ai/api/v1"
          />
        </div>
        
        <div class="setting-item">
          <label>请求超时时间 (秒)</label>
          <input 
            type="number" 
            v-model="settings.requestTimeout" 
            class="setting-input"
            min="10"
            max="300"
          />
        </div>
        
        <div class="setting-item">
          <label>最大重试次数</label>
          <input 
            type="number" 
            v-model="settings.maxRetries" 
            class="setting-input"
            min="0"
            max="10"
          />
        </div>
        
        <div class="setting-item">
          <label>重试延迟 (毫秒)</label>
          <input 
            type="number" 
            v-model="settings.retryDelay" 
            class="setting-input"
            min="100"
            max="10000"
            step="100"
          />
        </div>
      </div>
    </div>

    <!-- 密钥管理设置 -->
    <div class="settings-section">
      <h2>密钥管理设置</h2>
      <div class="settings-grid">
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.keyRotationEnabled"
            />
            启用密钥轮询
          </label>
          <small>自动轮换API密钥以提高可用性</small>
        </div>
        
        <div class="setting-item">
          <label>失败阈值</label>
          <input 
            type="number" 
            v-model="settings.failureThreshold" 
            class="setting-input"
            min="1"
            max="20"
          />
          <small>密钥连续失败多少次后暂时禁用</small>
        </div>
        
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.healthCheckEnabled"
            />
            启用健康检查
          </label>
          <small>定期检查密钥可用性</small>
        </div>
        
        <div class="setting-item">
          <label>健康检查间隔 (分钟)</label>
          <input 
            type="number" 
            v-model="settings.healthCheckInterval" 
            class="setting-input"
            min="1"
            max="60"
            :disabled="!settings.healthCheckEnabled"
          />
        </div>
      </div>
    </div>

    <!-- 用户设置 -->
    <div class="settings-section">
      <h2>用户设置</h2>
      <div class="settings-grid">
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.allowRegistration"
            />
            允许用户注册
          </label>
          <small>是否允许新用户注册账号</small>
        </div>
        
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.requireEmailVerification"
            />
            需要邮箱验证
          </label>
          <small>新用户注册时需要验证邮箱</small>
        </div>
        
        <div class="setting-item">
          <label>默认用户角色</label>
          <select v-model="settings.defaultUserRole" class="setting-select">
            <option value="user">普通用户</option>
            <option value="premium">高级用户</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label>会话超时时间 (小时)</label>
          <input 
            type="number" 
            v-model="settings.sessionTimeout" 
            class="setting-input"
            min="1"
            max="168"
          />
        </div>
      </div>
    </div>

    <!-- 安全设置 -->
    <div class="settings-section">
      <h2>安全设置</h2>
      <div class="settings-grid">
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.enableRateLimit"
            />
            启用请求频率限制
          </label>
          <small>限制用户API请求频率</small>
        </div>
        
        <div class="setting-item">
          <label>每分钟最大请求数</label>
          <input 
            type="number" 
            v-model="settings.rateLimitPerMinute" 
            class="setting-input"
            min="1"
            max="1000"
            :disabled="!settings.enableRateLimit"
          />
        </div>
        
        <div class="setting-item checkbox-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.enableLogging"
            />
            启用操作日志
          </label>
          <small>记录用户操作和系统事件</small>
        </div>
        
        <div class="setting-item">
          <label>日志保留天数</label>
          <input 
            type="number" 
            v-model="settings.logRetentionDays" 
            class="setting-input"
            min="1"
            max="365"
            :disabled="!settings.enableLogging"
          />
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="settings-actions">
      <button class="btn btn-primary" @click="saveSettings" :disabled="saving">
        {{ saving ? '保存中...' : '保存设置' }}
      </button>
      <button class="btn btn-info" @click="resetSettings">
        重置为默认值
      </button>
      <button class="btn btn-warning" @click="exportSettings">
        导出配置
      </button>
      <button class="btn btn-info" @click="importSettings">
        导入配置
      </button>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      style="display: none" 
      @change="handleFileImport"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const saving = ref(false)
const fileInput = ref(null)

// 设置数据
const settings = reactive({
  // 基础设置
  systemName: 'AI创意平台',
  systemDescription: '基于AI的创意内容生成平台',
  defaultLanguage: 'zh-CN',
  timezone: 'Asia/Shanghai',
  
  // API设置
  apiBaseUrl: 'https://openrouter.ai/api/v1',
  requestTimeout: 60,
  maxRetries: 3,
  retryDelay: 1000,
  
  // 密钥管理设置
  keyRotationEnabled: true,
  failureThreshold: 3,
  healthCheckEnabled: true,
  healthCheckInterval: 5,
  
  // 用户设置
  allowRegistration: true,
  requireEmailVerification: false,
  defaultUserRole: 'user',
  sessionTimeout: 24,
  
  // 安全设置
  enableRateLimit: true,
  rateLimitPerMinute: 60,
  enableLogging: true,
  logRetentionDays: 30
})

// 默认设置
const defaultSettings = { ...settings }

// 方法
const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('ai_creative_system_settings')
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings)
      Object.assign(settings, parsed)
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.warning('加载设置失败，使用默认设置')
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    localStorage.setItem('ai_creative_system_settings', JSON.stringify(settings))
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置为默认值吗？此操作不可恢复。',
      '确认重置',
      {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    Object.assign(settings, defaultSettings)
    ElMessage.success('设置已重置为默认值')
  } catch {
    // 用户取消操作
  }
}

const exportSettings = () => {
  try {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system_settings_${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('配置导出成功')
  } catch (error) {
    console.error('导出配置失败:', error)
    ElMessage.error('导出配置失败')
  }
}

const importSettings = () => {
  fileInput.value?.click()
}

const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importedSettings = JSON.parse(e.target.result)
      Object.assign(settings, importedSettings)
      ElMessage.success('配置导入成功')
    } catch (error) {
      console.error('导入配置失败:', error)
      ElMessage.error('配置文件格式错误')
    }
  }
  reader.readAsText(file)
  
  // 清空文件输入
  event.target.value = ''
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style lang="scss" scoped>
.system-settings {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.settings-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f1f3f4;
  }
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  &.checkbox-item {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;

    label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      font-weight: 500;
      color: #2c3e50;

      input[type="checkbox"] {
        margin: 0;
        transform: scale(1.2);
      }
    }

    small {
      margin-top: 0.25rem;
      color: #7f8c8d;
      font-size: 0.85rem;
      line-height: 1.4;
    }
  }

  label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
  }

  small {
    color: #7f8c8d;
    font-size: 0.8rem;
    margin-top: 0.25rem;
  }
}

.setting-input,
.setting-select,
.setting-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }

  &:disabled {
    background-color: #f5f7fa;
    color: #999;
    cursor: not-allowed;
  }
}

.setting-textarea {
  resize: vertical;
  min-height: 80px;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

/* 自定义按钮样式 */
.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
  text-align: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-info {
  background-color: #909399;
  border-color: #909399;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

.btn-warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #ebb563;
  border-color: #ebb563;
}

@media (max-width: 768px) {
  .system-settings {
    padding: 1rem;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;
    align-items: stretch;

    .btn {
      width: 100%;
    }
  }

  .setting-item.checkbox-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
