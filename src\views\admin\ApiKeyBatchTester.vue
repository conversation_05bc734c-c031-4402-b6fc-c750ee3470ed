<template>
  <div class="api-key-batch-tester">
    <div class="page-header">
      <h1>API密钥批量检测</h1>
      <p>支持多种AI服务的API密钥有效性批量检测</p>
    </div>

    <!-- 配置区域 -->
    <div class="config-section">
      <h2>检测配置</h2>
      
      <!-- API提供商选择 -->
      <div class="config-group">
        <label>API 提供商:</label>
        <div class="provider-options">
          <label class="radio-option">
            <input type="radio" v-model="config.provider" value="openai" @change="updateConfig">
            <span>OpenAI 通用</span>
          </label>
          <label class="radio-option">
            <input type="radio" v-model="config.provider" value="openrouter" @change="updateConfig">
            <span>OpenRouter</span>
          </label>
          <label class="radio-option">
            <input type="radio" v-model="config.provider" value="gemini" @change="updateConfig">
            <span>Google Gemini</span>
          </label>
        </div>
      </div>

      <!-- OpenAI/OpenRouter 配置 -->
      <div v-if="config.provider === 'openai' || config.provider === 'openrouter'" class="config-row">
        <div class="config-item">
          <label>Base URL:</label>
          <input 
            type="url" 
            v-model="config.baseUrl" 
            :placeholder="getDefaultBaseUrl()"
            class="config-input"
          />
        </div>
        <div class="config-item">
          <label>测试模型:</label>
          <input 
            type="text" 
            v-model="config.testModel" 
            :placeholder="getDefaultModel()"
            class="config-input"
          />
        </div>
      </div>

      <!-- Gemini 配置 -->
      <div v-if="config.provider === 'gemini'" class="config-row">
        <div class="config-item">
          <label>Proxy 地址 (可选):</label>
          <input 
            type="url" 
            v-model="config.geminiProxy" 
            placeholder="https://your-proxy.com"
            class="config-input"
          />
        </div>
        <div class="config-item">
          <label>Gemini 模型:</label>
          <input 
            type="text" 
            v-model="config.geminiModel" 
            placeholder="gemini-1.5-flash-8b"
            class="config-input"
          />
        </div>
      </div>

      <!-- 检测参数 -->
      <div class="config-row">
        <div class="config-item">
          <label>并发请求数:</label>
          <input 
            type="number" 
            v-model="config.concurrency" 
            min="1" 
            max="20"
            class="config-input"
          />
        </div>
        <div class="config-item">
          <label>请求超时 (秒):</label>
          <input 
            type="number" 
            v-model="config.timeout" 
            min="5" 
            max="60"
            class="config-input"
          />
        </div>
      </div>
    </div>

    <!-- API密钥输入区域 -->
    <div class="input-section">
      <h2>API密钥输入</h2>
      <div class="input-controls">
        <button class="btn btn-info" @click="importFromFile">
          📁 导入文件
        </button>
        <button class="btn btn-warning" @click="clearInput">
          🗑️ 清空输入
        </button>
      </div>
      <textarea 
        v-model="apiKeysInput"
        :placeholder="getInputPlaceholder()"
        class="keys-textarea"
        rows="8"
      ></textarea>
      <div class="input-stats">
        <span>总计: {{ totalKeys }} 个密钥</span>
        <span v-if="duplicateKeys > 0">重复: {{ duplicateKeys }} 个</span>
        <span>有效: {{ uniqueKeys }} 个</span>
      </div>
    </div>

    <!-- 检测控制 -->
    <div class="test-controls">
      <button 
        class="btn btn-primary btn-large"
        @click="startBatchTest"
        :disabled="testing || !canStartTest"
      >
        {{ testing ? `检测中... (${progress.current}/${progress.total})` : '开始批量检测' }}
      </button>
      <button 
        v-if="testing"
        class="btn btn-danger"
        @click="stopTest"
      >
        停止检测
      </button>
    </div>

    <!-- 检测进度 -->
    <div v-if="testing || results.length > 0" class="progress-section">
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
      <div class="progress-text">
        {{ progress.current }} / {{ progress.total }} 
        ({{ progressPercentage }}%)
      </div>
    </div>

    <!-- 检测结果 -->
    <div v-if="results.length > 0" class="results-section">
      <h2>检测结果</h2>
      
      <!-- 结果统计 -->
      <div class="result-stats">
        <div class="stat-card valid">
          <div class="stat-number">{{ validResults.length }}</div>
          <div class="stat-label">有效密钥</div>
        </div>
        <div class="stat-card invalid">
          <div class="stat-number">{{ invalidResults.length }}</div>
          <div class="stat-label">无效密钥</div>
        </div>
        <div class="stat-card rate-limit">
          <div class="stat-number">{{ rateLimitResults.length }}</div>
          <div class="stat-label">限流密钥</div>
        </div>
        <div class="stat-card error">
          <div class="stat-number">{{ errorResults.length }}</div>
          <div class="stat-label">错误密钥</div>
        </div>
      </div>

      <!-- 结果详情 -->
      <div class="result-details">
        <!-- 有效密钥 -->
        <div v-if="validResults.length > 0" class="result-group">
          <div class="result-header">
            <h3>✅ 有效密钥 ({{ validResults.length }})</h3>
            <div class="result-actions">
              <button class="btn btn-info btn-small" @click="copyResults('valid')">
                复制
              </button>
              <button class="btn btn-primary btn-small" @click="exportResults('valid')">
                导出
              </button>
            </div>
          </div>
          <div class="result-list">
            <div v-for="result in validResults" :key="result.token" class="result-item valid">
              <span class="result-token">{{ maskToken(result.token) }}</span>
              <span class="result-message">{{ result.message }}</span>
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
            </div>
          </div>
        </div>

        <!-- 限流密钥 -->
        <div v-if="rateLimitResults.length > 0" class="result-group">
          <div class="result-header">
            <h3>⚠️ 限流密钥 ({{ rateLimitResults.length }})</h3>
            <div class="result-actions">
              <button class="btn btn-info btn-small" @click="copyResults('rateLimit')">
                复制
              </button>
            </div>
          </div>
          <div class="result-list">
            <div v-for="result in rateLimitResults" :key="result.token" class="result-item rate-limit">
              <span class="result-token">{{ maskToken(result.token) }}</span>
              <span class="result-message">{{ result.message }}</span>
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
            </div>
          </div>
        </div>

        <!-- 无效密钥 -->
        <div v-if="invalidResults.length > 0" class="result-group">
          <div class="result-header">
            <h3>❌ 无效密钥 ({{ invalidResults.length }})</h3>
            <button class="btn btn-info btn-small" @click="toggleInvalidDetails">
              {{ showInvalidDetails ? '隐藏详情' : '显示详情' }}
            </button>
          </div>
          <div v-if="showInvalidDetails" class="result-list">
            <div v-for="result in invalidResults" :key="result.token" class="result-item invalid">
              <span class="result-token">{{ maskToken(result.token) }}</span>
              <span class="result-message">{{ result.message }}</span>
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".txt,.json" 
      style="display: none" 
      @change="handleFileImport"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { celebrateApiTestCompletion } from '@/utils/celebration'

// 响应式数据
const testing = ref(false)
const apiKeysInput = ref('')
const results = ref([])
const showInvalidDetails = ref(false)
const fileInput = ref(null)

// 配置
const config = reactive({
  provider: 'openai',
  baseUrl: '',
  testModel: '',
  geminiProxy: '',
  geminiModel: 'gemini-1.5-flash-8b',
  concurrency: 5,
  timeout: 30
})

// 进度
const progress = reactive({
  current: 0,
  total: 0
})

// 提供商配置
const PROVIDERS = {
  openai: {
    name: 'OpenAI 通用',
    defaultBaseUrl: 'https://api.openai.com/v1',
    defaultModel: 'gpt-4o-mini',
    placeholder: '请输入 OpenAI API KEY，多个以英文逗号、分号或换行分隔\n\n示例格式：\nsk-xxx1,sk-xxx2\nsk-xxx3;sk-xxx4\nsk-xxx5'
  },
  openrouter: {
    name: 'OpenRouter',
    defaultBaseUrl: 'https://openrouter.ai/api/v1',
    defaultModel: 'openai/gpt-4o-mini',
    placeholder: '请输入 OpenRouter API KEY，多个以英文逗号、分号或换行分隔\n\n示例格式：\nsk-or-xxx1,sk-or-xxx2\nsk-or-xxx3;sk-or-xxx4\nsk-or-xxx5'
  },
  gemini: {
    name: 'Google Gemini',
    defaultBaseUrl: 'https://generativelanguage.googleapis.com',
    defaultModel: 'gemini-1.5-flash-8b',
    placeholder: '请输入 Google Gemini API KEY，多个以英文逗号、分号或换行分隔\n\n示例格式：\nAIzaSyXXX1,AIzaSyXXX2\nAIzaSyXXX3;AIzaSyXXX4\nAIzaSyXXX5'
  }
}

// 计算属性
const totalKeys = computed(() => {
  if (!apiKeysInput.value.trim()) return 0
  return parseApiKeys(apiKeysInput.value).raw.length
})

const duplicateKeys = computed(() => {
  if (!apiKeysInput.value.trim()) return 0
  const parsed = parseApiKeys(apiKeysInput.value)
  return parsed.raw.length - parsed.unique.length
})

const uniqueKeys = computed(() => {
  if (!apiKeysInput.value.trim()) return 0
  return parseApiKeys(apiKeysInput.value).unique.length
})

const canStartTest = computed(() => {
  return uniqueKeys.value > 0 && !testing.value
})

const progressPercentage = computed(() => {
  if (progress.total === 0) return 0
  return Math.round((progress.current / progress.total) * 100)
})

const validResults = computed(() => results.value.filter(r => r.isValid))
const invalidResults = computed(() => results.value.filter(r => !r.isValid && !r.isRateLimit && !r.isError))
const rateLimitResults = computed(() => results.value.filter(r => r.isRateLimit))
const errorResults = computed(() => results.value.filter(r => r.isError))

// 方法
const getDefaultBaseUrl = () => PROVIDERS[config.provider]?.defaultBaseUrl || ''
const getDefaultModel = () => PROVIDERS[config.provider]?.defaultModel || ''
const getInputPlaceholder = () => PROVIDERS[config.provider]?.placeholder || ''

const updateConfig = () => {
  const provider = PROVIDERS[config.provider]
  if (provider) {
    config.baseUrl = provider.defaultBaseUrl
    config.testModel = provider.defaultModel
  }
}

const parseApiKeys = (input) => {
  const raw = input
    .split(new RegExp('[,;\\s\\n\\r]+'))
    .map(key => key.trim())
    .filter(key => key !== '')

  const seen = new Set()
  const unique = []
  const duplicates = []

  raw.forEach(key => {
    if (seen.has(key)) {
      duplicates.push(key)
    } else {
      seen.add(key)
      unique.push(key)
    }
  })

  return { raw, unique, duplicates }
}

const importFromFile = () => {
  fileInput.value?.click()
}

const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return

  if (!file.name.toLowerCase().match(/\.(txt|json)$/)) {
    ElMessage.warning('请选择 .txt 或 .json 格式的文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      let content = e.target.result

      // 如果是JSON文件，尝试解析
      if (file.name.toLowerCase().endsWith('.json')) {
        const jsonData = JSON.parse(content)
        if (Array.isArray(jsonData)) {
          content = jsonData.join('\n')
        } else if (jsonData.keys && Array.isArray(jsonData.keys)) {
          content = jsonData.keys.join('\n')
        } else {
          throw new Error('JSON格式不正确')
        }
      }

      // 处理内容
      const cleanContent = content
        .split(new RegExp('[,\\n\\r]+'))
        .map(line => line.trim())
        .filter(line => line !== '')
        .join('\n')

      if (apiKeysInput.value.trim()) {
        apiKeysInput.value += '\n' + cleanContent
      } else {
        apiKeysInput.value = cleanContent
      }

      ElMessage.success(`文件导入成功！共导入 ${cleanContent.split('\n').length} 个密钥`)
    } catch (error) {
      ElMessage.error('文件解析失败：' + error.message)
    }
  }

  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }

  reader.readAsText(file)
  event.target.value = ''
}

const clearInput = () => {
  apiKeysInput.value = ''
  results.value = []
  progress.current = 0
  progress.total = 0
}

const maskToken = (token) => {
  if (token.length <= 8) return token
  return token.substring(0, 4) + '***' + token.substring(token.length - 4)
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// API检测函数
const checkOpenAIToken = async (token) => {
  try {
    const baseUrl = config.baseUrl || PROVIDERS.openai.defaultBaseUrl
    const testModel = config.testModel || PROVIDERS.openai.defaultModel
    const apiUrl = baseUrl.endsWith('/') ? baseUrl + 'chat/completions' : baseUrl + '/chat/completions'

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout * 1000)

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        model: testModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 1
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (response.ok) {
      return {
        token,
        isValid: true,
        message: '有效',
        timestamp: Date.now()
      }
    } else {
      const errorData = await response.json().catch(() => null)
      let message = '无效'
      let isRateLimit = false

      if (response.status === 429) {
        message = '429 - 请求过于频繁'
        isRateLimit = true
      } else if (response.status === 401) {
        message = '401 - 认证失败'
      } else if (response.status === 403) {
        message = '403 - 权限不足'
      } else if (errorData?.error?.message) {
        message = errorData.error.message
      }

      return {
        token,
        isValid: false,
        isRateLimit,
        message,
        timestamp: Date.now()
      }
    }
  } catch (error) {
    return {
      token,
      isValid: false,
      isError: true,
      message: '网络错误: ' + error.message,
      timestamp: Date.now()
    }
  }
}

const checkGeminiToken = async (token) => {
  try {
    const baseUrl = config.geminiProxy || PROVIDERS.gemini.defaultBaseUrl
    const model = config.geminiModel || PROVIDERS.gemini.defaultModel
    const apiUrl = baseUrl.endsWith('/')
      ? `${baseUrl}v1beta/models/${model}:generateContent?key=${token}`
      : `${baseUrl}/v1beta/models/${model}:generateContent?key=${token}`

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout * 1000)

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: 'Hello' }] }],
        generationConfig: { maxOutputTokens: 1 }
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (response.ok) {
      return {
        token,
        isValid: true,
        message: '有效',
        timestamp: Date.now()
      }
    } else {
      const errorData = await response.json().catch(() => null)
      let message = '无效'
      let isRateLimit = false

      if (response.status === 429) {
        message = '429 - 请求过于频繁'
        isRateLimit = true
      } else if (response.status === 400) {
        message = '400 - API KEY无效'
      } else if (response.status === 403) {
        message = '403 - API KEY被禁用或权限不足'
      } else if (errorData?.error?.message) {
        message = errorData.error.message
      }

      return {
        token,
        isValid: false,
        isRateLimit,
        message,
        timestamp: Date.now()
      }
    }
  } catch (error) {
    return {
      token,
      isValid: false,
      isError: true,
      message: '网络错误: ' + error.message,
      timestamp: Date.now()
    }
  }
}

// 并发控制函数
const runWithConcurrencyLimit = async (tasks, concurrency, onResult) => {
  return new Promise((resolve) => {
    let i = 0
    let running = 0
    const results = []

    function runNext() {
      while (running < concurrency && i < tasks.length) {
        const currentIndex = i++
        const task = tasks[currentIndex]
        running++

        task()
          .then((result) => {
            results[currentIndex] = result
            onResult(result, currentIndex)
          })
          .catch((err) => {
            const errorResult = {
              token: 'unknown',
              isValid: false,
              isError: true,
              message: err.message,
              timestamp: Date.now()
            }
            results[currentIndex] = errorResult
            onResult(errorResult, currentIndex)
          })
          .finally(() => {
            running--
            if (i === tasks.length && running === 0) {
              resolve(results)
            } else {
              runNext()
            }
          })
      }
    }

    runNext()
  })
}

// 批量测试主函数
const startBatchTest = async () => {
  if (!canStartTest.value) return

  const parsed = parseApiKeys(apiKeysInput.value)
  if (parsed.unique.length === 0) {
    ElMessage.warning('请输入至少一个有效的API密钥')
    return
  }

  // 验证配置
  if (config.provider === 'gemini' && config.geminiProxy) {
    const urlPattern = /^https?:\/\/.+/i
    if (!urlPattern.test(config.geminiProxy)) {
      ElMessage.error('Proxy地址格式无效！必须以 http:// 或 https:// 开头')
      return
    }
  }

  testing.value = true
  results.value = []
  progress.current = 0
  progress.total = parsed.unique.length

  // 构建检测任务
  const tasks = parsed.unique.map(token => () => {
    switch (config.provider) {
      case 'openai':
      case 'openrouter':
        return checkOpenAIToken(token)
      case 'gemini':
        return checkGeminiToken(token)
      default:
        return Promise.resolve({
          token,
          isValid: false,
          isError: true,
          message: '未知提供商',
          timestamp: Date.now()
        })
    }
  })

  try {
    await runWithConcurrencyLimit(
      tasks,
      config.concurrency,
      (result) => {
        results.value.push(result)
        progress.current++
      }
    )

    // 检测完成
    const validCount = validResults.value.length
    const totalCount = results.value.length
    const successRate = Math.round((validCount / totalCount) * 100)

    // 触发庆祝动画
    celebrateApiTestCompletion(validCount, totalCount, 'API密钥')

    ElMessage.success(
      `检测完成！总计: ${totalCount}, 有效: ${validCount}, 成功率: ${successRate}%`
    )
  } catch (error) {
    ElMessage.error('批量检测失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const stopTest = () => {
  testing.value = false
  ElMessage.info('检测已停止')
}

const toggleInvalidDetails = () => {
  showInvalidDetails.value = !showInvalidDetails.value
}

const copyResults = async (type) => {
  let targetResults = []
  let typeName = ''

  switch (type) {
    case 'valid':
      targetResults = validResults.value
      typeName = '有效密钥'
      break
    case 'rateLimit':
      targetResults = rateLimitResults.value
      typeName = '限流密钥'
      break
    default:
      return
  }

  if (targetResults.length === 0) {
    ElMessage.warning(`没有可复制的${typeName}`)
    return
  }

  const tokens = targetResults.map(r => r.token).join('\n')

  try {
    await navigator.clipboard.writeText(tokens)
    ElMessage.success(`${typeName}已复制到剪贴板 (${targetResults.length} 个)`)
  } catch (error) {
    // 降级到传统方法
    const textArea = document.createElement('textarea')
    textArea.value = tokens
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success(`${typeName}已复制到剪贴板 (${targetResults.length} 个)`)
  }
}

const exportResults = (type) => {
  let targetResults = []
  let fileName = ''

  switch (type) {
    case 'valid':
      targetResults = validResults.value
      fileName = 'valid_api_keys'
      break
    default:
      return
  }

  if (targetResults.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  const exportData = {
    provider: config.provider,
    timestamp: new Date().toISOString(),
    total: targetResults.length,
    keys: targetResults.map(r => ({
      token: r.token,
      message: r.message,
      timestamp: r.timestamp
    }))
  }

  const dataStr = JSON.stringify(exportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${fileName}_${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)

  ElMessage.success('数据导出成功')
}



// 生命周期
onMounted(() => {
  updateConfig()
})
</script>

<style lang="scss" scoped>
.api-key-batch-tester {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.config-section,
.input-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f1f3f4;
  }
}

.config-group {
  margin-bottom: 1.5rem;

  > label {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }
}

.provider-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    border-color: #409eff;
    background: #f0f8ff;
  }

  input[type="radio"] {
    margin: 0;
  }

  input[type="radio"]:checked + span {
    color: #409eff;
    font-weight: 600;
  }

  span {
    font-size: 0.9rem;
    color: #2c3e50;
  }
}

.config-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
  }
}

.config-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
}

.input-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.keys-textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  resize: vertical;
  min-height: 200px;

  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
}

.input-stats {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #7f8c8d;

  span {
    padding: 0.25rem 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
  }
}

.test-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.btn-small {
    padding: 6px 12px;
    font-size: 12px;
  }

  &.btn-large {
    padding: 12px 24px;
    font-size: 16px;
    min-width: 200px;
  }
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;

  &:hover:not(:disabled) {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}

.btn-info {
  background-color: #909399;
  border-color: #909399;
  color: white;

  &:hover:not(:disabled) {
    background-color: #a6a9ad;
    border-color: #a6a9ad;
  }
}

.btn-warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;

  &:hover:not(:disabled) {
    background-color: #ebb563;
    border-color: #ebb563;
  }
}

.btn-danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;

  &:hover:not(:disabled) {
    background-color: #f78989;
    border-color: #f78989;
  }
}

.progress-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e8ed;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #66b1ff);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.results-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f1f3f4;
  }
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  padding: 1rem;
  border-radius: 8px;
  text-align: center;

  &.valid {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    border: 1px solid #c3e6cb;
  }

  &.invalid {
    background: linear-gradient(135deg, #ffebee, #f8d7da);
    border: 1px solid #f5c6cb;
  }

  &.rate-limit {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
  }

  &.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f1b0b7;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
  }
}

.result-details {
  .result-group {
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
  }
}

.result-actions {
  display: flex;
  gap: 0.5rem;
}

.result-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f3f4;

  &:last-child {
    border-bottom: none;
  }

  &.valid {
    background: #f8fff8;
    border-left: 4px solid #28a745;
  }

  &.invalid {
    background: #fff8f8;
    border-left: 4px solid #dc3545;
  }

  &.rate-limit {
    background: #fffbf0;
    border-left: 4px solid #ffc107;
  }

  .result-token {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
    min-width: 120px;
  }

  .result-message {
    flex: 1;
    color: #7f8c8d;
    font-size: 0.9rem;
  }

  .result-time {
    color: #adb5bd;
    font-size: 0.8rem;
    min-width: 80px;
    text-align: right;
  }
}

@media (max-width: 768px) {
  .api-key-batch-tester {
    padding: 1rem;
  }

  .config-row {
    grid-template-columns: 1fr;
  }

  .provider-options {
    flex-direction: column;
  }

  .input-controls {
    flex-direction: column;
  }

  .test-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .result-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .result-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;

    .result-time {
      text-align: left;
    }
  }
}

@media (max-width: 480px) {
  .result-stats {
    grid-template-columns: 1fr;
  }

  .input-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* 撒花动画样式 */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
  overflow: hidden;
}

.confetti-piece {
  position: absolute;
  top: -50px;
  animation: confetti-fall linear infinite;
  user-select: none;
  pointer-events: none;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* 为不同的confetti添加随机效果 */
.confetti-piece:nth-child(odd) {
  animation-timing-function: ease-in-out;
}

.confetti-piece:nth-child(even) {
  animation-timing-function: ease-in;
}

.confetti-piece:nth-child(3n) {
  animation-duration: 3s !important;
}

.confetti-piece:nth-child(4n) {
  animation-duration: 2.5s !important;
}

.confetti-piece:nth-child(5n) {
  animation-duration: 4s !important;
}

/* 庆祝弹窗样式 */
:global(.celebration-modal) {
  .el-message-box {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  .el-message-box__header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px;
  }

  .el-message-box__title {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
  }

  .el-message-box__content {
    padding: 20px;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-line;
  }

  .el-message-box__btns {
    padding: 10px 20px 20px;
  }

  .el-button--primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8, #6a4190);
    }
  }
}

/* 成功率高时的特殊效果 */
:global(.celebration-modal.high-success) {
  .el-message-box__header {
    background: linear-gradient(135deg, #4CAF50, #45a049);
  }

  .el-button--primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);

    &:hover {
      background: linear-gradient(135deg, #45a049, #3d8b40);
    }
  }
}
</style>
