// GeminiPool API 连接测试工具
import { generateChatCompletion } from '@/services/pollinationsApi.js'

// 测试 GeminiPool API 连接
export async function testGeminiPoolConnection() {
  console.log('🧪 开始测试 GeminiPool API 连接...')
  
  const testCases = [
    {
      name: 'Gemini 2.5 Flash 基础测试',
      model: 'gemini-2.5-flash',
      message: '你好，请简单回复"连接成功"'
    },
    {
      name: 'Gemini 2.5 Flash Lite 测试',
      model: 'gemini-2.5-flash-lite',
      message: '请用一句话介绍你自己'
    },
    {
      name: 'Gemini 2.5 Pro 测试',
      model: 'gemini-2.5-pro',
      message: '请解释什么是人工智能'
    }
  ]

  const results = []

  for (const testCase of testCases) {
    console.log(`\n📋 测试: ${testCase.name}`)
    console.log(`🤖 模型: ${testCase.model}`)
    console.log(`💬 消息: ${testCase.message}`)
    
    try {
      const startTime = Date.now()
      
      const result = await generateChatCompletion([
        { role: 'user', content: testCase.message }
      ], {
        model: testCase.model,
        temperature: 0.7,
        max_tokens: 100
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      if (result.success) {
        console.log(`✅ 成功! 响应时间: ${duration}ms`)
        console.log(`📝 回复: ${result.content}`)
        console.log(`📊 Token 使用: ${JSON.stringify(result.usage)}`)
        
        results.push({
          ...testCase,
          success: true,
          response: result.content,
          duration,
          usage: result.usage
        })
      } else {
        console.log(`❌ 失败: ${result.error}`)
        results.push({
          ...testCase,
          success: false,
          error: result.error,
          duration
        })
      }
    } catch (error) {
      console.log(`💥 异常: ${error.message}`)
      results.push({
        ...testCase,
        success: false,
        error: error.message,
        duration: 0
      })
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  console.log('\n📊 测试结果汇总:')
  console.log('='.repeat(50))
  
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`)
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`)
  
  if (successCount > 0) {
    const avgDuration = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.duration, 0) / successCount
    console.log(`⏱️  平均响应时间: ${avgDuration.toFixed(0)}ms`)
  }

  console.log('\n📋 详细结果:')
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.name}`)
    console.log(`   模型: ${result.model}`)
    console.log(`   状态: ${result.success ? '✅ 成功' : '❌ 失败'}`)
    if (result.success) {
      console.log(`   响应: ${result.response}`)
      console.log(`   时间: ${result.duration}ms`)
    } else {
      console.log(`   错误: ${result.error}`)
    }
  })

  return {
    success: successCount > 0,
    successRate: successCount / totalCount,
    results
  }
}

// 测试特定模型
export async function testSpecificModel(modelId, message = '你好') {
  console.log(`🧪 测试模型: ${modelId}`)
  
  try {
    const result = await generateChatCompletion([
      { role: 'user', content: message }
    ], {
      model: modelId,
      temperature: 0.7,
      max_tokens: 50
    })
    
    if (result.success) {
      console.log(`✅ ${modelId} 测试成功`)
      console.log(`📝 回复: ${result.content}`)
      return { success: true, response: result.content }
    } else {
      console.log(`❌ ${modelId} 测试失败: ${result.error}`)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.log(`💥 ${modelId} 测试异常: ${error.message}`)
    return { success: false, error: error.message }
  }
}

// 获取可用模型列表 (仅保留 2.0 和 2.5 系列)
export function getAvailableGeminiModels() {
  return [
    // Gemini 2.5 系列 (最新推荐)
    'gemini-2.5-flash-preview-05-20',
    'gemini-2.5-flash',
    'gemini-2.5-flash-lite-preview-06-17',
    'gemini-2.5-pro',
    'gemini-2.5-flash-lite',

    // Gemini 2.0 系列
    'gemini-2.0-flash-exp',
    'gemini-2.0-flash-thinking-exp-1219',

    // 实验版本
    'gemini-exp-1114',
    'gemini-exp-1121',
    'gemini-exp-1206'
  ]
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testGeminiPool = testGeminiPoolConnection
  window.testGeminiModel = testSpecificModel
  window.getGeminiModels = getAvailableGeminiModels
  
  console.log('🔧 GeminiPool 测试工具已加载!')
  console.log('📝 可用命令:')
  console.log('  - testGeminiPool(): 运行完整测试')
  console.log('  - testGeminiModel(modelId, message): 测试特定模型')
  console.log('  - getGeminiModels(): 获取可用模型列表')
}
