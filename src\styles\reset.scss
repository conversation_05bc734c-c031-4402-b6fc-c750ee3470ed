// CSS Reset
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

// HTML5 display-role reset for older browsers
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 额外的重置样式
a {
  text-decoration: none;
  color: inherit;
}

button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  outline: none;
}

input, textarea, select {
  outline: none;
  border: none;
  background: none;
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 移除默认的focus样式
*:focus {
  outline: none;
}

// 移除iOS默认样式
input[type="button"],
input[type="submit"],
input[type="reset"],
button {
  -webkit-appearance: none;
  border-radius: 0;
}

input[type="search"] {
  -webkit-appearance: none;
  border-radius: 0;
}

// 移除Android默认样式
input, textarea {
  -webkit-user-select: auto;
}

// 禁用选择
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
