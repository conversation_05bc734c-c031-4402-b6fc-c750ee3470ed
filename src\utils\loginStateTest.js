/**
 * 登录状态测试工具
 * 用于测试登录状态的持久化和恢复功能
 */

import { getToken, setToken, removeToken, isTokenExpired } from '@/utils/auth'
import { APP_CONFIG } from '@/config'
import * as loginStateManager from '@/utils/loginStateManager'

/**
 * 创建测试用户数据
 */
function createTestUserData() {
  const timestamp = Date.now()
  const expirationTime = timestamp + 24 * 60 * 60 * 1000 // 24小时后过期
  
  return {
    token: `mock_token_${timestamp}_${Math.random().toString(36).substr(2, 9)}_exp_${expirationTime}`,
    userInfo: {
      id: 'test_user_' + timestamp,
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      avatar: '',
      createdAt: new Date().toISOString()
    }
  }
}

/**
 * 测试登录状态保存
 */
export function testSaveLoginState() {
  console.log('🧪 测试登录状态保存...')
  
  try {
    const testData = createTestUserData()
    
    // 保存登录状态
    const saveResult = loginStateManager.saveLoginState(
      testData.token,
      testData.userInfo,
      { rememberMe: true }
    )
    
    if (saveResult) {
      console.log('✅ 登录状态保存测试通过')
      return { success: true, data: testData }
    } else {
      console.log('❌ 登录状态保存测试失败')
      return { success: false, error: '保存失败' }
    }
  } catch (error) {
    console.error('❌ 登录状态保存测试异常:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试登录状态恢复
 */
export function testRestoreLoginState() {
  console.log('🧪 测试登录状态恢复...')
  
  try {
    // 恢复登录状态
    const restoredState = loginStateManager.restoreLoginState()
    
    if (restoredState) {
      console.log('✅ 登录状态恢复测试通过:', restoredState.userInfo.username)
      return { success: true, data: restoredState }
    } else {
      console.log('ℹ️ 没有找到可恢复的登录状态')
      return { success: false, error: '没有可恢复的状态' }
    }
  } catch (error) {
    console.error('❌ 登录状态恢复测试异常:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试登录状态验证
 */
export function testValidateLoginState() {
  console.log('🧪 测试登录状态验证...')
  
  try {
    const isValid = loginStateManager.isLoginStateValid()
    
    console.log('登录状态验证结果:', isValid)
    return { success: true, isValid }
  } catch (error) {
    console.error('❌ 登录状态验证测试异常:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试登录状态清除
 */
export function testClearLoginState() {
  console.log('🧪 测试登录状态清除...')
  
  try {
    const clearResult = loginStateManager.clearLoginState()
    
    if (clearResult) {
      console.log('✅ 登录状态清除测试通过')
      return { success: true }
    } else {
      console.log('❌ 登录状态清除测试失败')
      return { success: false, error: '清除失败' }
    }
  } catch (error) {
    console.error('❌ 登录状态清除测试异常:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试页面刷新后的状态恢复
 */
export function testPageRefreshRestore() {
  console.log('🧪 测试页面刷新后的状态恢复...')
  
  try {
    // 模拟页面刷新后的初始化
    const restoredState = loginStateManager.initializeLoginStateOnPageLoad()
    
    if (restoredState) {
      console.log('✅ 页面刷新状态恢复测试通过:', restoredState.userInfo.username)
      return { success: true, data: restoredState }
    } else {
      console.log('ℹ️ 页面刷新后没有找到可恢复的登录状态')
      return { success: false, error: '没有可恢复的状态' }
    }
  } catch (error) {
    console.error('❌ 页面刷新状态恢复测试异常:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行完整的登录状态测试套件
 */
export function runLoginStateTestSuite() {
  console.log('🚀 开始运行登录状态测试套件...')
  
  const results = {
    save: null,
    restore: null,
    validate: null,
    pageRefresh: null,
    clear: null,
    overall: false
  }
  
  try {
    // 1. 测试保存
    results.save = testSaveLoginState()
    
    // 2. 测试恢复
    if (results.save.success) {
      results.restore = testRestoreLoginState()
    }
    
    // 3. 测试验证
    results.validate = testValidateLoginState()
    
    // 4. 测试页面刷新恢复
    results.pageRefresh = testPageRefreshRestore()
    
    // 5. 测试清除
    results.clear = testClearLoginState()
    
    // 判断整体测试结果
    results.overall = results.save.success && 
                     results.restore.success && 
                     results.validate.success &&
                     results.clear.success
    
    console.log('📊 登录状态测试套件完成:', results)
    return results
    
  } catch (error) {
    console.error('❌ 登录状态测试套件异常:', error)
    results.overall = false
    return results
  }
}

// 在开发环境下，将测试函数挂载到 window 对象上，方便在控制台调用
if (import.meta.env.DEV && typeof window !== 'undefined') {
  window.loginStateTest = {
    testSaveLoginState,
    testRestoreLoginState,
    testValidateLoginState,
    testClearLoginState,
    testPageRefreshRestore,
    runLoginStateTestSuite
  }
  
  console.log('🔧 登录状态测试工具已挂载到 window.loginStateTest')
}

export default {
  testSaveLoginState,
  testRestoreLoginState,
  testValidateLoginState,
  testClearLoginState,
  testPageRefreshRestore,
  runLoginStateTestSuite
}
