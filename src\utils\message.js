// 高对比度消息提示工具函数
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'

/**
 * 显示高对比度成功提示
 * @param {string} message - 提示消息
 * @param {Object} options - 额外选项
 */
export function showSuccessMessage(message, options = {}) {
  return ElMessage({
    message,
    type: 'success',
    duration: 3000,
    showClose: true,
    customClass: 'high-contrast-message',
    offset: 0,
    ...options
  })
}

/**
 * 显示高对比度警告提示
 * @param {string} message - 提示消息
 * @param {Object} options - 额外选项
 */
export function showWarningMessage(message, options = {}) {
  return ElMessage({
    message,
    type: 'warning',
    duration: 4000,
    showClose: true,
    customClass: 'high-contrast-message',
    offset: 0,
    ...options
  })
}

/**
 * 显示高对比度错误提示
 * @param {string} message - 提示消息
 * @param {Object} options - 额外选项
 */
export function showErrorMessage(message, options = {}) {
  return ElMessage({
    message,
    type: 'error',
    duration: 4000,
    showClose: true,
    customClass: 'high-contrast-message',
    offset: 0,
    ...options
  })
}

/**
 * 显示高对比度信息提示
 * @param {string} message - 提示消息
 * @param {Object} options - 额外选项
 */
export function showInfoMessage(message, options = {}) {
  return ElMessage({
    message,
    type: 'info',
    duration: 4000,
    showClose: true,
    customClass: 'high-contrast-message',
    offset: 0,
    ...options
  })
}

/**
 * 显示登录相关的警告提示
 * @param {string} message - 提示消息，默认为"请先登录后再访问此页面"
 */
export function showLoginRequiredMessage(message = '请先登录后再访问此页面') {
  return showWarningMessage(message, {
    duration: 5000,
    offset: 100
  })
}

/**
 * 显示权限不足提示
 * @param {string} message - 提示消息，默认为"您没有权限访问该资源"
 */
export function showPermissionDeniedMessage(message = '您没有权限访问该资源') {
  return showErrorMessage(message, {
    duration: 5000
  })
}

/**
 * 显示网络错误提示
 * @param {string} message - 提示消息，默认为"网络连接异常，请检查网络后重试"
 */
export function showNetworkErrorMessage(message = '网络连接异常，请检查网络后重试') {
  return showErrorMessage(message, {
    duration: 6000
  })
}

/**
 * 显示服务器错误提示
 * @param {string} message - 提示消息，默认为"服务器暂时无法响应，请稍后重试"
 */
export function showServerErrorMessage(message = '服务器暂时无法响应，请稍后重试') {
  return showErrorMessage(message, {
    duration: 6000
  })
}

/**
 * 显示操作成功提示
 * @param {string} action - 操作名称，如"登录"、"保存"等
 * @param {Object} options - 额外选项
 */
export function showActionSuccessMessage(action, options = {}) {
  return showSuccessMessage(`${action}成功！`, options)
}

/**
 * 显示操作失败提示
 * @param {string} action - 操作名称，如"登录"、"保存"等
 * @param {string} reason - 失败原因，可选
 * @param {Object} options - 额外选项
 */
export function showActionFailedMessage(action, reason = '', options = {}) {
  const message = reason ? `${action}失败：${reason}` : `${action}失败，请重试`
  return showErrorMessage(message, options)
}

/**
 * 显示高对比度确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @param {Object} options - 额外选项
 */
export function showConfirmDialog(message, title = '确认操作', options = {}) {
  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'high-contrast-dialog',
    center: true,
    ...options
  })
}

/**
 * 显示高对比度通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知内容
 * @param {string} type - 通知类型
 * @param {Object} options - 额外选项
 */
export function showNotification(title, message, type = 'info', options = {}) {
  return ElNotification({
    title,
    message,
    type,
    duration: 4500,
    position: 'top-right',
    showClose: true,
    ...options
  })
}

// 导出常用的消息类型
export const MessageTypes = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  INFO: 'info'
}

// 导出默认配置
export const MessageConfig = {
  SUCCESS_DURATION: 3000,
  WARNING_DURATION: 4000,
  ERROR_DURATION: 4000,
  INFO_DURATION: 4000,
  LONG_DURATION: 6000
}
