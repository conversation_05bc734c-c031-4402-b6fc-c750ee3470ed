<div align="center">

# 🎨 AI创作助手平台

![Vue.js](https://img.shields.io/badge/Vue.js-3.x-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white)
![Vite](https://img.shields.io/badge/Vite-5.x-646CFF?style=for-the-badge&logo=vite&logoColor=white)
![Element Plus](https://img.shields.io/badge/Element_Plus-409EFF?style=for-the-badge&logo=element&logoColor=white)
![Pinia](https://img.shields.io/badge/Pinia-FFD859?style=for-the-badge&logo=pinia&logoColor=black)

**技术栈：** `Vue 3` • `Vite 5.x` • `Element Plus` • `Pinia` • `Vue Router` • `Axios`

**🌟 释放创意无限可能，让AI成为您的创作伙伴 🌟**

*集AI聊天、AI绘画、内容创作和社交分享于一体的综合性创作平台*

[🚀 快速开始](#-快速开始) • [✨ 功能特色](#-主要功能) • [📖 文档](#-项目结构) • [🤝 贡献](#-贡献指南)

</div>

---

## 🎯 项目简介

AI创作助手平台是一个**现代化**的Web应用程序，采用最新的前端技术栈构建。平台致力于为用户提供从**创意构思**到**作品完成**的一站式服务体验，集成了先进的AI技术，支持智能对话、图像生成、作品分享等功能。

<!-- 平台预览图 -->
<div align="center">
  <img src="./public/logo-banner.svg" alt="AI创作助手平台" width="100%" style="max-width: 800px; border-radius: 10px;">
</div>

<!-- 如果上方图片无法显示，请查看下方文字版本 -->
<div align="center">

```
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║                    🎨 AI创作助手平台                                   ║
║                 释放创意无限可能，让AI成为您的创作伙伴                    ║
║                                                                      ║
║    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐             ║
║    │  💬 AI聊天   │    │  🎨 AI绘画   │    │  🖼️ 作品展示  │             ║
║    │             │    │             │    │             │             ║
║    │  智能对话    │    │  创意生成    │    │  社交分享    │             ║
║    │  获得灵感    │    │  艺术作品    │    │  互动交流    │             ║
║    └─────────────┘    └─────────────┘    └─────────────┘             ║
║                                                                      ║
║              🚀 现代化 • 📱 响应式 • ⚡ 高性能                          ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
```

### 🚀 现代化 • 📱 响应式 • ⚡ 高性能

| 💬 **AI智能聊天** | 🎨 **AI图像生成** | 🖼️ **作品展示** |
|:---:|:---:|:---:|
| 与AI对话获得创意灵感 | 文字描述生成艺术作品 | 分享作品社交互动 |

</div>

## ✨ 主要功能

### 🤖 多模态 AI 支持
- **智能对话** - 25+ 最新 AI 模型 (OpenAI、Llama、Mistral、DeepSeek、Gemini 等)
- **图像生成** - Pollinations.AI 无水印专业图像生成
- **音频处理** - 文本转语音、音频生成、语音转文本
- **多模态理解** - 支持文本、图像、音频的输入和输出

### 🌟 先进模型集成
- **Pollinations.AI** - 25+ 免费 AI 模型，无需 API 密钥
- **OpenRouter** - 100+ 最新 AI 模型，统一接口调用
- **GeminiPool** - 最新 Gemini 2.0/2.5 系列模型
- **OpenAI 兼容** - 标准化 API 接口
- **多提供商** - OpenAI、Google、Meta、Microsoft、DeepSeek 等

### 🎛️ 智能功能
- **模型选择器** - 智能分类和推荐系统
- **性能监控** - 实时响应速度和质量评估
- **批量处理** - 支持批量图像生成和文本处理
- **历史记录** - 完整的创作历史管理
- **API密钥管理** - 多平台API密钥统一管理和监控

### 🎨 创作工具
- **AI 绘画** - 多种尺寸和风格的图像生成
- **音频创作** - 6种语音选择，音乐和音效生成
- **文本创作** - 创意写作、代码生成、专业分析
- **作品分享** - 社交互动和作品展示

### 🛠️ 管理功能
- **用户管理** - 完整的用户权限和角色管理
- **系统监控** - 炫酷的实时系统状态监控
- **API密钥管理** - 批量测试、状态监控、使用统计
- **系统设置** - 灵活的系统配置和参数调整
- **日志管理** - 详细的操作日志和错误追踪

### 💻 技术特性
- **响应式设计** - 完美适配桌面端、平板和移动设备
- **主题切换** - 支持明暗主题切换
- **PWA支持** - 支持离线使用和应用安装
- **实时更新** - 支持流式响应和实时交互
- **移动端优化** - 专门的移动端样式和交互优化

## 🛠️ 技术栈

### 前端技术
- **Vue 3** - 渐进式JavaScript框架 (Composition API)
- **Vite 5.x** - 下一代前端构建工具
- **Element Plus** - Vue 3 UI组件库
- **Pinia** - Vue 状态管理库
- **Vue Router** - Vue 官方路由管理器
- **Axios** - HTTP客户端
- **SCSS** - CSS预处理器
- **Day.js** - 轻量级日期处理库
- **Marked** - Markdown解析器
- **Highlight.js** - 代码高亮库

### AI 服务集成
- **Pollinations.AI** - 免费多模态 AI 服务
- **OpenRouter** - 100+ AI 模型统一接口
- **GeminiPool** - Google Gemini 模型服务
- **OpenAI 兼容接口** - 标准化 API 调用
- **音频处理** - 文本转语音和音频生成
- **图像生成** - 专业级图像创作

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Unplugin Auto Import** - 自动导入插件
- **Unplugin Vue Components** - 组件自动注册

## 📁 项目结构

```
ai-creative-platform/
├── public/                 # 静态资源
│   ├── favicon.ico        # 网站图标
│   └── manifest.json      # PWA配置
├── src/
│   ├── components/        # 可复用组件
│   │   ├── common/        # 通用组件
│   │   ├── admin/         # 管理后台组件
│   │   ├── chat/          # 聊天相关组件
│   │   ├── image/         # 图像处理组件
│   │   └── audio/         # 音频处理组件
│   ├── views/             # 页面视图
│   │   ├── home/          # 首页
│   │   ├── chat/          # 聊天页面
│   │   ├── drawing/       # 绘画页面
│   │   ├── gallery/       # 作品展示页面
│   │   ├── admin/         # 管理后台页面
│   │   ├── auth/          # 用户认证页面
│   │   └── test/          # 功能测试页面
│   ├── services/          # AI 服务集成
│   │   ├── pollinationsApi.js      # Pollinations API (25+ 模型)
│   │   ├── openrouterApi.js        # OpenRouter API (100+ 模型)
│   │   ├── imageGenerationService.js # 图像生成统一服务
│   │   └── audioService.js         # 音频处理服务
│   ├── config/            # 配置文件
│   │   └── aiModels.js    # AI 模型配置和元数据
│   ├── utils/             # 工具函数
│   │   ├── geminiPoolTest.js # GeminiPool 测试工具
│   │   ├── auth.js        # 认证工具
│   │   ├── common.js      # 通用工具
│   │   └── request.js     # HTTP请求封装
│   ├── stores/            # 状态管理 (Pinia)
│   │   ├── app.js         # 应用状态
│   │   ├── user.js        # 用户状态
│   │   ├── chat.js        # 聊天状态
│   │   └── drawing.js     # 绘画状态
│   ├── styles/            # 样式文件
│   │   ├── index.scss     # 主样式文件
│   │   ├── variables.scss # 变量定义
│   │   ├── common.scss    # 通用样式
│   │   └── mobile.scss    # 移动端样式
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
├── docs/                  # 项目文档
│   ├── POLLINATIONS_COMPLETE_INTEGRATION.md # 完整集成文档
│   ├── GEMINI_2.5_MODELS.md                # Gemini 模型说明
│   └── GEMINI_CLEANUP_SUMMARY.md           # 模型清理总结
├── index.html             # HTML模板
├── vite.config.js         # Vite配置
├── jsconfig.json          # JavaScript配置
└── package.json           # 项目配置
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
```

### 代码格式化
```bash
npm run format
```

## 🎨 设计特色

### 响应式设计
- 移动优先的设计理念
- 完美适配各种屏幕尺寸
- 触摸友好的交互体验

### 主题系统
- 支持明暗主题切换
- 可自定义主题色彩
- 一致的设计语言

### 性能优化
- 组件懒加载
- 图片懒加载
- 代码分割
- PWA支持

## 🎨 支持的 AI 模型

### Pollinations.AI 模型 (免费，无需 API 密钥)
- **OpenAI 系列**: GPT-4.1-nano, GPT-4.1 mini, o4-mini, GPT-4o-audio-preview
- **Meta/Llama 系列**: Llama 3.3 70B, Llama 4 Scout 17B, Llama 3.2 11B Vision
- **Mistral 系列**: Mistral Small 3, Unity Mistral Large (无审查)
- **DeepSeek 系列**: DeepSeek-V3, DeepSeek-R1 推理模型
- **专业模型**: Qwen 2.5 Coder 32B, Phi-4 Instruct, Gemini 2.5 Flash
- **创意模型**: Rtist (创意写作), Evil (无审查), SearchGPT (搜索增强)
- **音频模型**: Midijourney (音乐), Hypnosis Tracy (心理学)
- **图像模型**: Flux (稳定扩散), Turbo (快速生成)

### OpenRouter 模型 (需要 API 密钥)
- **OpenAI 系列**: GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo
- **Anthropic 系列**: Claude-3.5-sonnet, Claude-3-opus, Claude-3-haiku
- **Google 系列**: Gemini-pro, Gemini-pro-vision, PaLM-2
- **Meta 系列**: Llama-3.1-405b, Llama-3.1-70b, Llama-3.1-8b
- **Mistral 系列**: Mistral-large, Mistral-medium, Mistral-small
- **其他模型**: Cohere Command, Perplexity, Together AI 等

### GeminiPool 模型 (需要 API 密钥)
- **Gemini 2.5 系列**: Flash, Flash Lite, Pro (最新推荐)
- **Gemini 2.0 系列**: 实验版本和思维链推理
- **实验版本**: 各种前沿功能测试

## 🔧 配置说明

### API 配置
项目使用多个 AI 服务，主要配置在 `src/services/` 目录下：

```javascript
// Pollinations.AI (免费，无需 API 密钥)
const API_CONFIG = {
  baseUrl: 'https://text.pollinations.ai',
  imageUrl: 'https://image.pollinations.ai',
  audioUrl: 'https://audio.pollinations.ai'
}

// OpenRouter (需要 API 密钥)
const OPENROUTER_CONFIG = {
  baseUrl: 'https://openrouter.ai/api/v1',
  apiKey: 'your-openrouter-api-key'
}

// GeminiPool (需要 API 密钥)
const GEMINI_CONFIG = {
  baseUrl: 'https://apiv2.aliyahzombie.top/v1beta/openai',
  apiKey: 'your-gemini-api-key'
}
```

### 环境变量
创建 `.env` 文件配置环境变量：

```env
# API基础URL
VITE_API_BASE_URL=http://localhost:8080/api

# 是否启用Mock数据
VITE_ENABLE_MOCK=false

# 应用标题
VITE_APP_TITLE=AI创作助手平台

# OpenRouter API 密钥 (可选)
VITE_OPENROUTER_API_KEY=your-openrouter-api-key

# Gemini API 密钥 (可选)
VITE_GEMINI_API_KEY=your-gemini-api-key
```

## 📱 移动端支持

### 🎨 完美移动端体验
- **响应式布局** - 自适应各种屏幕尺寸
- **触摸优化** - 44px最小触摸目标，防止误触
- **手势支持** - 滑动、长按等触摸手势
- **PWA应用** - 支持安装到桌面，离线使用
- **安全区域** - 适配iPhone刘海屏和安全区域
- **虚拟键盘** - 智能避让虚拟键盘遮挡
- **性能优化** - 移动端专用动画和交互优化

### 📱 移动端特色功能
- **抽屉式侧边栏** - 聊天页面移动端友好的侧边栏
- **全屏画布** - 绘画页面移动端全屏体验
- **瀑布流布局** - 作品展示页面单列瀑布流
- **紧凑表格** - 管理页面移动端优化显示
- **触摸友好** - 所有按钮和控件都针对触摸优化

## 📋 开发规范

### 代码风格
- 使用 ESLint 进行代码质量检查
- 使用 Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 最佳实践
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main` - 主分支，用于生产环境
- `develop` - 开发分支，用于集成测试
- `feature/*` - 功能分支
- `hotfix/*` - 热修复分支

## 🚀 使用示例

### AI 对话
```javascript
import { generateChatCompletion } from '@/services/pollinationsApi.js'

// 使用 Pollinations.AI 模型
const result = await generateChatCompletion([
  { role: 'user', content: '你好，请介绍一下自己' }
], {
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  max_tokens: 1000
})

console.log('AI 回复:', result.content)
```

### 图像生成
```javascript
import imageGenerationService from '@/services/imageGenerationService.js'

// 生成图像
const imageResult = await imageGenerationService.generateImage('一只可爱的小猫', {
  width: 1024,
  height: 1024,
  quality: 'high',
  nologo: true
})

console.log('图像URL:', imageResult.imageUrl)
```

### 音频处理
```javascript
import audioService from '@/services/audioService.js'

// 文本转语音
const audioResult = await audioService.textToSpeech('你好世界', {
  voice: 'alloy',
  model: 'openai-audio'
})

// 播放音频
audioService.playAudio(audioResult.audioUrl)
```

## 🧪 功能测试

访问 `/test` 页面进行功能测试：
- **Pollinations API 测试** - 测试各种 AI 模型
- **OpenRouter API 测试** - 测试100+ AI模型
- **音频功能测试** - 文本转语音、音频生成
- **GeminiPool API 测试** - Gemini 模型测试
- **图像生成测试** - 各种参数和风格测试
- **移动端测试** - 专门的移动端样式测试页面

## ❓ 常见问题

### Q: 如何配置开发环境？
A: 确保安装了 Node.js 16+ 和 npm 8+，然后运行 `npm install` 安装依赖。

### Q: 哪些功能是免费的？
A: Pollinations.AI 的所有功能都是免费的，包括 25+ AI 模型、图像生成和音频处理。OpenRouter 和 GeminiPool 需要 API 密钥。

### Q: 如何添加新的 AI 模型？
A: 在对应的服务文件中添加模型 ID，在 `src/config/aiModels.js` 中配置模型信息。

### Q: 管理后台有哪些功能？
A: 包括用户管理、API密钥管理、系统监控、系统设置、日志管理等完整的后台管理功能。

### Q: 如何自定义主题？
A: 修改 `src/styles/variables.scss` 文件中的CSS变量。

### Q: 支持哪些音频格式？
A: 支持 MP3、WAV、OGG、M4A、FLAC 等常见音频格式。

## 📊 性能指标

### 响应速度 (平均)
- **快速模型**: 1-3秒 (Llama Scout, Turbo, Gemini Flash Lite)
- **标准模型**: 3-8秒 (Llama, Mistral, Gemini Flash)
- **大型模型**: 8-15秒 (OpenAI Large, Gemini Pro)
- **推理模型**: 10-20秒 (DeepSeek Reasoning, OpenAI o4-mini)

### 质量评分 (5星制)
- **5星模型**: OpenAI Large, Gemini 2.5 Pro, DeepSeek Reasoning Large
- **4星模型**: OpenAI, Llama, Mistral, Gemini 2.5 Flash
- **3星模型**: Llama Scout, Phi, Hormoz

### 功能支持
- **多模态**: 15+ 模型支持文本+图像输入
- **音频处理**: 6种语音选择，支持多种音频格式
- **代码生成**: 专业的编程助手模型
- **推理能力**: 专门的逻辑推理和分析模型

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证

## 🙏 致谢

### AI 服务提供商
- [Pollinations.AI](https://pollinations.ai) - 免费多模态 AI 服务平台
- [GeminiPool](https://apiv2.aliyahzombie.top) - Google Gemini 模型服务
- [OpenAI](https://openai.com) - 先进的 AI 模型技术
- [Google AI](https://ai.google) - Gemini 系列模型
- [Meta AI](https://ai.meta.com) - Llama 开源模型
- [Mistral AI](https://mistral.ai) - 高性能开源模型

### 前端技术栈
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3 UI组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理库
- [Vue Router](https://router.vuejs.org/) - Vue 官方路由
- [Axios](https://axios-http.com/) - HTTP客户端
- [Day.js](https://day.js.org/) - 日期处理库
- [Marked](https://marked.js.org/) - Markdown解析器
- [Highlight.js](https://highlightjs.org/) - 代码高亮

---

## 📚 相关文档

### 🚀 功能文档
- [移动端优化完整指南](./MOBILE_OPTIMIZATION.md)
- [管理后台系统指南](./ADMIN_SYSTEM.md)

### 🔧 技术文档
- [Pollinations.AI 完整集成指南](./POLLINATIONS_COMPLETE_INTEGRATION.md)
- [Gemini 2.0/2.5 系列模型说明](./GEMINI_2.5_MODELS.md)
- [快速开始指南](./QUICK_START.md)
- [故障排除指南](./TROUBLESHOOTING.md)

---

**🚀 AI 创作平台** - 集成 125+ 最新 AI 模型，释放创意无限可能！✨

**🌟 特色功能**: 免费使用 | 多模态支持 | 最新模型 | 专业品质 | 移动端优化 | 管理后台

**📱 移动端**: 完美适配 | 触摸优化 | PWA支持 | 离线使用

**🛠️ 管理功能**: 用户管理 | API密钥管理 | 系统监控 | 日志管理
