<template>
  <div class="model-selector">
    <!-- 模型选择器触发按钮 -->
    <div class="selector-trigger" @click="toggleSelector">
      <div class="current-model">
        <ModelAvatar
          :model-name="currentModel.name"
          :status="currentModel.status"
          size="medium"
        />
        <div class="model-info">
          <div class="model-name">{{ currentModel.name }}</div>
          <div class="model-desc">{{ currentModel.description }}</div>
        </div>
      </div>
      <div class="selector-arrow" :class="{ 'open': showSelector }">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>

    <!-- 背景遮罩 -->
    <transition name="overlay-fade">
      <div v-if="showSelector" class="overlay" @click="closeSelector"></div>
    </transition>

    <!-- 模型选择器面板 -->
    <transition name="selector-fade">
      <div v-if="showSelector" class="selector-panel mobile-optimized touch-optimized">
        <div class="panel-header safe-area-top">
          <div class="header-top">
            <h3>选择AI模型</h3>
            <button @click="showSelector = false" class="close-btn mobile-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
          <div class="search-box">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
            </svg>
            <input
              v-model="searchQuery"
              placeholder="搜索模型名称、描述或功能..."
              class="search-input mobile-input"
              :class="{ 'searching': searchQuery !== debouncedSearchQuery }"
            />
            <button v-if="searchQuery" @click="searchQuery = ''" class="clear-search mobile-btn">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="panel-content">
          <!-- 分类标签 - 可折叠 -->
          <div class="category-tabs collapsible">
            <div class="tabs-header" @click="toggleCategories">
              <div class="tabs-title">
                <span class="tabs-icon">🏷️</span>
                <span>AI模型分类</span>
                <span class="active-category-info" v-if="activeCategory !== 'all'">
                  - {{ categories.find(c => c.id === activeCategory)?.name }}
                </span>
              </div>
              <div class="tabs-toggle" :class="{ 'expanded': showCategories }">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>

            <transition name="categories-slide">
              <div v-show="showCategories" class="tabs-content">
                <div class="tabs-container">
                  <button
                    v-for="category in categories"
                    :key="category.id"
                    @click="selectCategory(category.id)"
                    class="category-tab"
                    :class="{ 'active': activeCategory === category.id }"
                  >
                    <span class="tab-icon">{{ category.icon }}</span>
                    <span class="tab-name">{{ category.name }}</span>
                    <span class="tab-count">{{ getCategoryCount(category.id) }}</span>
                  </button>
                </div>
              </div>
            </transition>
          </div>

          <!-- 模型列表 -->
          <div class="models-container" @scroll="handleScroll">
            <!-- 加载状态 -->
            <div v-if="modelsLoading" class="loading-container">
              <div class="loading-spinner"></div>
              <p>正在加载模型列表...</p>
            </div>

            <!-- 空状态 -->
            <div v-else-if="filteredModels.length === 0" class="empty-container">
              <div class="empty-icon">🔍</div>
              <h4>未找到匹配的模型</h4>
              <p>尝试调整搜索条件或选择其他分类</p>
            </div>

            <!-- 调试信息 -->
            <div v-if="!modelsLoading && filteredModels.length === 0" class="debug-info" style="padding: 1rem; background: #f3f4f6; margin: 1rem; border-radius: 8px;">
              <p>调试信息:</p>
              <p>总模型数: {{ aiModels.length }}</p>
              <p>当前分类: {{ activeCategory }}</p>
              <p>搜索关键词: "{{ debouncedSearchQuery }}"</p>
              <p>筛选后模型数: {{ filteredModels.length }}</p>
            </div>

            <!-- 模型网格 - 优化布局 -->
            <div v-else class="models-grid mobile-scroll enhanced-grid" :class="{ 'scrolling': isScrolling }">
              <div
                v-for="(model, index) in filteredModels"
                :key="model.id"
                @click="selectModel(model)"
                class="model-card mobile-card touch-optimized enhanced-card"
                :class="{
                  'selected': selectedModelId === model.id,
                  'premium': model.isPremium,
                  'offline': model.status === 'offline',
                  'free': model.isFree,
                  'new': model.isNew
                }"
                :style="{ '--index': index }"
              >
                <!-- 卡片背景装饰 -->
                <div class="card-decoration"></div>

                <div class="model-header">
                  <ModelAvatar
                    :model-name="model.name"
                    :status="model.status"
                    size="xlarge"
                  />
                  <div class="model-badges">
                    <span v-if="model.isFree" class="badge free">免费</span>
                    <span v-if="model.isPremium" class="badge premium">Pro</span>
                    <span v-if="model.isNew" class="badge new">New</span>
                    <span v-if="model.isFast" class="badge fast">Fast</span>
                  </div>
                </div>

                <div class="model-content">
                  <h4 class="model-title">{{ model.name }}</h4>
                  <p class="model-description">{{ model.description }}</p>

                  <div class="model-specs">
                    <div class="spec-row">
                      <div class="spec-item">
                        <span class="spec-label">参数:</span>
                        <span class="spec-value">{{ model.parameters }}</span>
                      </div>
                      <div class="spec-item">
                        <span class="spec-label">速度:</span>
                        <div class="speed-indicator">
                          <div class="speed-bar">
                            <div class="speed-fill" :style="{ width: model.speed + '%' }"></div>
                          </div>
                          <span class="speed-text">{{ model.speed }}%</span>
                        </div>
                      </div>
                    </div>
                    <div class="spec-row">
                      <div class="spec-item">
                        <span class="spec-label">质量:</span>
                        <div class="quality-stars">
                          <span v-for="i in 5" :key="i" class="star" :class="{ 'filled': i <= model.quality }">★</span>
                        </div>
                      </div>
                      <div class="spec-item">
                        <span class="spec-label">上下文:</span>
                        <span class="spec-value">{{ model.contextLength }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="model-capabilities">
                    <div class="capabilities-list">
                      <span v-for="capability in model.capabilities.slice(0, 3)" :key="capability" class="capability-tag">
                        {{ capability }}
                      </span>
                      <span v-if="model.capabilities.length > 3" class="capability-more">
                        +{{ model.capabilities.length - 3 }}
                      </span>
                    </div>
                  </div>

                  <div class="model-footer">
                    <div class="pricing">
                      <span class="price-label">价格:</span>
                      <span class="price-value" :class="{ 'free': model.isFree }">{{ model.pricing }}</span>
                    </div>
                    <div class="usage-count">
                      <span class="usage-text">{{ formatUsageCount(model.usageCount) }} 次使用</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel-footer">
          <div class="footer-info">
            <span class="model-count">共 {{ filteredModels.length }} 个模型</span>
            <span v-if="searchQuery" class="search-result">搜索 "{{ searchQuery }}"</span>
          </div>
          <div class="footer-actions safe-area-bottom">
            <button @click="showSelector = false" class="btn-cancel mobile-btn">取消</button>
            <button
              @click="applySelection"
              class="btn-apply mobile-btn"
              :disabled="!selectedModelId || selectedModelId === props.modelValue"
            >
              确定选择
            </button>
          </div>
        </div>
      </div>
    </transition>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ModelAvatar from './ModelAvatarSimple.vue'
import { getAIModels, AI_MODEL_CATEGORIES } from '@/config/aiModels.js'

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: 'gemini-2.5-flash'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'model-changed'])

// 响应式数据
const showSelector = ref(false)
const searchQuery = ref('')
const debouncedSearchQuery = ref('')
const activeCategory = ref('all')
const selectedModelId = ref(props.modelValue)
const modelsLoading = ref(false)
const isScrolling = ref(false)
const showCategories = ref(true) // 分类标签默认展开

// 分类配置
const categories = ref(AI_MODEL_CATEGORIES)

// AI模型配置
const aiModels = ref([])

// 防抖搜索
const debouncedSearch = debounce((query) => {
  debouncedSearchQuery.value = query
}, 300)

// 监听搜索输入
watch(searchQuery, (newQuery) => {
  debouncedSearch(newQuery)
})

// 计算属性
const currentModel = computed(() => {
  const found = aiModels.value.find(model => model.id === selectedModelId.value)
  if (found) return found

  // 如果没找到，返回第一个模型或默认模型
  if (aiModels.value.length > 0) {
    return aiModels.value[0]
  }

  // 如果没有模型，返回默认模型结构
  return {
    id: 'openai',
    name: 'OpenAI GPT-4o Mini',
    description: '快速高效的对话模型',
    category: 'openai',
    status: 'online',
    speed: 95,
    quality: 4,
    capabilities: ['对话', '图像理解'],
    isPremium: false,
    isFree: true,
    pricing: '免费'
  }
})

const filteredModels = computed(() => {
  let models = aiModels.value

  // 按分类筛选
  if (activeCategory.value !== 'all') {
    models = models.filter(model => model.category === activeCategory.value)
  }

  // 按搜索关键词筛选 - 使用防抖搜索
  if (debouncedSearchQuery.value) {
    const query = debouncedSearchQuery.value.toLowerCase()
    models = models.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.description.toLowerCase().includes(query) ||
      model.capabilities.some(cap => cap.toLowerCase().includes(query))
    )
  }

  return models
})

// 方法
const toggleSelector = () => {
  showSelector.value = !showSelector.value
}

const selectModel = (model) => {
  selectedModelId.value = model.id
}

// 切换分类标签显示/隐藏
const toggleCategories = () => {
  showCategories.value = !showCategories.value
}

// 选择分类并自动折叠
const selectCategory = (categoryId) => {
  activeCategory.value = categoryId
  // 在移动端选择分类后自动折叠
  if (window.innerWidth <= 768) {
    showCategories.value = false
  }
}

// 格式化使用次数
const formatUsageCount = (count) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'k'
  }
  return count.toString()
}

const applySelection = () => {
  emit('update:modelValue', selectedModelId.value)
  emit('model-changed', currentModel.value)
  showSelector.value = false
  if (currentModel.value && currentModel.value.name) {
    ElMessage.success(`已切换到 ${currentModel.value.name}`)
  }
}

const closeSelector = () => {
  showSelector.value = false
}



// 滚动处理 - 性能优化
let scrollTimeout
const handleScroll = () => {
  isScrolling.value = true
  clearTimeout(scrollTimeout)
  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
  }, 150)
}

const getCategoryCount = (categoryId) => {
  if (categoryId === 'all') return aiModels.value.length
  return aiModels.value.filter(model => model.category === categoryId).length
}

// 加载模型列表
const loadModels = async () => {
  if (modelsLoading.value) return

  try {
    modelsLoading.value = true
    const models = await getAIModels()
    aiModels.value = models
    console.log('已加载模型:', models.length, '个')
  } catch (error) {
    console.error('加载模型失败:', error)
    ElMessage.error('加载模型列表失败，请刷新页面重试')
  } finally {
    modelsLoading.value = false
  }
}

// 生命周期
onMounted(async () => {
  selectedModelId.value = props.modelValue
  await loadModels()
})
</script>

<style lang="scss" scoped>
.model-selector {
  position: relative;
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.875rem 1rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.current-model {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  flex: 1;

  // 确保当前模型头像显示为纯SVG图标 - 使用更强的选择器
  :deep(.model-avatar) {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;

    * {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }

    .avatar-content,
    .model-icon,
    svg {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
}

.model-info {
  flex: 1;

  .model-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    line-height: 1.3;
    margin-bottom: 2px;
  }

  .model-desc {
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.selector-arrow {
  transition: transform 0.3s ease;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;

  &.open {
    transform: rotate(180deg);
    color: #3b82f6;
  }
}

.selector-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 98vw;
  max-width: 2000px;
  height: 95vh;
  max-height: 1200px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  z-index: 9999; // 提高z-index确保在最前面
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);

  // 移动端优化
  &.mobile-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-overflow-scrolling: touch;
    touch-action: manipulation;
  }

  // 触摸优化
  &.touch-optimized {
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      touch-action: manipulation;
    }

    button, .model-card, .category-tab {
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none;
    }
  }
}

.panel-header {
  padding: 2rem 2.5rem 1.5rem;
  border-bottom: 2px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  flex-shrink: 0;

  .header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;

    h3 {
      margin: 0;
      font-size: 1.75rem;
      font-weight: 800;
      color: #1f2937;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -0.025em;
    }

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      background: rgba(107, 114, 128, 0.1);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #6b7280;

      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        transform: scale(1.05);
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;

  .search-icon {
    position: absolute;
    left: 1.25rem;
    color: #9ca3af;
    z-index: 1;
    width: 20px;
    height: 20px;
  }

  .search-input {
    width: 100%;
    padding: 1.125rem 3.5rem 1.125rem 3.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
      font-size: 0.95rem;
    }

    &.searching {
      opacity: 0.8;
      border-color: #3b82f6;

      &::after {
        content: '';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        border: 2px solid #3b82f6;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .clear-search {
    position: absolute;
    right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border: none;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;

    &:hover {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
      transform: scale(1.05);
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

.category-tabs {
  padding: 0;
  border-bottom: none;
  flex-shrink: 0;
  margin: 1rem 2rem;

  &.collapsible {
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #cbd5e1;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }
}

  .tabs-container {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding-bottom: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
    }
  }
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 1rem 1.5rem;
  border: 2px solid transparent;
  background: #f8fafc;
  cursor: pointer;
  border-radius: 16px;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-weight: 600;
  color: #64748b;
  min-width: fit-content;

  &:hover {
    background: #e2e8f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
  }

  .tab-icon {
    font-size: 1.125rem;
  }

  .tab-name {
    font-size: 0.95rem;
    font-weight: 600;
  }

  .tab-count {
    background: rgba(255, 255, 255, 0.25);
    color: inherit;
    padding: 0.375rem 0.625rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 700;
    min-width: 24px;
    text-align: center;
    line-height: 1;
  }

  &:not(.active) .tab-count {
    background: #e2e8f0;
    color: #64748b;
  }
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.models-container {
  flex: 1;
  overflow-y: auto;
  padding: 2.5rem 3rem;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;

    &:hover {
      background: #94a3b8;
    }
  }
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
  gap: 2rem;
  max-width: none;
  padding: 0 1rem;

  // 性能优化
  &.scrolling {
    .model-card {
      pointer-events: none;

      &:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.empty-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h4 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }
}

.loading-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: #6b7280;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.model-card {
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
  min-height: 320px;
  height: auto;
  display: flex;
  flex-direction: column;
  will-change: transform;
  transform: translateZ(0); // 启用硬件加速

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.2);
    transform: translateY(-4px);

    &::before {
      opacity: 1;
    }
  }

  &.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.25);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &.offline {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  &.premium {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-color: #f59e0b;

    &:hover {
      box-shadow: 0 12px 35px rgba(245, 158, 11, 0.2);
    }
  }

  // 强制所有模型卡片中的头像显示为纯SVG图标
  :deep(.model-avatar) {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;

    &, * {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      border-radius: 0 !important;
    }

    // 确保SVG图标在模型选择器中显示更大
    .model-icon {
      width: 100% !important;
      height: 100% !important;

      svg {
        width: 100% !important;
        height: 100% !important;
        min-width: 64px !important;
        min-height: 64px !important;
      }
    }
  }
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  flex-shrink: 0;

  // 确保模型头像显示为纯SVG图标 - 使用更强的选择器
  :deep(.model-avatar) {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;

    * {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }

    .avatar-content,
    .model-icon,
    svg {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
}

.model-badges {
  display: flex;
  gap: 0.375rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.625rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.premium {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #d97706;
    border: 1px solid #f59e0b;
  }

  &.new {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #16a34a;
    border: 1px solid #22c55e;
  }

  &.fast {
    background: linear-gradient(135deg, #ddd6fe, #c4b5fd);
    color: #7c3aed;
    border: 1px solid #8b5cf6;
  }
}

.model-content {
  flex: 1;
}

.model-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.model-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 1.25rem 0;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.model-specs {
  margin-bottom: 1.25rem;
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.spec-item {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  flex: 1;

  &:not(:last-child) {
    margin-right: 1.25rem;
  }

  .spec-label {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .spec-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
  }
}

.speed-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.speed-bar {
  width: 50px;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;

  .speed-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
    border-radius: 3px;
  }
}

.speed-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #3b82f6;
}

.quality-stars {
  display: flex;
  gap: 1px;

  .star {
    color: #e5e7eb;
    font-size: 0.875rem;
    transition: color 0.2s ease;

    &.filled {
      color: #fbbf24;
    }
  }
}

.model-capabilities {
  margin-bottom: 1rem;

  .capabilities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
  }
}

.capability-tag {
  padding: 0.25rem 0.625rem;
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  color: #0369a1;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #0ea5e9;
}

.capability-more {
  padding: 0.25rem 0.625rem;
  background: #f1f5f9;
  color: #64748b;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #cbd5e1;
}

.model-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  margin-top: auto;
}

.pricing {
  display: flex;
  align-items: center;
  gap: 0.375rem;

  .price-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
  }

  .price-value {
    font-size: 0.8rem;
    font-weight: 600;
    color: #f59e0b;

    &.free {
      color: #10b981;
    }
  }
}

.usage-count {
  .usage-text {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
  }
}

.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  border-top: 2px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  flex-shrink: 0;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .model-count {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 600;
  }

  .search-result {
    font-size: 0.75rem;
    color: #6b7280;
  }
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
}

.btn-cancel, .btn-apply {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-cancel {
  background: white;
  border-color: #e5e7eb;
  color: #6b7280;

  &:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
  }
}

.btn-apply {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  &:disabled {
    background: #e5e7eb;
    border-color: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
  }

  &:not(:disabled) {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-color: #3b82f6;
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
}

.selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 999;
  backdrop-filter: blur(2px);
}

// 背景遮罩
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998; // 确保在弹窗下方
  backdrop-filter: blur(4px);
  -webkit-tap-highlight-color: transparent;
}

.overlay-fade-enter-active,
.overlay-fade-leave-active {
  transition: all 0.3s ease;
}

.overlay-fade-enter-from,
.overlay-fade-leave-to {
  opacity: 0;
}

.selector-fade-enter-active,
.selector-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.selector-fade-enter-from,
.selector-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.models-grid .model-card {
  /* 移除动画延迟，确保立即可见 */
  animation: fadeIn 0.2s ease forwards;
  opacity: 1; /* 确保模型卡片可见 */
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .selector-panel {
    width: 97vw;
    max-width: 1600px;
  }

  .models-grid {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 1024px) {
  .selector-panel {
    width: 98vw;
    max-width: 1200px;
  }

  .models-grid {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .selector-panel {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    top: 0;
    left: 0;
    transform: none;
    border: none;

    // 安全区域适配
    &.safe-area-top {
      padding-top: env(safe-area-inset-top);
    }
  }

  .models-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 0 1rem;
    display: grid !important; // 强制显示网格
    visibility: visible !important; // 确保可见
    opacity: 1 !important; // 确保不透明

    &.mobile-scroll {
      -webkit-overflow-scrolling: touch;
      overflow-scrolling: touch;
    }
  }

  .category-tabs .tabs-container {
    gap: 0.5rem;
    padding: 0 1rem;
    justify-content: flex-start;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .category-tab {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    min-width: fit-content;
    white-space: nowrap;

    // 触摸优化
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .model-card {
    padding: 1.25rem;
    min-height: 180px; // 减小最小高度
    border-radius: 12px;
    display: block !important; // 强制显示
    visibility: visible !important; // 确保可见
    opacity: 1 !important; // 确保不透明

    // 触摸反馈
    &.mobile-card {
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        background-color: #f8fafc;
      }

      // 增大触摸目标
      min-height: 160px;
    }
  }

  .model-description {
    font-size: 0.875rem;
    -webkit-line-clamp: 2;
    line-height: 1.4;
  }

  .model-specs {
    padding: 0.75rem;
    margin-top: 0.75rem;
  }

  .panel-header {
    padding: 1rem 1.25rem;

    &.safe-area-top {
      padding-top: calc(1rem + env(safe-area-inset-top));
    }

    h3 {
      font-size: 1.5rem;
    }
  }

  .models-container {
    padding: 1rem 0;
  }

  .search-input {
    font-size: 16px; // 防止iOS缩放
    padding: 1rem 3.5rem 1rem 3.25rem;

    &.mobile-input {
      min-height: 44px;
    }
  }

  .close-btn, .clear-search {
    &.mobile-btn {
      min-width: 44px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .footer-actions {
    padding: 1rem 1.25rem;
    gap: 1rem;

    &.safe-area-bottom {
      padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    }

    .btn-cancel, .btn-apply {
      &.mobile-btn {
        min-height: 48px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 12px;
        flex: 1;
      }
    }
  }
}

// 超小屏幕优化 (480px以下)
@media (max-width: 480px) {
  .selector-panel {
    .panel-header {
      padding: 0.75rem 1rem;

      h3 {
        font-size: 1.25rem;
      }

      .search-box {
        margin-top: 1rem;
      }
    }

    .models-container {
      padding: 0.75rem 0;
      display: block !important; // 强制显示容器
      visibility: visible !important; // 确保可见
    }

    .models-grid {
      padding: 0 0.75rem;
      gap: 0.5rem;
      display: grid !important; // 强制显示网格
      grid-template-columns: 1fr !important; // 单列布局
      visibility: visible !important; // 确保可见
      opacity: 1 !important; // 确保不透明
    }

    .model-card {
      padding: 1rem;
      min-height: 200px; // 增加高度以容纳完整内容
      display: block !important; // 强制显示卡片
      visibility: visible !important; // 确保可见
      opacity: 1 !important; // 确保不透明

      .model-header {
        margin-bottom: 0.75rem;

        .model-avatar {
          width: 32px;
          height: 32px;
        }

        .model-badges {
          gap: 0.25rem;

          .badge {
            font-size: 0.65rem;
            padding: 0.15rem 0.4rem;
            border-radius: 4px;

            &.premium {
              background: linear-gradient(135deg, #fbbf24, #f59e0b);
              color: white;
            }

            &.new {
              background: linear-gradient(135deg, #10b981, #059669);
              color: white;
            }

            &.fast {
              background: linear-gradient(135deg, #8b5cf6, #7c3aed);
              color: white;
            }
          }
        }
      }

      .model-title {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
      }

      .model-description {
        font-size: 0.8rem;
        -webkit-line-clamp: 2; // 允许显示2行描述
        line-height: 1.3;
        margin-bottom: 0.75rem;
      }

      .model-specs {
        padding: 0.75rem;
        margin-top: 0.5rem;
        margin-bottom: 0.75rem;
        background: #f8fafc;
        border-radius: 8px;

        .spec-row {
          margin-bottom: 0.5rem;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .spec-item {
          font-size: 0.75rem;
          margin-bottom: 0.25rem;

          .spec-label {
            font-weight: 500;
            color: #64748b;
          }

          .spec-value {
            color: #1e293b;
            font-weight: 600;
          }
        }

        .speed-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .speed-bar {
            flex: 1;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;

            .speed-fill {
              height: 100%;
              background: linear-gradient(90deg, #10b981, #059669);
              transition: width 0.3s ease;
            }
          }

          .speed-text {
            font-size: 0.7rem;
            color: #64748b;
            min-width: 30px;
          }
        }

        .quality-stars {
          display: flex;
          gap: 1px;

          .star {
            font-size: 0.8rem;
            color: #e2e8f0;

            &.filled {
              color: #fbbf24;
            }
          }
        }
      }

      .model-capabilities {
        margin-top: 0.5rem;

        .capabilities-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem;

          .capability-tag {
            font-size: 0.65rem;
            padding: 0.2rem 0.4rem;
            background: #e0f2fe;
            color: #0369a1;
            border-radius: 4px;
            white-space: nowrap;
          }

          .capability-more {
            font-size: 0.65rem;
            padding: 0.2rem 0.4rem;
            background: #f1f5f9;
            color: #64748b;
            border-radius: 4px;
          }
        }
      }
    }

    .category-tab {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
      min-height: 40px;
    }

    .footer-actions {
      padding: 0.75rem 1rem;

      .btn-cancel, .btn-apply {
        &.mobile-btn {
          min-height: 44px;
          font-size: 0.9rem;
        }
      }
    }
  }
}

// 横屏优化
@media (orientation: landscape) and (max-height: 500px) {
  .selector-panel {
    height: 100vh;

    .panel-header {
      padding: 0.5rem 1rem;

      h3 {
        font-size: 1.125rem;
      }
    }

    .models-container {
      padding: 0.5rem 0;
    }

    .model-card {
      min-height: 120px;
      padding: 0.75rem;
    }
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .model-card {
    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }

  .category-tab {
    &:hover {
      transform: none;
      box-shadow: none;
    }

    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }
}

// 动画
@keyframes spin {
  from {
    transform: translateY(-50%) rotate(0deg);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* 可折叠分类标签样式 */
.tabs-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

.tabs-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;

  .tabs-icon {
    font-size: 1.1rem;
  }

  .active-category-info {
    color: #6366f1;
    font-weight: 500;
  }
}

.tabs-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  transition: all 0.3s ease;

  &.expanded {
    transform: rotate(180deg);
    background: rgba(99, 102, 241, 0.2);
  }
}

.tabs-content {
  border-top: 1px solid #e5e7eb;
  background: white;
}

/* 分类标签折叠动画 */
.categories-slide-enter-active,
.categories-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.categories-slide-enter-from,
.categories-slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.categories-slide-enter-to,
.categories-slide-leave-from {
  max-height: 200px;
  opacity: 1;
  transform: translateY(0);
}

/* 增强的模型卡片样式 */
.enhanced-grid {
  .model-card.enhanced-card {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }

    &.free {
      border-left: 4px solid #10b981;

      .card-decoration {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
      }
    }

    &.premium {
      border-left: 4px solid #f59e0b;

      .card-decoration {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
      }
    }

    &.new {
      &::after {
        content: '✨';
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 16px;
        animation: sparkle 2s ease-in-out infinite;
      }
    }

    &.selected {
      transform: translateY(-2px);
      box-shadow:
        0 12px 40px rgba(99, 102, 241, 0.15),
        0 6px 20px rgba(99, 102, 241, 0.1);
      border-color: #6366f1;

      .card-decoration {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
      }
    }
  }
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(107, 114, 128, 0.05) 100%);
  transition: all 0.3s ease;
}

.badge.free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-weight: 600;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) rotate(180deg);
  }
}
</style>
