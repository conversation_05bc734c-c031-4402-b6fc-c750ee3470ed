// 认证相关工具函数
import { APP_CONFIG } from '@/config'

const TOKEN_KEY = APP_CONFIG.storage.tokenKey

/**
 * 获取token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置token
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 检查token是否存在
 */
export function hasToken() {
  return !!getToken()
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token) {
  if (!token) return true
  
  try {
    // 解析JWT token
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    return payload.exp < currentTime
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

/**
 * 获取token中的用户信息
 */
export function getTokenUserInfo(token) {
  if (!token) return null
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return {
      userId: payload.userId,
      username: payload.username,
      role: payload.role,
      exp: payload.exp,
    }
  } catch (error) {
    console.error('解析token用户信息失败:', error)
    return null
  }
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  localStorage.removeItem(APP_CONFIG.storage.userKey)
  localStorage.removeItem(APP_CONFIG.storage.settingsKey)
}
