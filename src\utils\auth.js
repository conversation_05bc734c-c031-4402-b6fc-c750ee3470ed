// 认证相关工具函数
import { APP_CONFIG } from '@/config'

const TOKEN_KEY = APP_CONFIG.storage.tokenKey

/**
 * 获取token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置token
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 检查token是否存在
 */
export function hasToken() {
  return !!getToken()
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token) {
  if (!token) return true

  try {
    // 检查是否是JWT格式的token
    if (token.includes('.') && token.split('.').length === 3) {
      // 解析JWT token
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    }

    // 检查是否是mock token格式 (mock_token_timestamp_random_exp_expirationTime)
    if (token.startsWith('mock_token_') && token.includes('_exp_')) {
      const parts = token.split('_exp_')
      if (parts.length === 2) {
        const expirationTime = parseInt(parts[1])
        const currentTime = Date.now()
        return currentTime > expirationTime
      }
    }

    // 如果无法识别token格式，默认认为未过期（向后兼容）
    console.warn('无法识别的token格式:', token.substring(0, 20) + '...')
    return false
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

/**
 * 获取token中的用户信息
 */
export function getTokenUserInfo(token) {
  if (!token) return null

  try {
    // 检查是否是JWT格式的token
    if (token.includes('.') && token.split('.').length === 3) {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        userId: payload.userId,
        username: payload.username,
        role: payload.role,
        exp: payload.exp,
      }
    }

    // 对于mock token，从localStorage获取用户信息
    if (token.startsWith('mock_token_')) {
      const userInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
      if (userInfo) {
        const parsedUserInfo = JSON.parse(userInfo)
        return {
          userId: parsedUserInfo.id,
          username: parsedUserInfo.username,
          role: parsedUserInfo.role,
          exp: null, // mock token的过期时间在token字符串中
        }
      }
    }

    return null
  } catch (error) {
    console.error('解析token用户信息失败:', error)
    return null
  }
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  localStorage.removeItem(APP_CONFIG.storage.userKey)
  localStorage.removeItem(APP_CONFIG.storage.settingsKey)
}
