<template>
  <div class="targon-key-pool-manager">
    <el-card class="manager-card">
      <template #header>
        <div class="card-header">
          <h3>🔑 Targon 密钥池管理</h3>
          <div class="header-actions">
            <el-button @click="refreshStatus" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
            <el-button @click="forceRotation" type="primary" size="small">
              <el-icon><Switch /></el-icon>
              手动轮换
            </el-button>
            <el-button @click="resetHealth" type="warning" size="small">
              <el-icon><RefreshRight /></el-icon>
              重置健康状态
            </el-button>
          </div>
        </div>
      </template>

      <!-- 总体状态 -->
      <div class="status-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总密钥数" :value="poolStatus.totalKeys">
              <template #suffix>
                <el-icon><Key /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="健康密钥" :value="poolStatus.healthyKeys" class="healthy">
              <template #suffix>
                <el-icon><CircleCheck /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="不健康密钥" :value="poolStatus.totalKeys - poolStatus.healthyKeys" class="unhealthy">
              <template #suffix>
                <el-icon><CircleClose /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="当前索引" :value="poolStatus.currentKeyIndex">
              <template #suffix>
                <el-icon><Position /></el-icon>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>

      <!-- 轮换信息 -->
      <div class="rotation-info">
        <el-descriptions title="轮换信息" :column="2" border>
          <el-descriptions-item label="上次轮换时间">
            {{ poolStatus.lastRotation }}
          </el-descriptions-item>
          <el-descriptions-item label="轮换间隔">
            5分钟
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 密钥状态表格 -->
      <div class="key-status-table">
        <h4>密钥状态详情（前10个）</h4>
        <el-table :data="poolStatus.keyStats" stripe>
          <el-table-column prop="key" label="密钥" width="150" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.healthy ? 'success' : 'danger'">
                {{ row.healthy ? '健康' : '不健康' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="usageCount" label="使用次数" width="100" />
          <el-table-column prop="errorCount" label="错误次数" width="100" />
          <el-table-column prop="lastError" label="最后错误" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 操作日志 -->
      <div class="operation-log">
        <h4>操作日志</h4>
        <el-scrollbar height="200px">
          <div class="log-content">
            <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Switch, RefreshRight, Key, CircleCheck, CircleClose, Position } from '@element-plus/icons-vue'
import targonApi from '@/services/targonApi'

// 响应式数据
const loading = ref(false)
const poolStatus = reactive({
  totalKeys: 0,
  healthyKeys: 0,
  currentKeyIndex: 0,
  lastRotation: '',
  keyStats: []
})

const operationLogs = ref([])
let refreshTimer = null

// 添加操作日志
const addLog = (message) => {
  const now = new Date()
  operationLogs.value.unshift({
    time: now.toLocaleTimeString(),
    message
  })
  
  // 保持最多50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 刷新状态
const refreshStatus = async () => {
  loading.value = true
  try {
    const status = targonApi.getKeyPoolStatus()
    Object.assign(poolStatus, status)
    addLog('刷新密钥池状态')
  } catch (error) {
    console.error('刷新状态失败:', error)
    ElMessage.error('刷新状态失败')
  } finally {
    loading.value = false
  }
}

// 手动轮换密钥
const forceRotation = () => {
  try {
    const newKey = targonApi.forceKeyRotation()
    refreshStatus()
    addLog(`手动轮换到新密钥: ${newKey.substring(0, 10)}...`)
    ElMessage.success('密钥轮换成功')
  } catch (error) {
    console.error('密钥轮换失败:', error)
    ElMessage.error('密钥轮换失败')
  }
}

// 重置健康状态
const resetHealth = () => {
  try {
    targonApi.resetAllKeyHealth()
    refreshStatus()
    addLog('重置所有密钥健康状态')
    ElMessage.success('健康状态重置成功')
  } catch (error) {
    console.error('重置健康状态失败:', error)
    ElMessage.error('重置健康状态失败')
  }
}

// 自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    refreshStatus()
  }, 30000) // 每30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  refreshStatus()
  startAutoRefresh()
  addLog('密钥池管理器已启动')
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.targon-key-pool-manager {
  padding: 20px;
}

.manager-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #409eff;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.status-overview {
  margin-bottom: 20px;
}

.status-overview .healthy :deep(.el-statistic__number) {
  color: #67c23a;
}

.status-overview .unhealthy :deep(.el-statistic__number) {
  color: #f56c6c;
}

.rotation-info {
  margin-bottom: 20px;
}

.key-status-table {
  margin-bottom: 20px;
}

.key-status-table h4 {
  margin-bottom: 10px;
  color: #303133;
}

.operation-log h4 {
  margin-bottom: 10px;
  color: #303133;
}

.log-content {
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  color: #606266;
}
</style>
