<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
    <!-- 震撼的英雄横幅 -->
    <div class="relative overflow-hidden bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <!-- 动态背景装饰 -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-20 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float"></div>
        <div class="absolute top-40 right-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style="animation-delay: 2s;"></div>
        <div class="absolute -bottom-8 left-1/2 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style="animation-delay: 4s;"></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
            <span class="block">探索无限</span>
            <span class="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
              创意世界
            </span>
          </h1>
          <p class="text-xl md:text-2xl text-purple-100 mb-12 max-w-3xl mx-auto leading-relaxed animate-slide-up">
            发现来自全球创作者的精彩AI艺术作品<br>
            让灵感在这里碰撞，让创意在这里绽放
          </p>

          <!-- 统计数据 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-slide-up" style="animation-delay: 0.3s;">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ totalArtworks.toLocaleString() }}+</div>
              <div class="text-purple-200">精彩作品</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ totalArtists.toLocaleString() }}+</div>
              <div class="text-purple-200">创作者</div>
            </div>
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ dailyViews.toLocaleString() }}+</div>
              <div class="text-purple-200">日浏览量</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化筛选栏 -->
    <div class="sticky top-0 z-40 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between py-4">
          <!-- 分类标签 -->
          <div class="flex items-center space-x-1">
            <div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                v-for="tab in filterTabs"
                :key="tab.value"
                @click="activeTab = tab.value"
                :class="[
                  'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
                  activeTab === tab.value
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                ]"
              >
                <div class="flex items-center space-x-2">
                  <span>{{ tab.icon }}</span>
                  <span>{{ tab.label }}</span>
                </div>
              </button>
            </div>
          </div>

          <!-- 搜索和筛选控件 -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-gray-400">
                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                    <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <input
                  v-model="searchKeyword"
                  placeholder="搜索作品、风格、创作者..."
                  @input="handleSearch"
                  class="w-80 pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                />
                <button
                  v-if="searchKeyword"
                  @click="clearSearch"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <!-- 搜索建议 -->
              <div
                v-if="showSearchSuggestions && searchSuggestions.length > 0"
                class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
              >
                <div class="p-3 border-b border-gray-100">
                  <div class="text-sm font-medium text-gray-700">热门搜索</div>
                </div>
                <div class="p-2 space-y-1">
                  <button
                    v-for="suggestion in searchSuggestions"
                    :key="suggestion"
                    @click="applySearchSuggestion(suggestion)"
                    class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors duration-200"
                  >
                    {{ suggestion }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 高级筛选 -->
            <div class="flex items-center space-x-3">
              <select
                v-model="selectedCategory"
                @change="handleFilterChange"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm bg-white"
              >
                <option value="">所有分类</option>
                <option v-for="category in categories" :key="category.value" :value="category.value">
                  {{ category.label }}
                </option>
              </select>

              <select
                v-model="selectedStyle"
                @change="handleFilterChange"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm bg-white"
              >
                <option value="">所有风格</option>
                <option v-for="style in artStyles" :key="style.value" :value="style.value">
                  {{ style.label }}
                </option>
              </select>

              <select
                v-model="sortBy"
                @change="handleFilterChange"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm bg-white"
              >
                <option value="latest">最新发布</option>
                <option value="popular">最受欢迎</option>
                <option value="trending">正在流行</option>
                <option value="views">浏览最多</option>
                  <option value="likes">点赞最多</option>
              </select>

              <button
                @click="showAdvancedFilters = !showAdvancedFilters"
                class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <line x1="4" y1="21" x2="4" y2="14" stroke="currentColor" stroke-width="2"/>
                  <line x1="4" y1="10" x2="4" y2="3" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="21" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="8" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                  <line x1="20" y1="21" x2="20" y2="16" stroke="currentColor" stroke-width="2"/>
                  <line x1="20" y1="12" x2="20" y2="3" stroke="currentColor" stroke-width="2"/>
                  <line x1="1" y1="14" x2="7" y2="14" stroke="currentColor" stroke-width="2"/>
                  <line x1="9" y1="8" x2="15" y2="8" stroke="currentColor" stroke-width="2"/>
                  <line x1="17" y1="16" x2="23" y2="16" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>高级筛选</span>
              </button>

              <!-- 视图切换 -->
              <button
                @click="toggleViewMode"
                class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
                :title="viewMode === 'grid' ? '切换到列表视图' : '切换到网格视图'"
              >
                <svg v-if="viewMode === 'grid'" width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                  <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                  <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                  <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
                  <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
                  <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
                  <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
                  <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
                  <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>{{ viewMode === 'grid' ? '网格' : '列表' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级筛选面板 -->
    <div v-if="showAdvancedFilters" class="bg-white border-b border-gray-200 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-4">
            <h4 class="text-sm font-semibold text-gray-900">分辨率</h4>
            <div class="space-y-2">
              <label v-for="resolution in resolutionOptions" :key="resolution.value" class="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  v-model="selectedResolutions"
                  :value="resolution.value"
                  class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                >
                <span class="text-sm text-gray-700">{{ resolution.label }}</span>
              </label>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="text-sm font-semibold text-gray-900">创建时间</h4>
            <div class="space-y-2">
              <label v-for="time in timeOptions" :key="time.value" class="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  v-model="selectedTimeRange"
                  :value="time.value"
                  name="timeRange"
                  class="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"
                >
                <span class="text-sm text-gray-700">{{ time.label }}</span>
              </label>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="text-sm font-semibold text-gray-900">互动数据</h4>
            <div class="space-y-4">
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <label class="text-sm text-gray-700">最少点赞数</label>
                  <span class="text-sm font-medium text-purple-600">{{ minLikes }}</span>
                </div>
                <input
                  type="range"
                  v-model="minLikes"
                  min="0"
                  max="1000"
                  class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                >
              </div>
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <label class="text-sm text-gray-700">最少浏览数</label>
                  <span class="text-sm font-medium text-purple-600">{{ minViews }}</span>
                </div>
                <input
                  type="range"
                  v-model="minViews"
                  min="0"
                  max="10000"
                  class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                >
              </div>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            @click="resetFilters"
            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
          >
            重置筛选
          </button>
          <button
            @click="applyAdvancedFilters"
            class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-md hover:shadow-lg"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 现代化作品展示区 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 精美加载动画 -->
      <div v-if="loading" class="flex flex-col items-center justify-center py-24">
        <div class="relative">
          <!-- AI大脑动画 -->
          <div class="w-24 h-24 relative">
            <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
            <div class="absolute inset-2 bg-white rounded-full flex items-center justify-center">
              <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-bounce"></div>
            </div>
            <!-- 神经网络连接线 -->
            <div class="absolute -top-4 -left-4 w-4 h-4 bg-purple-400 rounded-full animate-ping"></div>
            <div class="absolute -top-4 -right-4 w-4 h-4 bg-pink-400 rounded-full animate-ping" style="animation-delay: 0.5s;"></div>
            <div class="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-400 rounded-full animate-ping" style="animation-delay: 1s;"></div>
          </div>
        </div>
        <div class="mt-8 text-center">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">AI正在为您精选作品</h3>
          <p class="text-gray-600">发现无限创意可能...</p>
        </div>
      </div>

      <!-- 瀑布流作品网格 -->
      <div v-else :class="viewMode === 'grid' ? 'columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6' : 'space-y-6'">
        <div
          v-for="(artwork, index) in displayedArtworks"
          :key="artwork.id"
          class="break-inside-avoid mb-6 group cursor-pointer"
          :class="{ 'animate-fade-in': !loading }"
          :style="{ animationDelay: `${index * 50}ms` }"
          @click="openArtworkDetail(artwork)"
        >
          <!-- 作品卡片 -->
          <div class="bg-white rounded-2xl shadow-soft hover:shadow-strong transition-all duration-300 overflow-hidden transform hover:-translate-y-1">
            <!-- 作品图片容器 -->
            <div class="relative overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>

              <img
                :src="artwork.thumbnail"
                :alt="artwork.title"
                  @load="handleImageLoad"
                  @error="handleImageError"
                  class="artwork-image"
                />

                <!-- 图片加载占位符 -->
                <div class="image-placeholder" v-if="!artwork.imageLoaded">
                  <div class="placeholder-shimmer"></div>
                  <div class="placeholder-content">
                    <el-icon class="placeholder-icon"><Picture /></el-icon>
                    <span>加载中...</span>
                  </div>
                </div>

                <!-- 悬浮操作层 -->
                <div class="artwork-overlay">
                  <div class="overlay-gradient"></div>
                  <div class="overlay-pattern"></div>

                  <!-- 快速操作按钮 -->
                  <div class="quick-actions">
                    <button @click.stop="viewArtwork(artwork.id)" class="action-btn view-btn" title="查看详情">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>

                    <button
                      @click.stop="toggleLike(artwork)"
                      :class="['action-btn', 'like-btn', { active: artwork.isLiked }]"
                      title="点赞"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="currentColor" stroke-width="2" :fill="artwork.isLiked ? 'currentColor' : 'none'"/>
                      </svg>
                    </button>

                    <button @click.stop="shareArtwork(artwork)" class="action-btn share-btn" title="分享">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <circle cx="18" cy="5" r="3" stroke="currentColor" stroke-width="2"/>
                        <circle cx="6" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <circle cx="18" cy="19" r="3" stroke="currentColor" stroke-width="2"/>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="currentColor" stroke-width="2"/>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>
                  </div>

                  <!-- 作品信息预览 -->
                  <div class="overlay-info">
                    <div class="artwork-stats-overlay">
                      <div class="stat-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                          <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span>{{ formatNumber(artwork.views) }}</span>
                      </div>
                      <div class="stat-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span>{{ formatNumber(artwork.likes) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 质量标识 -->
                <div class="quality-badge" v-if="artwork.isHighQuality">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                  </svg>
                  <span>精选</span>
                </div>
              </div>
            </div>

            <!-- 作品信息卡片 -->
            <div class="artwork-info">
              <div class="info-header">
                <h4 class="artwork-title">{{ artwork.title }}</h4>
                <div class="artwork-category">{{ artwork.category }}</div>
              </div>

              <div class="artwork-meta">
                <div class="author-section">
                  <div class="author-avatar">
                    <img :src="artwork.author.avatar" :alt="artwork.author.name" />
                    <div class="avatar-ring"></div>
                  </div>
                  <div class="author-details">
                    <span class="author-name">{{ artwork.author.name }}</span>
                    <span class="author-level">{{ artwork.author.level }}</span>
                  </div>
                </div>

                <div class="artwork-time">
                  {{ formatRelativeTime(artwork.createdAt) }}
                </div>
              </div>

              <!-- 风格标签 -->
              <div class="artwork-tags">
                <div
                  v-for="tag in artwork.tags.slice(0, 3)"
                  :key="tag"
                  class="style-tag"
                  :style="{ background: getTagColor(tag) }"
                >
                  {{ tag }}
                </div>
                <div v-if="artwork.tags.length > 3" class="more-tags">
                  +{{ artwork.tags.length - 3 }}
                </div>
              </div>

              <!-- 互动数据 -->
              <div class="engagement-stats">
                <div class="stat-group">
                  <div class="stat-item">
                    <div class="stat-icon views">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </div>
                    <span class="stat-value">{{ formatNumber(artwork.views) }}</span>
                  </div>

                  <div class="stat-item">
                    <div class="stat-icon likes">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </div>
                    <span class="stat-value">{{ formatNumber(artwork.likes) }}</span>
                  </div>

                  <div class="stat-item">
                    <div class="stat-icon comments">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </div>
                    <span class="stat-value">{{ formatNumber(artwork.comments || 0) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && artworks.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <h3>暂无作品</h3>
          <p>{{ getEmptyMessage() }}</p>
          <el-button type="primary" @click="$router.push('/drawing')">
            开始创作
          </el-button>
        </div>

        <!-- 分页 -->
        <div v-if="artworks.length > 0" class="pagination-container">
          <div class="pagination-background">
            <div class="floating-particles">
              <div class="particle" v-for="i in 5" :key="i"></div>
            </div>
          </div>
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 40, 60, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 作品详情对话框 -->
    <ArtworkDetail
      v-model="showDetail"
      :artwork-id="selectedArtworkId"
      @like="handleArtworkLike"
      @comment="handleArtworkComment"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Grid,
  List,
  StarFilled,
  View,
  Share,
  Download,
  Star,
  Comment,
  Search
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
// import { getArtworksApi, likeArtworkApi } from '@/api/gallery'
import { formatNumber, debounce } from '@/utils/common'
import ArtworkDetail from '@/components/gallery/ArtworkDetail.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const activeTab = ref('latest')
const selectedCategory = ref('')
const selectedStyle = ref('')
const sortBy = ref('latest')
const viewMode = ref('grid')
const loading = ref(false)
const artworks = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showDetail = ref(false)
const selectedArtworkId = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const showSearchSuggestions = ref(false)
const searchSuggestions = ref(['水彩画', '动漫风格', '写实人物', '科幻场景', '抽象艺术'])
const showAdvancedFilters = ref(false)

// 高级筛选选项
const selectedResolutions = ref([])
const selectedTimeRange = ref('all')
const minLikes = ref(0)
const minViews = ref(0)

// 网格布局
const gridColumns = ref(4)

// 统计数据
const totalArtworks = ref(125847)
const totalArtists = ref(8934)
const dailyViews = ref(234567)

// 筛选选项数据
const filterTabs = [
  { label: '最新', value: 'latest', icon: '🆕' },
  { label: '热门', value: 'popular', icon: '🔥' },
  { label: '推荐', value: 'recommended', icon: '⭐' },
  { label: '关注', value: 'following', icon: '👥' }
]

const categories = [
  { label: '人物肖像', value: 'portrait' },
  { label: '自然风景', value: 'landscape' },
  { label: '可爱动物', value: 'animal' },
  { label: '建筑空间', value: 'architecture' },
  { label: '抽象艺术', value: 'abstract' },
  { label: '科幻未来', value: 'scifi' },
  { label: '动漫二次元', value: 'anime' },
  { label: '静物摄影', value: 'still_life' },
  { label: '概念设计', value: 'concept' }
]

const artStyles = [
  { label: '写实风格', value: 'realistic' },
  { label: '动漫风格', value: 'anime' },
  { label: '水彩画', value: 'watercolor' },
  { label: '油画', value: 'oil_painting' },
  { label: '素描', value: 'sketch' },
  { label: '数字艺术', value: 'digital_art' },
  { label: '印象派', value: 'impressionist' },
  { label: '超现实', value: 'surreal' }
]

const resolutionOptions = [
  { label: '512×512', value: '512x512' },
  { label: '768×512', value: '768x512' },
  { label: '512×768', value: '512x768' },
  { label: '1024×1024', value: '1024x1024' },
  { label: '1536×1024', value: '1536x1024' }
]

const timeOptions = [
  { label: '全部时间', value: 'all' },
  { label: '今天', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' }
]

// 计算属性
const displayedArtworks = computed(() => {
  return artworks.value.filter(artwork => {
    // 应用各种筛选条件
    if (selectedCategory.value && artwork.category !== selectedCategory.value) return false
    if (selectedStyle.value && artwork.style !== selectedStyle.value) return false
    if (searchKeyword.value && !artwork.title.toLowerCase().includes(searchKeyword.value.toLowerCase())) return false
    return true
  })
})

const filteredArtworksCount = computed(() => displayedArtworks.value.length)

// 方法
const handleTabChange = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
  loadArtworks()
}

const handleFilterChange = () => {
  currentPage.value = 1
  loadArtworks()
}

const handlePageChange = (page) => {
  currentPage.value = page
  loadArtworks()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadArtworks()
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

const handleSearch = debounce(() => {
  currentPage.value = 1
  loadArtworks()
}, 500)

const loadArtworks = async () => {
  try {
    loading.value = true
    
    const params = {
      tab: activeTab.value,
      category: selectedCategory.value,
      sortBy: sortBy.value,
      page: currentPage.value,
      pageSize: pageSize.value,
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    const mockArtworks = Array.from({ length: pageSize.value }, (_, index) => ({
      id: (currentPage.value - 1) * pageSize.value + index + 1,
      title: `艺术作品 ${(currentPage.value - 1) * pageSize.value + index + 1}`,
      thumbnail: `/demo-artwork-${(index % 4) + 1}.jpg`,
      author: {
        id: index + 1,
        name: `创作者${String.fromCharCode(65 + (index % 26))}`,
        avatar: `/demo-avatar-${(index % 5) + 1}.jpg`,
      },
      views: Math.floor(Math.random() * 10000) + 100,
      likes: Math.floor(Math.random() * 1000) + 10,
      isLiked: Math.random() > 0.7,
      tags: ['AI绘画', '数字艺术', '创意'].slice(0, Math.floor(Math.random() * 3) + 1),
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    }))

    artworks.value = mockArtworks
    total.value = 1000 // 模拟总数

  } catch (error) {
    console.error('加载作品失败:', error)
    ElMessage.error('加载作品失败')
  } finally {
    loading.value = false
  }
}

const viewArtwork = (artworkId) => {
  selectedArtworkId.value = artworkId
  showDetail.value = true
}

const likeArtwork = async (artworkId) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // await likeArtworkApi(artworkId)
    
    // 更新本地状态
    const artwork = artworks.value.find(item => item.id === artworkId)
    if (artwork) {
      artwork.isLiked = !artwork.isLiked
      artwork.likes += artwork.isLiked ? 1 : -1
    }
    
    ElMessage.success(artwork.isLiked ? '已点赞' : '已取消点赞')
  } catch (error) {
    console.error('点赞失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleArtworkLike = (artworkId, isLiked) => {
  const artwork = artworks.value.find(item => item.id === artworkId)
  if (artwork) {
    artwork.isLiked = isLiked
    artwork.likes += isLiked ? 1 : -1
  }
}

const handleArtworkComment = (artworkId) => {
  // 处理评论更新
  console.log('评论更新:', artworkId)
}

const handleImageLoad = (event) => {
  event.target.classList.add('loaded')
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg'
}

// 新增功能方法
const clearSearch = () => {
  searchKeyword.value = ''
  showSearchSuggestions.value = false
  handleFilterChange()
}

const applySearchSuggestion = (suggestion) => {
  searchKeyword.value = suggestion
  showSearchSuggestions.value = false
  handleFilterChange()
}

const toggleLike = async (artwork) => {
  try {
    artwork.isLiked = !artwork.isLiked
    artwork.likes += artwork.isLiked ? 1 : -1

    // 这里应该调用API
    // await likeArtworkApi(artwork.id, artwork.isLiked)

    ElMessage.success(artwork.isLiked ? '❤️ 已点赞' : '已取消点赞')
  } catch (error) {
    // 回滚状态
    artwork.isLiked = !artwork.isLiked
    artwork.likes += artwork.isLiked ? 1 : -1
    ElMessage.error('操作失败，请重试')
  }
}

const shareArtwork = async (artwork) => {
  try {
    if (navigator.share) {
      await navigator.share({
        title: artwork.title,
        text: `来看看这个精彩的AI艺术作品：${artwork.title}`,
        url: `${window.location.origin}/gallery/${artwork.id}`
      })
    } else {
      // 复制链接到剪贴板
      await navigator.clipboard.writeText(`${window.location.origin}/gallery/${artwork.id}`)
      ElMessage.success('链接已复制到剪贴板')
    }
  } catch (error) {
    console.error('分享失败:', error)
  }
}

const openArtworkDetail = (artwork) => {
  selectedArtworkId.value = artwork.id
  showDetail.value = true
}

const resetFilters = () => {
  selectedCategory.value = ''
  selectedStyle.value = ''
  selectedResolutions.value = []
  selectedTimeRange.value = 'all'
  minLikes.value = 0
  minViews.value = 0
  searchKeyword.value = ''
  handleFilterChange()
}

const applyAdvancedFilters = () => {
  showAdvancedFilters.value = false
  handleFilterChange()
  ElMessage.success('筛选条件已应用')
}

const getTagColor = (tag) => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  ]
  const index = tag.length % colors.length
  return colors[index]
}

const formatRelativeTime = (date) => {
  const now = new Date()
  const diff = now - new Date(date)
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  return new Date(date).toLocaleDateString()
}

const getEmptyMessage = () => {
  switch (activeTab.value) {
    case 'following':
      return '您关注的用户还没有发布作品'
    case 'popular':
      return '暂无热门作品'
    case 'recommended':
      return '暂无推荐作品'
    default:
      return '暂无最新作品'
  }
}

// 虚拟滚动计算
const visibleArtworks = computed(() => {
  if (artworks.value.length <= visibleCount.value) {
    return artworks.value
  }
  return artworks.value.slice(startIndex.value, endIndex.value)
})

const totalHeight = computed(() => {
  return Math.ceil(artworks.value.length / getColumnsCount()) * itemHeight
})

const offsetY = computed(() => {
  return Math.floor(startIndex.value / getColumnsCount()) * itemHeight
})

const getColumnsCount = () => {
  if (!containerRef.value) return 4
  const containerWidth = containerRef.value.clientWidth
  const cardWidth = 280 + 24 // 卡片宽度 + 间距
  return Math.floor(containerWidth / cardWidth) || 1
}

// 优化的滚动处理
let scrollTicking = false

const handleScroll = () => {
  if (!scrollTicking) {
    requestAnimationFrame(() => {
      updateVisibleRange()
      scrollTicking = false
    })
    scrollTicking = true
  }
}

const updateVisibleRange = () => {
  if (!containerRef.value) return

  const scrollTop = containerRef.value.scrollTop
  const containerHeight = containerRef.value.clientHeight
  const columnsCount = getColumnsCount()

  // 计算可见范围
  const startRow = Math.floor(scrollTop / itemHeight)
  const endRow = Math.ceil((scrollTop + containerHeight) / itemHeight)

  // 添加缓冲区
  const bufferRows = 2
  const newStartIndex = Math.max(0, (startRow - bufferRows) * columnsCount)
  const newEndIndex = Math.min(artworks.value.length, (endRow + bufferRows) * columnsCount)

  startIndex.value = newStartIndex
  endIndex.value = newEndIndex
}

// 生命周期
onMounted(() => {
  loadArtworks()

  // 初始化虚拟滚动
  nextTick(() => {
    updateVisibleRange()
    // 监听窗口大小变化
    window.addEventListener('resize', updateVisibleRange, { passive: true })
  })
})

import { onUnmounted, nextTick } from 'vue'
onUnmounted(() => {
  window.removeEventListener('resize', updateVisibleRange)
})
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  background: #e5e7eb;
  border-radius: 4px;
}

.slider::-moz-range-track {
  background: #e5e7eb;
  border-radius: 4px;
}

/* 移动端适配样式 */
@media (max-width: 768px) {
  .min-h-screen {
    padding: 0;
  }

  .container {
    padding: 1rem;
  }

  /* 头部区域 */
  .gallery-header {
    padding: 1rem 0;
    text-align: center;
  }

  .gallery-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .gallery-subtitle {
    font-size: 0.875rem;
  }

  /* 筛选器 */
  .filters-container {
    padding: 1rem;
    margin: 0 -1rem 1rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-group {
    width: 100%;
  }

  .filter-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .filter-btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  /* 作品网格 */
  .artworks-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0;
  }

  /* 作品卡片 */
  .artwork-card {
    margin-bottom: 1rem;
  }

  .artwork-image {
    height: 250px;
  }

  .artwork-info {
    padding: 1rem;
  }

  .artwork-title {
    font-size: 1rem;
  }

  .artwork-meta {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .author-section {
    width: 100%;
  }

  .artwork-time {
    align-self: flex-end;
  }

  /* 标签 */
  .artwork-tags {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .style-tag {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }

  /* 统计数据 */
  .engagement-stats {
    margin-top: 0.75rem;
  }

  .stat-group {
    gap: 1rem;
  }

  .stat-item {
    font-size: 0.75rem;
  }

  /* 模态框 */
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  .modal-image {
    max-height: 60vh;
  }

  .modal-info {
    padding: 1rem;
  }

  /* 工具栏 */
  .modal-toolbar {
    padding: 0.75rem 1rem;
    gap: 0.5rem;
  }

  .toolbar-btn {
    padding: 0.375rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  /* 头部更紧凑 */
  .gallery-header {
    padding: 0.75rem 0;
  }

  .gallery-title {
    font-size: 1.5rem;
  }

  /* 筛选器更紧凑 */
  .filters-container {
    padding: 0.75rem;
  }

  .filter-buttons {
    gap: 0.25rem;
  }

  .filter-btn {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
  }

  /* 作品网格更紧凑 */
  .artworks-grid {
    gap: 1rem;
  }

  .artwork-image {
    height: 200px;
  }

  .artwork-info {
    padding: 0.75rem;
  }

  .artwork-title {
    font-size: 0.875rem;
  }

  /* 作者信息更紧凑 */
  .author-avatar {
    width: 24px;
    height: 24px;
  }

  .author-name {
    font-size: 0.75rem;
  }

  .author-level {
    font-size: 0.625rem;
  }

  /* 统计数据更紧凑 */
  .stat-item {
    font-size: 0.625rem;
  }

  .stat-icon {
    width: 12px;
    height: 12px;
  }

  /* 模态框适配 */
  .modal-content {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
  }

  .modal-image {
    max-height: 50vh;
  }

  .modal-info {
    padding: 0.75rem;
  }

  .modal-toolbar {
    padding: 0.5rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .artwork-card:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .filter-btn:hover {
    background-color: #f3f4f6;
  }

  .toolbar-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* iOS Safari 适配 */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }

  .modal-content {
    max-height: -webkit-fill-available;
  }
}

/* 滚动条优化 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }
}