<template>
  <div class="test-page">
    <div class="test-header">
      <h1>🔧 模型选择器测试页面</h1>
      <p>验证修复后的模型选择器功能</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h2>✅ 功能测试</h2>
        <div class="test-grid">
          <div class="test-item">
            <h3>默认模型</h3>
            <p>当前选择: <strong>{{ selectedModel }}</strong></p>
            <p class="status">{{ selectedModel === 'gemini-2.5-flash' ? '✅ 正确' : '❌ 错误' }}</p>
          </div>
          
          <div class="test-item">
            <h3>可折叠分类</h3>
            <p>分类标签应该可以点击折叠/展开</p>
            <p class="status">✅ 已实现</p>
          </div>
          
          <div class="test-item">
            <h3>增强卡片</h3>
            <p>模型卡片应该有新的视觉效果</p>
            <p class="status">✅ 已实现</p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>🎯 模型选择器</h2>
        <div class="selector-test">
          <ModelSelector
            v-model="selectedModel"
            @model-changed="handleModelChange"
          />
        </div>
      </div>

      <div class="test-section" v-if="lastSelectedModel">
        <h2>📋 最近选择</h2>
        <div class="recent-selection">
          <div class="selection-info">
            <h3>{{ lastSelectedModel.name }}</h3>
            <p>{{ lastSelectedModel.description }}</p>
            <div class="selection-meta">
              <span>分类: {{ lastSelectedModel.category }}</span>
              <span>状态: {{ lastSelectedModel.status }}</span>
              <span v-if="lastSelectedModel.isFree">免费使用</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ModelSelector from '@/components/chat/ModelSelector.vue'

// 响应式数据
const selectedModel = ref('gemini-2.5-flash')
const lastSelectedModel = ref(null)

// 方法
const handleModelChange = (model) => {
  console.log('模型已切换:', model)
  lastSelectedModel.value = model
  ElMessage.success(`测试成功：已切换到 ${model.name}`)
}
</script>

<style lang="scss" scoped>
.test-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 1rem;
  }
  
  p {
    font-size: 1.125rem;
    color: #6b7280;
  }
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  
  h2 {
    font-size: 1.5rem;
    color: #374151;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.test-item {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 0.5rem;
    
    &.status {
      font-weight: 600;
      font-size: 1rem;
    }
  }
}

.selector-test {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  border: 2px dashed #d1d5db;
}

.recent-selection {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #0ea5e9;
}

.selection-info {
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #0c4a6e;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #0369a1;
    margin-bottom: 1rem;
    line-height: 1.6;
  }
}

.selection-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  
  span {
    background: white;
    color: #0c4a6e;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #0ea5e9;
  }
}

@media (max-width: 768px) {
  .test-page {
    padding: 1rem;
  }
  
  .test-header h1 {
    font-size: 2rem;
  }
  
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .selector-test {
    padding: 1rem;
  }
}
</style>
