<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
    <div class="flex h-screen">
      <!-- 左侧控制面板 -->
      <div class="w-96 bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl">
        <div class="p-6">
          <h3 class="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            AI 创作工坊
          </h3>
          <p class="text-sm text-gray-600">将您的想象力转化为艺术作品</p>
        </div>
        
        <!-- 提示词输入 -->
        <div class="p-6 space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">描述您想要的图像</label>
            <textarea
              v-model="prompt"
              placeholder="请输入您想要生成的图像描述..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
              rows="4"
            ></textarea>
          </div>
          
          <!-- 生成按钮 -->
          <button
            @click="generateImage"
            :disabled="!prompt.trim() || loading"
            class="w-full py-3 px-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-lg hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <div class="flex items-center justify-center space-x-2">
              <svg v-if="loading" class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span v-if="loading">生成中...</span>
              <span v-else>🎨 开始创作</span>
            </div>
          </button>
        </div>
      </div>

      <!-- 右侧结果展示 -->
      <div class="flex-1 bg-white/60 backdrop-blur-sm">
        <div class="h-full flex items-center justify-center">
          <div v-if="!currentImage" class="text-center">
            <div class="text-6xl mb-4">🎨</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">开始您的创作</h3>
            <p class="text-gray-600">输入描述并点击"开始创作"，AI将为您生成独特的艺术作品</p>
          </div>
          
          <div v-else class="text-center">
            <img :src="currentImage" alt="生成的图像" class="max-w-full max-h-96 rounded-lg shadow-lg" />
            <p class="mt-4 text-gray-600">{{ prompt }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const prompt = ref('')
const loading = ref(false)
const currentImage = ref('')

// 生成图像
const generateImage = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入图像描述')
    return
  }

  loading.value = true

  try {
    // 导入Pollinations API
    const { generateImage: pollinationsGenerate } = await import('@/services/pollinationsApi.js')

    // 调用Pollinations API生成图像
    const result = await pollinationsGenerate(prompt.value, {
      width: 512,
      height: 512,
      enhance: true,
      safe: true
    })

    if (result.success) {
      currentImage.value = result.imageUrl
      ElMessage.success('图像生成成功')
    } else {
      throw new Error(result.error || '图像生成失败')
    }
  } catch (error) {
    console.error('生成图像错误:', error)
    ElMessage.error('图像生成失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 组件特定样式 */
</style>
