<!--
  登录状态管理演示页面
  展示登录状态管理的各种功能
-->
<template>
  <div class="login-state-demo">
    <div class="container">
      <h1 class="demo-title">🔐 登录状态管理演示</h1>
      <p class="demo-description">
        演示登录状态的保存、恢复和多标签页同步功能
      </p>

      <!-- 当前状态展示 -->
      <el-card class="status-display" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📊 当前登录状态</h3>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              inactive-text="手动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </template>

        <div class="status-content">
          <div class="status-indicator">
            <div class="indicator-light" :class="{ active: isLoggedIn }"></div>
            <span class="status-text">
              {{ isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>

          <div v-if="isLoggedIn" class="user-info">
            <el-avatar :src="userAvatar" :size="40">
              {{ userName.charAt(0).toUpperCase() }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ userName }}</div>
              <div class="user-role">{{ userRole }}</div>
            </div>
          </div>

          <div class="status-stats">
            <div class="stat-item">
              <span class="stat-label">Token状态:</span>
              <el-tag :type="hasToken ? 'success' : 'danger'" size="small">
                {{ hasToken ? '有效' : '无效' }}
              </el-tag>
            </div>
            <div v-if="loginDuration" class="stat-item">
              <span class="stat-label">登录时长:</span>
              <span class="stat-value">{{ formatDuration(loginDuration) }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作演示 -->
      <el-card class="demo-actions" shadow="hover">
        <template #header>
          <h3>🎮 操作演示</h3>
        </template>

        <div class="action-groups">
          <!-- 基本操作 -->
          <div class="action-group">
            <h4>基本操作</h4>
            <div class="action-buttons">
              <el-button 
                @click="demoLogin" 
                :loading="loading" 
                type="primary"
                :disabled="isLoggedIn"
              >
                <el-icon><User /></el-icon>
                演示登录
              </el-button>
              <el-button 
                @click="demoLogout" 
                :loading="loading" 
                type="danger"
                :disabled="!isLoggedIn"
              >
                <el-icon><SwitchButton /></el-icon>
                演示登出
              </el-button>
            </div>
          </div>

          <!-- 状态测试 -->
          <div class="action-group">
            <h4>状态持久化测试</h4>
            <div class="action-buttons">
              <el-button @click="testRefresh" type="info">
                <el-icon><Refresh /></el-icon>
                测试页面刷新
              </el-button>
              <el-button @click="openNewTab" type="success">
                <el-icon><Plus /></el-icon>
                打开新标签页
              </el-button>
            </div>
          </div>

          <!-- 高级功能 -->
          <div class="action-group">
            <h4>高级功能</h4>
            <div class="action-buttons">
              <el-button @click="showStateDetails" type="warning">
                <el-icon><View /></el-icon>
                查看状态详情
              </el-button>
              <el-button @click="clearAllState" type="danger" plain>
                <el-icon><Delete /></el-icon>
                清除所有状态
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 实时日志 -->
      <el-card class="demo-logs" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📝 实时日志</h3>
            <el-button @click="clearLogs" size="small">清除日志</el-button>
          </div>
        </template>

        <div class="logs-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            暂无日志记录
          </div>
        </div>
      </el-card>

      <!-- 多标签页同步演示 -->
      <el-card class="sync-demo" shadow="hover">
        <template #header>
          <h3>🔄 多标签页同步演示</h3>
        </template>

        <div class="sync-content">
          <el-alert
            title="多标签页同步功能"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>当前页面会自动监听其他标签页的登录状态变化。</p>
              <p>尝试打开新标签页并在其中登录/登出，观察本页面的状态变化。</p>
            </template>
          </el-alert>

          <div class="sync-status">
            <div class="sync-indicator">
              <div class="sync-light" :class="{ active: syncActive }"></div>
              <span>同步监听: {{ syncActive ? '活跃' : '非活跃' }}</span>
            </div>
            <div class="sync-count">
              <span>检测到的同步事件: {{ syncEventCount }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 状态详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="登录状态详情"
      width="600px"
    >
      <div class="state-details">
        <pre>{{ JSON.stringify(stateDetails, null, 2) }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, SwitchButton, Refresh, Plus, View, Delete 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import loginStateManager from '@/utils/loginStateManager'

// 用户store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const logs = ref([])
const syncEventCount = ref(0)
const syncActive = ref(true)
const showDetailsDialog = ref(false)
const stateDetails = ref({})

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userName = computed(() => userStore.userName)
const userAvatar = computed(() => userStore.userAvatar)
const userRole = computed(() => userStore.userRole)
const hasToken = computed(() => !!userStore.token)

const loginDuration = computed(() => {
  const duration = loginStateManager.getDuration()
  return duration
})

// 自动刷新定时器
let refreshTimer = null
let syncCleanup = null

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    message,
    type,
    timestamp: new Date()
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 格式化时间
const formatTime = (time) => {
  return time.toLocaleTimeString()
}

// 格式化持续时间
const formatDuration = (duration) => {
  if (!duration) return '未知'
  
  const { days, hours, minutes } = duration
  const parts = []
  
  if (days > 0) parts.push(`${days}天`)
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  
  return parts.length > 0 ? parts.join(' ') : '不到1分钟'
}

// 演示登录
const demoLogin = async () => {
  loading.value = true
  addLog('开始演示登录...', 'info')
  
  try {
    const result = await userStore.login({
      username: 'demo',
      password: 'demo',
      rememberMe: true
    })
    
    if (result.success) {
      addLog('登录成功！状态已保存到本地存储', 'success')
      ElMessage.success('登录成功！')
    } else {
      addLog(`登录失败: ${result.message}`, 'error')
      ElMessage.error('登录失败')
    }
  } catch (error) {
    addLog(`登录异常: ${error.message}`, 'error')
    ElMessage.error('登录异常')
  } finally {
    loading.value = false
  }
}

// 演示登出
const demoLogout = async () => {
  loading.value = true
  addLog('开始演示登出...', 'info')
  
  try {
    await userStore.logout()
    addLog('登出成功！本地状态已清除', 'success')
    ElMessage.success('登出成功！')
  } catch (error) {
    addLog(`登出异常: ${error.message}`, 'error')
    ElMessage.error('登出异常')
  } finally {
    loading.value = false
  }
}

// 测试页面刷新
const testRefresh = () => {
  ElMessageBox.confirm(
    '这将刷新页面来测试登录状态持久化。如果当前已登录，刷新后应该保持登录状态。',
    '确认刷新页面',
    {
      confirmButtonText: '确定刷新',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    addLog('用户确认刷新页面，测试状态持久化', 'info')
    window.location.reload()
  }).catch(() => {
    addLog('用户取消页面刷新', 'info')
  })
}

// 打开新标签页
const openNewTab = () => {
  const newTab = window.open(window.location.href, '_blank')
  if (newTab) {
    addLog('已打开新标签页，可以测试多标签页同步', 'info')
    ElMessage.success('新标签页已打开')
  } else {
    addLog('打开新标签页失败，可能被浏览器阻止', 'error')
    ElMessage.error('无法打开新标签页')
  }
}

// 显示状态详情
const showStateDetails = () => {
  stateDetails.value = loginStateManager.getSummary()
  showDetailsDialog.value = true
  addLog('显示详细状态信息', 'info')
}

// 清除所有状态
const clearAllState = async () => {
  try {
    await ElMessageBox.confirm(
      '这将清除所有登录相关的本地数据，包括Token和用户信息。',
      '确认清除状态',
      {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    loginStateManager.clear()
    addLog('所有登录状态已清除', 'success')
    ElMessage.success('状态已清除')
  } catch (error) {
    if (error !== 'cancel') {
      addLog(`清除状态失败: ${error.message}`, 'error')
    }
  }
}

// 清除日志
const clearLogs = () => {
  logs.value = []
}

// 切换自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    startAutoRefresh()
    addLog('启用自动刷新', 'info')
  } else {
    stopAutoRefresh()
    addLog('禁用自动刷新', 'info')
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) return
  
  refreshTimer = setInterval(() => {
    // 静默刷新状态，不添加日志
  }, 2000)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 设置同步监听
const setupSyncListener = () => {
  syncCleanup = loginStateManager.setupListener((event) => {
    syncEventCount.value++
    addLog(`检测到存储变化: ${event.key}`, 'sync')
    
    // 重新初始化用户状态
    userStore.initUserState()
  })
  
  addLog('多标签页同步监听已启动', 'info')
}

// 生命周期
onMounted(() => {
  addLog('登录状态管理演示页面已加载', 'info')
  
  // 启动自动刷新
  if (autoRefresh.value) {
    startAutoRefresh()
  }
  
  // 设置同步监听
  setupSyncListener()
  
  // 记录初始状态
  const summary = loginStateManager.getSummary()
  addLog(`初始登录状态: ${summary.isLoggedIn ? '已登录' : '未登录'}`, 'info')
})

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh()
  
  // 清理同步监听
  if (syncCleanup) {
    syncCleanup()
  }
  
  addLog('页面卸载，清理资源', 'info')
})
</script>

<style lang="scss" scoped>
.login-state-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
}

.demo-title {
  text-align: center;
  color: white;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-description {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    color: #2c3e50;
  }
}

.status-display, .demo-actions, .demo-logs, .sync-demo {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .indicator-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e74c3c;
    transition: background-color 0.3s ease;
    
    &.active {
      background: #27ae60;
      box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
    }
  }
  
  .status-text {
    font-weight: 500;
    font-size: 1.1rem;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.user-details {
  .user-name {
    font-weight: 600;
    color: #2c3e50;
  }
  
  .user-role {
    font-size: 0.9rem;
    color: #7f8c8d;
  }
}

.status-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-label {
  font-weight: 500;
  color: #495057;
}

.stat-value {
  color: #212529;
  font-family: monospace;
}

.action-groups {
  display: grid;
  gap: 1.5rem;
}

.action-group {
  h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 0.9rem;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.success {
    background: #d4edda;
    color: #155724;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
  }
  
  &.sync {
    background: #d1ecf1;
    color: #0c5460;
  }
}

.log-time {
  color: #6c757d;
  min-width: 80px;
}

.no-logs {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.sync-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sync-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.sync-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .sync-light {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #e74c3c;
    transition: background-color 0.3s ease;
    
    &.active {
      background: #27ae60;
      animation: pulse 2s infinite;
    }
  }
}

.sync-count {
  font-family: monospace;
  color: #495057;
}

.state-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  
  pre {
    margin: 0;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (max-width: 768px) {
  .demo-title {
    font-size: 2rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .sync-status {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
</style>
