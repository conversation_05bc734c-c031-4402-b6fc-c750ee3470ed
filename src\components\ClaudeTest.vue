<template>
  <div class="claude-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>Claude API 测试</span>
          <el-button type="primary" @click="testClaudeApi" :loading="testing">
            测试连接
          </el-button>
        </div>
      </template>

      <el-form :model="form" label-width="120px">
        <el-form-item label="API 密钥">
          <el-input
            v-model="form.apiKey"
            type="password"
            placeholder="请输入 Claude API 密钥"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="选择模型">
          <el-select v-model="form.model" placeholder="请选择模型">
            <el-option
              v-for="model in models"
              :key="model.id"
              :label="model.name"
              :value="model.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="测试消息">
          <el-input
            v-model="form.message"
            type="textarea"
            :rows="3"
            placeholder="请输入测试消息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="sendTestMessage" :loading="sending">
            发送测试消息
          </el-button>
          <el-button @click="clearResponse">清空响应</el-button>
        </el-form-item>
      </el-form>

      <el-divider>API 响应</el-divider>

      <div v-if="response" class="response-section">
        <el-alert
          :title="response.success ? '请求成功' : '请求失败'"
          :type="response.success ? 'success' : 'error'"
          :description="response.message"
          show-icon
          :closable="false"
        />

        <div v-if="response.success && response.data" class="response-content">
          <h4>响应内容：</h4>
          <el-card class="response-card">
            <pre>{{ formatResponse(response.data) }}</pre>
          </el-card>
        </div>

        <div v-if="!response.success && response.error" class="error-content">
          <h4>错误详情：</h4>
          <el-card class="error-card">
            <pre>{{ JSON.stringify(response.error, null, 2) }}</pre>
          </el-card>
        </div>
      </div>

      <div v-if="!response" class="no-response">
        <el-empty description="暂无响应数据" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import claudeApi from '@/services/claudeApi'

const testing = ref(false)
const sending = ref(false)
const response = ref(null)

const form = reactive({
  apiKey: '',
  model: 'claude-3.5-sonnet',
  message: '你好，请简单介绍一下你自己。'
})

const models = ref([
  { id: 'claude-3.5-sonnet', name: 'Claude 3.5 Sonnet' },
  { id: 'claude-3.7-sonnet', name: 'Claude 3.7 Sonnet' },
  { id: 'claude-4-sonnet', name: 'Claude 4 Sonnet' },
  { id: 'deepseek-r1', name: 'DeepSeek R1' },
  { id: 'openai-gpt-4.1', name: 'OpenAI GPT-4.1' }
])

// 测试 Claude API 连接
const testClaudeApi = async () => {
  if (!form.apiKey) {
    ElMessage.warning('请输入 API 密钥')
    return
  }

  testing.value = true
  response.value = null

  try {
    claudeApi.setApiKey(form.apiKey)
    const result = await claudeApi.testConnection()
    
    response.value = result
    
    if (result.success) {
      ElMessage.success('Claude API 连接测试成功！')
    } else {
      ElMessage.error('Claude API 连接测试失败')
    }
  } catch (error) {
    response.value = {
      success: false,
      message: error.message,
      error: error
    }
    ElMessage.error('测试失败：' + error.message)
  } finally {
    testing.value = false
  }
}

// 发送测试消息
const sendTestMessage = async () => {
  if (!form.apiKey) {
    ElMessage.warning('请输入 API 密钥')
    return
  }

  if (!form.message) {
    ElMessage.warning('请输入测试消息')
    return
  }

  sending.value = true
  response.value = null

  try {
    claudeApi.setApiKey(form.apiKey)
    
    const result = await claudeApi.createChatCompletion({
      model: form.model,
      messages: [
        { role: 'user', content: form.message }
      ],
      max_tokens: 1000,
      temperature: 0.7
    })
    
    response.value = result
    
    if (result.success) {
      ElMessage.success('消息发送成功！')
    } else {
      ElMessage.error('消息发送失败')
    }
  } catch (error) {
    response.value = {
      success: false,
      message: error.message,
      error: error
    }
    ElMessage.error('发送失败：' + error.message)
  } finally {
    sending.value = false
  }
}

// 清空响应
const clearResponse = () => {
  response.value = null
}

// 格式化响应数据
const formatResponse = (data) => {
  if (typeof data === 'string') {
    return data
  }
  
  if (data.choices && data.choices[0] && data.choices[0].message) {
    return data.choices[0].message.content
  }
  
  return JSON.stringify(data, null, 2)
}

// 组件挂载时从 localStorage 加载 API 密钥
onMounted(() => {
  const savedApiKey = localStorage.getItem('claude_api_key')
  if (savedApiKey) {
    form.apiKey = savedApiKey
  }
})
</script>

<style scoped>
.claude-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.response-section {
  margin-top: 20px;
}

.response-content,
.error-content {
  margin-top: 15px;
}

.response-card,
.error-card {
  margin-top: 10px;
}

.response-card pre,
.error-card pre {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.no-response {
  margin-top: 20px;
  text-align: center;
}
</style>
