/**
 * 温和的CSS重置 - 与Tailwind CSS兼容
 * 只重置必要的样式，不影响Tailwind的基础样式
 */

// 盒模型统一
*,
*::before,
*::after {
  box-sizing: border-box;
}

// 移除默认的margin和padding，但保留其他样式
body,
h1, h2, h3, h4, h5, h6,
p,
ul, ol, li,
figure,
blockquote,
dl, dd {
  margin: 0;
}

// 移除列表样式
ul, ol {
  padding: 0;
  list-style: none;
}

// 图片响应式
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

// 表单元素继承字体
input,
button,
textarea,
select {
  font: inherit;
}

// 移除按钮默认样式，但保留Tailwind的样式
button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

// 链接样式重置
a {
  color: inherit;
  text-decoration: none;
}

// 表格重置
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 确保文本可选择
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式优化
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
