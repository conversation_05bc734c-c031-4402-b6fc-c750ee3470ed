<template>
  <div class="system-monitor">
    <!-- 粒子背景 -->
    <div class="particles-background" ref="particlesContainer"></div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="glitch-text" data-text="系统监控">系统监控</h1>
          <p class="typing-text">实时监控系统运行状态和性能指标</p>
        </div>
        <div class="header-right">
          <div class="system-status-indicator" :class="systemStatusClass">
            <div class="status-pulse"></div>
            <span>{{ systemStatus }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="status-overview">
      <div class="status-card" v-for="(card, index) in statusCards" :key="index"
           :style="{ animationDelay: index * 0.1 + 's' }">
        <div class="card-background"></div>
        <div class="card-glow"></div>
        <div class="status-icon" :class="card.iconClass">
          <div class="icon-content">{{ card.icon }}</div>
          <div class="icon-pulse"></div>
        </div>
        <div class="status-content">
          <h3>{{ card.title }}</h3>
          <p class="status-value" :class="card.valueClass">
            <span class="counter" :data-target="card.rawValue">{{ card.value }}</span>
          </p>
          <small>{{ card.subtitle }}</small>
          <div class="trend-indicator" :class="card.trend">
            <span class="trend-arrow">{{ card.trendIcon }}</span>
            <span class="trend-text">{{ card.trendText }}</span>
          </div>
        </div>
        <div class="card-corner-effects">
          <div class="corner top-left"></div>
          <div class="corner top-right"></div>
          <div class="corner bottom-left"></div>
          <div class="corner bottom-right"></div>
        </div>
      </div>
    </div>

    <!-- 实时监控图表 -->
    <div class="monitoring-charts">
      <div class="chart-container">
        <div class="chart-header">
          <h3>API调用趋势</h3>
          <div class="chart-controls">
            <button class="chart-btn" :class="{ active: chartTimeRange === '1h' }" @click="chartTimeRange = '1h'">1H</button>
            <button class="chart-btn" :class="{ active: chartTimeRange === '6h' }" @click="chartTimeRange = '6h'">6H</button>
            <button class="chart-btn" :class="{ active: chartTimeRange === '24h' }" @click="chartTimeRange = '24h'">24H</button>
          </div>
        </div>
        <div class="chart-content">
          <canvas ref="apiTrendChart" class="trend-chart"></canvas>
          <div class="chart-overlay">
            <div class="chart-grid"></div>
            <div class="chart-tooltip" ref="chartTooltip"></div>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <h3>系统性能</h3>
          <div class="performance-indicator">
            <div class="perf-dot" :class="performanceStatus"></div>
            <span>{{ performanceText }}</span>
          </div>
        </div>
        <div class="chart-content">
          <div class="performance-rings">
            <div class="ring-container" v-for="(metric, index) in performanceMetrics" :key="index">
              <div class="performance-ring">
                <svg viewBox="0 0 100 100">
                  <defs>
                    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="dangerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#F44336;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#EF5350;stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  <circle cx="50" cy="50" r="45" class="ring-bg"></circle>
                  <circle cx="50" cy="50" r="45" class="ring-progress"
                          :style="{ strokeDashoffset: 283 - (283 * metric.value / 100) }"
                          :class="metric.class"></circle>
                </svg>
                <div class="ring-content">
                  <span class="ring-value">{{ metric.value }}%</span>
                  <span class="ring-label">{{ metric.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统资源使用情况 -->
    <div class="resource-usage">
      <div class="resource-grid">
        <div class="resource-card" v-for="(resource, index) in systemResources" :key="index">
          <div class="resource-header">
            <div class="resource-icon">{{ resource.icon }}</div>
            <div class="resource-info">
              <h4>{{ resource.name }}</h4>
              <span class="resource-status" :class="resource.statusClass">{{ resource.status }}</span>
            </div>
          </div>
          <div class="resource-chart">
            <div class="liquid-progress" :class="resource.class">
              <div class="liquid-wave" :style="{ height: resource.percentage + '%' }">
                <div class="wave"></div>
                <div class="wave wave2"></div>
              </div>
              <div class="liquid-percentage">{{ resource.percentage }}%</div>
            </div>
          </div>
          <div class="resource-details">
            <div class="detail-item">
              <span class="detail-label">已用:</span>
              <span class="detail-value">{{ resource.used }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">总计:</span>
              <span class="detail-value">{{ resource.total }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">可用:</span>
              <span class="detail-value">{{ resource.available }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动日志 -->
    <div class="recent-activities">
      <h3>最近活动</h3>
      <div class="activity-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon" :class="activity.type">
            {{ getActivityIcon(activity.type) }}
          </div>
          <div class="activity-content">
            <p class="activity-message">{{ activity.message }}</p>
            <small class="activity-time">{{ formatTime(activity.timestamp) }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- 刷新控制 -->
    <div class="refresh-controls">
      <button class="btn btn-primary" @click="refreshData" :disabled="refreshing">
        {{ refreshing ? '刷新中...' : '手动刷新' }}
      </button>
      <label class="auto-refresh">
        <input type="checkbox" v-model="autoRefresh" @change="toggleAutoRefresh">
        自动刷新 (30秒)
      </label>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const systemStatus = ref('正常运行')
const systemStatusClass = computed(() => {
  switch (systemStatus.value) {
    case '正常运行': return 'online'
    case '警告': return 'warning'
    case '错误': return 'error'
    default: return 'offline'
  }
})

const uptime = ref('0天 0小时 0分钟')
const apiCalls = ref(0)
const avgResponseTime = ref(0)
const activeKeys = ref(0)
const totalKeys = ref(0)
const keyAvailability = ref(0)
const refreshing = ref(false)
const autoRefresh = ref(true)
const chartTimeRange = ref('1h')
const performanceStatus = ref('excellent')
const performanceText = ref('系统运行良好')

// DOM引用
const particlesContainer = ref(null)
const apiTrendChart = ref(null)
const chartTooltip = ref(null)

// 状态卡片数据
const statusCards = ref([])
const performanceMetrics = ref([])
const systemResources = ref([])

// 图表数据
const apiTrendData = ref([])
const timeLabels = ref([])
const recentActivities = ref([])

// 定时器和动画
let refreshTimer = null
let particlesAnimation = null
let chartAnimation = null

// 方法
const generateMockData = () => {
  // 模拟系统状态数据
  const startTime = Date.now() - Math.random() * 86400000
  const uptimeMs = Date.now() - startTime
  const days = Math.floor(uptimeMs / 86400000)
  const hours = Math.floor((uptimeMs % 86400000) / 3600000)
  const minutes = Math.floor((uptimeMs % 3600000) / 60000)
  uptime.value = `${days}天 ${hours}小时 ${minutes}分钟`

  const currentApiCalls = Math.floor(Math.random() * 10000) + 1000
  const currentResponseTime = Math.floor(Math.random() * 500) + 100
  const currentActiveKeys = Math.floor(Math.random() * 50) + 40
  const currentTotalKeys = 57
  const currentKeyAvailability = Math.round((currentActiveKeys / currentTotalKeys) * 100)

  // 更新状态卡片
  statusCards.value = [
    {
      title: '系统状态',
      value: systemStatus.value,
      rawValue: 1,
      subtitle: `运行时间: ${uptime.value}`,
      icon: '🟢',
      iconClass: 'online',
      valueClass: 'status-online',
      trend: 'up',
      trendIcon: '↗',
      trendText: '稳定运行'
    },
    {
      title: 'API调用',
      value: currentApiCalls.toLocaleString(),
      rawValue: currentApiCalls,
      subtitle: '今日总计',
      icon: '📊',
      iconClass: 'api',
      valueClass: 'value-primary',
      trend: 'up',
      trendIcon: '↗',
      trendText: '+12.5%'
    },
    {
      title: '平均响应时间',
      value: `${currentResponseTime}ms`,
      rawValue: currentResponseTime,
      subtitle: '最近1小时',
      icon: '⚡',
      iconClass: 'performance',
      valueClass: currentResponseTime < 200 ? 'value-success' : 'value-warning',
      trend: currentResponseTime < 200 ? 'down' : 'up',
      trendIcon: currentResponseTime < 200 ? '↘' : '↗',
      trendText: currentResponseTime < 200 ? '性能优秀' : '需要优化'
    },
    {
      title: '活跃密钥',
      value: `${currentActiveKeys}/${currentTotalKeys}`,
      rawValue: currentKeyAvailability,
      subtitle: `可用率: ${currentKeyAvailability}%`,
      icon: '🔑',
      iconClass: 'keys',
      valueClass: 'value-info',
      trend: 'stable',
      trendIcon: '→',
      trendText: '稳定'
    }
  ]

  // 更新性能指标
  const cpuUsage = Math.floor(Math.random() * 40) + 30
  const memoryUsage = Math.floor(Math.random() * 50) + 25
  const diskUsage = Math.floor(Math.random() * 30) + 20
  const networkUsage = Math.floor(Math.random() * 60) + 20

  performanceMetrics.value = [
    { label: 'CPU', value: cpuUsage, class: cpuUsage > 70 ? 'danger' : cpuUsage > 50 ? 'warning' : 'success' },
    { label: '内存', value: memoryUsage, class: memoryUsage > 80 ? 'danger' : memoryUsage > 60 ? 'warning' : 'success' },
    { label: '磁盘', value: diskUsage, class: diskUsage > 85 ? 'danger' : diskUsage > 70 ? 'warning' : 'success' },
    { label: '网络', value: networkUsage, class: networkUsage > 80 ? 'danger' : networkUsage > 60 ? 'warning' : 'success' }
  ]

  // 更新系统资源
  systemResources.value = [
    {
      name: '内存使用',
      icon: '🧠',
      percentage: memoryUsage,
      used: `${Math.floor(memoryUsage * 16 / 100)}GB`,
      total: '16GB',
      available: `${16 - Math.floor(memoryUsage * 16 / 100)}GB`,
      status: memoryUsage > 80 ? '高负载' : memoryUsage > 60 ? '中等负载' : '正常',
      statusClass: memoryUsage > 80 ? 'danger' : memoryUsage > 60 ? 'warning' : 'success',
      class: memoryUsage > 80 ? 'danger' : memoryUsage > 60 ? 'warning' : 'success'
    },
    {
      name: '存储空间',
      icon: '💾',
      percentage: diskUsage,
      used: `${Math.floor(diskUsage * 500 / 100)}GB`,
      total: '500GB',
      available: `${500 - Math.floor(diskUsage * 500 / 100)}GB`,
      status: diskUsage > 85 ? '空间不足' : diskUsage > 70 ? '空间紧张' : '充足',
      statusClass: diskUsage > 85 ? 'danger' : diskUsage > 70 ? 'warning' : 'success',
      class: diskUsage > 85 ? 'danger' : diskUsage > 70 ? 'warning' : 'success'
    },
    {
      name: 'CPU使用',
      icon: '⚙️',
      percentage: cpuUsage,
      used: `${cpuUsage}%`,
      total: '100%',
      available: `${100 - cpuUsage}%`,
      status: cpuUsage > 70 ? '高负载' : cpuUsage > 50 ? '中等负载' : '正常',
      statusClass: cpuUsage > 70 ? 'danger' : cpuUsage > 50 ? 'warning' : 'success',
      class: cpuUsage > 70 ? 'danger' : cpuUsage > 50 ? 'warning' : 'success'
    }
  ]

  // 生成API趋势数据
  apiTrendData.value = Array.from({ length: 20 }, () => Math.random() * 80 + 20)
  timeLabels.value = Array.from({ length: 20 }, (_, i) => {
    const time = new Date(Date.now() - (19 - i) * 60000)
    return time.getHours().toString().padStart(2, '0') + ':' +
           time.getMinutes().toString().padStart(2, '0')
  })

  // 生成最近活动
  const activities = [
    { type: 'success', message: 'API密钥测试成功' },
    { type: 'warning', message: '密钥使用率达到80%' },
    { type: 'info', message: '新用户注册' },
    { type: 'error', message: 'API调用失败' },
    { type: 'success', message: '系统健康检查通过' }
  ]

  recentActivities.value = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    ...activities[Math.floor(Math.random() * activities.length)],
    timestamp: Date.now() - Math.random() * 3600000
  }))

  // 更新性能状态
  const avgPerformance = (cpuUsage + memoryUsage + diskUsage) / 3
  if (avgPerformance < 50) {
    performanceStatus.value = 'excellent'
    performanceText.value = '系统运行优秀'
  } else if (avgPerformance < 70) {
    performanceStatus.value = 'good'
    performanceText.value = '系统运行良好'
  } else if (avgPerformance < 85) {
    performanceStatus.value = 'warning'
    performanceText.value = '系统负载较高'
  } else {
    performanceStatus.value = 'danger'
    performanceText.value = '系统负载过高'
  }
}

const getActivityIcon = (type) => {
  const icons = {
    success: '✅',
    warning: '⚠️',
    info: 'ℹ️',
    error: '❌'
  }
  return icons[type] || 'ℹ️'
}

const formatTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    generateMockData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) clearInterval(refreshTimer)
  refreshTimer = setInterval(() => {
    generateMockData()
  }, 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 粒子背景动画
const initParticles = () => {
  if (!particlesContainer.value) return

  const container = particlesContainer.value
  const particleCount = 50

  // 清空容器
  container.innerHTML = ''

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle'

    // 随机位置和大小
    const size = Math.random() * 4 + 1
    const x = Math.random() * 100
    const y = Math.random() * 100
    const duration = Math.random() * 20 + 10
    const delay = Math.random() * 5

    particle.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      left: ${x}%;
      top: ${y}%;
      animation-duration: ${duration}s;
      animation-delay: ${delay}s;
    `

    container.appendChild(particle)
  }
}

// 初始化图表
const initChart = () => {
  if (!apiTrendChart.value) return

  const canvas = apiTrendChart.value
  const ctx = canvas.getContext('2d')

  // 设置画布大小
  const rect = canvas.parentElement.getBoundingClientRect()
  canvas.width = rect.width
  canvas.height = 200

  // 绘制图表
  const drawChart = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制网格
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.lineWidth = 1

    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = (canvas.width / 10) * i
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, canvas.height)
      ctx.stroke()
    }

    // 水平网格线
    for (let i = 0; i <= 5; i++) {
      const y = (canvas.height / 5) * i
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvas.width, y)
      ctx.stroke()
    }

    // 绘制数据线
    if (apiTrendData.value.length > 0) {
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
      gradient.addColorStop(0, 'rgba(102, 177, 255, 0.8)')
      gradient.addColorStop(1, 'rgba(102, 177, 255, 0.1)')

      ctx.fillStyle = gradient
      ctx.strokeStyle = '#66b1ff'
      ctx.lineWidth = 3

      ctx.beginPath()
      ctx.moveTo(0, canvas.height)

      apiTrendData.value.forEach((point, index) => {
        const x = (canvas.width / (apiTrendData.value.length - 1)) * index
        const y = canvas.height - (point / 100) * canvas.height

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.lineTo(canvas.width, canvas.height)
      ctx.closePath()
      ctx.fill()

      // 绘制线条
      ctx.beginPath()
      apiTrendData.value.forEach((point, index) => {
        const x = (canvas.width / (apiTrendData.value.length - 1)) * index
        const y = canvas.height - (point / 100) * canvas.height

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()

      // 绘制数据点
      ctx.fillStyle = '#66b1ff'
      apiTrendData.value.forEach((point, index) => {
        const x = (canvas.width / (apiTrendData.value.length - 1)) * index
        const y = canvas.height - (point / 100) * canvas.height

        ctx.beginPath()
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()
      })
    }
  }

  drawChart()

  // 定期重绘
  chartAnimation = setInterval(drawChart, 1000)
}

// 数字计数动画
const animateCounters = () => {
  const counters = document.querySelectorAll('.counter')

  counters.forEach(counter => {
    const target = parseInt(counter.dataset.target) || 0
    const duration = 2000
    const start = performance.now()
    const startValue = 0

    const animate = (currentTime) => {
      const elapsed = currentTime - start
      const progress = Math.min(elapsed / duration, 1)

      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const current = Math.floor(startValue + (target - startValue) * easeOutQuart)

      if (counter.textContent.includes('%')) {
        counter.textContent = current + '%'
      } else if (counter.textContent.includes('ms')) {
        counter.textContent = current + 'ms'
      } else if (counter.textContent.includes('/')) {
        const parts = counter.dataset.target.toString().split('/')
        if (parts.length === 2) {
          counter.textContent = current + '/' + parts[1]
        }
      } else {
        counter.textContent = current.toLocaleString()
      }

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  })
}

// 生命周期
onMounted(() => {
  generateMockData()

  nextTick(() => {
    initParticles()
    initChart()
    animateCounters()
  })

  if (autoRefresh.value) {
    startAutoRefresh()
  }

  // 窗口大小改变时重新初始化图表
  window.addEventListener('resize', initChart)
})

onUnmounted(() => {
  stopAutoRefresh()

  if (chartAnimation) {
    clearInterval(chartAnimation)
  }

  window.removeEventListener('resize', initChart)
})
</script>

<style lang="scss" scoped>
.system-monitor {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 30%, #cbd5e1 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  color: #1e293b;
}

/* 粒子背景 */
.particles-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, rgba(59, 130, 246, 0) 70%);
  border-radius: 50%;
  animation: float linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 页面头部 */
.page-header {
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left h1 {
  font-size: 3rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  background: linear-gradient(45deg, #3b82f6, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

/* 故障文字效果 */
.glitch-text {
  position: relative;

  &::before,
  &::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &::before {
    animation: glitch-1 2s infinite;
    color: #ef4444;
    z-index: -1;
  }

  &::after {
    animation: glitch-2 2s infinite;
    color: #06b6d4;
    z-index: -2;
  }
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 1px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -1px);
  }
}

/* 打字机效果 */
.typing-text {
  font-size: 1.2rem;
  color: #64748b;
  margin: 0;
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation: typing 3s steps(30, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #3b82f6; }
}

/* 系统状态指示器 */
.system-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  &.online {
    .status-pulse {
      background: #10b981;
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
    }
  }

  &.warning {
    .status-pulse {
      background: #f59e0b;
      box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
    }
  }

  &.error {
    .status-pulse {
      background: #ef4444;
      box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
    }
  }

  span {
    font-weight: 600;
    color: #1e293b;
  }
}

.status-pulse {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 1;
  }
  70% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.95);
    opacity: 1;
  }
}

/* 状态概览 */
.status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.status-card {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.status-card:hover .card-background {
  opacity: 1;
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: rotate 10s linear infinite;
}

.status-card:hover .card-glow {
  opacity: 1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-icon {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);

  &.online {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.2);
  }

  &.api {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
  }

  &.performance {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
  }

  &.keys {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.2);
  }
}

.icon-content {
  font-size: 2rem;
  z-index: 2;
  position: relative;
}

.icon-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: inherit;
  transform: translate(-50%, -50%);
  animation: iconPulse 2s infinite;
  opacity: 0.6;
}

@keyframes iconPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
}

.status-content {
  position: relative;
  z-index: 2;

  h3 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .status-value {
    font-size: 2rem;
    font-weight: 900;
    margin: 0 0 0.5rem 0;
    background: linear-gradient(45deg, #3b82f6, #6366f1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    &.status-online {
      background: linear-gradient(45deg, #10b981, #059669);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.value-success {
      background: linear-gradient(45deg, #10b981, #059669);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.value-warning {
      background: linear-gradient(45deg, #f59e0b, #d97706);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.value-primary {
      background: linear-gradient(45deg, #3b82f6, #2563eb);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.value-info {
      background: linear-gradient(45deg, #8b5cf6, #7c3aed);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  small {
    color: #6b7280;
    font-size: 0.9rem;
  }
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;

  &.up {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.down {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  &.stable {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
  }
}

.trend-arrow {
  font-size: 1rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.card-corner-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;

  &.top-left {
    top: 10px;
    left: 10px;
    border-right: none;
    border-bottom: none;
  }

  &.top-right {
    top: 10px;
    right: 10px;
    border-left: none;
    border-bottom: none;
  }

  &.bottom-left {
    bottom: 10px;
    left: 10px;
    border-right: none;
    border-top: none;
  }

  &.bottom-right {
    bottom: 10px;
    right: 10px;
    border-left: none;
    border-top: none;
  }
}

.status-card:hover .corner {
  border-color: rgba(59, 130, 246, 0.8);
  width: 30px;
  height: 30px;
}

/* 监控图表 */
.monitoring-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.chart-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h3 {
    margin: 0;
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #3b82f6, #6366f1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.chart-controls {
  display: flex;
  gap: 0.5rem;
}

.chart-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(59, 130, 246, 0.2);
  background: rgba(59, 130, 246, 0.05);
  color: #64748b;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    color: #1e293b;
  }

  &.active {
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    border-color: transparent;
    color: #ffffff;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);
  }
}

.chart-content {
  position: relative;
  height: 250px;
}

.trend-chart {
  width: 100%;
  height: 100%;
  border-radius: 10px;
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.chart-grid {
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(148, 163, 184, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(148, 163, 184, 0.2) 1px, transparent 1px);
  background-size: 20px 20px;
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);

  span {
    color: #374151;
    font-size: 0.9rem;
    font-weight: 600;
  }
}

.perf-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;

  &.excellent {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }

  &.good {
    background: #3b82f6;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }

  &.warning {
    background: #f59e0b;
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
  }

  &.danger {
    background: #ef4444;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  }
}

.performance-rings {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  height: 100%;
}

.ring-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.performance-ring {
  position: relative;
  width: 100px;
  height: 100px;

  svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }

  .ring-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 8;
  }

  .ring-progress {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 283;
    transition: stroke-dashoffset 1s ease-in-out;

    &.success {
      stroke: url(#successGradient);
    }

    &.warning {
      stroke: url(#warningGradient);
    }

    &.danger {
      stroke: url(#dangerGradient);
    }
  }
}

.ring-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.ring-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.ring-label {
  display: block;
  font-size: 0.7rem;
  color: #64748b;
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  position: relative;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
}

.chart-line {
  position: relative;
  height: 100%;
  background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
}

.chart-point {
  position: absolute;
  bottom: 0;
  width: 8%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.chart-labels {
  position: absolute;
  bottom: -25px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.response-time-chart {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 200px;
  padding: 1rem 0;
}

.response-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.bar-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  text-align: center;
}

/* 资源使用情况 */
.resource-usage {
  position: relative;
  z-index: 2;
  margin-bottom: 3rem;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.resource-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.resource-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
}

.resource-info {
  flex: 1;

  h4 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 700;
  }
}

.resource-status {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;

  &.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
}

.resource-chart {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.liquid-progress {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(148, 163, 184, 0.1);
  border: 3px solid rgba(148, 163, 184, 0.2);
  overflow: hidden;

  &.success {
    border-color: rgba(16, 185, 129, 0.3);

    .liquid-wave {
      background: linear-gradient(0deg, #10b981 0%, #34d399 100%);
    }
  }

  &.warning {
    border-color: rgba(245, 158, 11, 0.3);

    .liquid-wave {
      background: linear-gradient(0deg, #f59e0b 0%, #fbbf24 100%);
    }
  }

  &.danger {
    border-color: rgba(239, 68, 68, 0.3);

    .liquid-wave {
      background: linear-gradient(0deg, #ef4444 0%, #f87171 100%);
    }
  }
}

.liquid-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(0deg, #3b82f6 0%, #6366f1 100%);
  transition: height 2s ease-in-out;
  border-radius: 0 0 50% 50%;

  .wave {
    position: absolute;
    top: -10px;
    left: 50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 45%;
    transform: translateX(-50%);
    animation: wave 3s linear infinite;
  }

  .wave2 {
    animation: wave 3s linear infinite reverse;
    opacity: 0.5;
  }
}

@keyframes wave {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg);
  }
}

.liquid-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  font-weight: 900;
  color: #1e293b;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.8);
  z-index: 2;
}

.resource-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.detail-item {
  text-align: center;
  padding: 0.75rem;
  background: rgba(148, 163, 184, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.detail-label {
  display: block;
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.detail-value {
  display: block;
  font-size: 1rem;
  color: #1e293b;
  font-weight: 700;
}

.recent-activities {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;

  h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
  }
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f3f4;

  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;

  &.success {
    background: #e8f5e8;
  }

  &.warning {
    background: #fff3cd;
  }

  &.info {
    background: #e3f2fd;
  }

  &.error {
    background: #ffebee;
  }
}

.activity-content {
  flex: 1;

  .activity-message {
    margin: 0 0 0.25rem 0;
    color: #2c3e50;
    font-size: 0.9rem;
  }

  .activity-time {
    color: #7f8c8d;
    font-size: 0.8rem;
  }
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  cursor: pointer;

  input[type="checkbox"] {
    margin: 0;
  }
}

/* 自定义按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 100px;
  text-align: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* SVG 渐变定义 */
.performance-ring svg defs {
  display: none;
}

/* 添加 SVG 渐变 */
.chart-container:first-child::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

/* 刷新控制 */
.refresh-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
  justify-content: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #374151;
  cursor: pointer;
  font-weight: 600;

  input[type="checkbox"] {
    margin: 0;
    transform: scale(1.2);
    accent-color: #3b82f6;
  }
}

/* 自定义按钮样式 */
.btn {
  padding: 12px 24px;
  border-radius: 25px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  text-align: center;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

/* 移动端适配 */
@media (max-width: 1200px) {
  .monitoring-charts {
    grid-template-columns: 1fr;
  }

  .performance-rings {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .system-monitor {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .header-left h1 {
    font-size: 2rem;
  }

  .typing-text {
    font-size: 1rem;
  }

  .status-overview {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .status-card {
    padding: 1.5rem;
  }

  .status-icon {
    width: 60px;
    height: 60px;
  }

  .icon-content {
    font-size: 1.5rem;
  }

  .status-value {
    font-size: 1.5rem;
  }

  .monitoring-charts {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-container {
    padding: 1.5rem;
  }

  .chart-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .chart-controls {
    justify-content: center;
  }

  .performance-rings {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .resource-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .resource-card {
    padding: 1.5rem;
  }

  .resource-details {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .refresh-controls {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .system-monitor {
    padding: 0.5rem;
  }

  .header-left h1 {
    font-size: 1.5rem;
  }

  .status-card {
    padding: 1rem;
  }

  .status-icon {
    width: 50px;
    height: 50px;
  }

  .icon-content {
    font-size: 1.2rem;
  }

  .status-value {
    font-size: 1.2rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .chart-content {
    height: 200px;
  }

  .performance-rings {
    grid-template-columns: 1fr;
  }

  .performance-ring {
    width: 80px;
    height: 80px;
  }

  .liquid-progress {
    width: 100px;
    height: 100px;
  }

  .liquid-percentage {
    font-size: 1.2rem;
  }

  .resource-card {
    padding: 1rem;
  }

  .resource-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #66b1ff, #667eea);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a9eff, #5a6fd8);
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
