<template>
  <div class="image-model-selector">
    <!-- 模型选择器触发器 -->
    <div 
      @click="showSelector = !showSelector"
      class="model-trigger cursor-pointer p-3 bg-white border border-gray-300 rounded-lg hover:border-purple-500 transition-all duration-200"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-white">
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
              <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
              <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ currentModel.name }}</div>
            <div class="text-sm text-gray-500">{{ currentModel.description }}</div>
          </div>
        </div>
        <svg 
          :class="['w-5 h-5 text-gray-400 transition-transform duration-200', showSelector ? 'rotate-180' : '']"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        </svg>
      </div>
    </div>

    <!-- 模型选择面板 -->
    <div 
      v-if="showSelector"
      class="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-xl max-h-96 overflow-y-auto"
    >
      <!-- 搜索框 -->
      <div class="p-3 border-b border-gray-100">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索图像生成模型..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
        />
      </div>

      <!-- 模型分类 -->
      <div class="p-3 border-b border-gray-100">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="category in categories"
            :key="category.id"
            @click="selectedCategory = category.id"
            :class="[
              'px-3 py-1 rounded-full text-xs font-medium transition-all duration-200',
              selectedCategory === category.id
                ? 'bg-purple-100 text-purple-700 border border-purple-300'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            {{ category.icon }} {{ category.name }}
          </button>
        </div>
      </div>

      <!-- 模型列表 -->
      <div class="max-h-64 overflow-y-auto">
        <div
          v-for="model in filteredModels"
          :key="model.id"
          @click="selectModel(model)"
          class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" class="text-white">
                  <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <div>
                <div class="font-medium text-gray-900 text-sm">{{ model.name }}</div>
                <div class="text-xs text-gray-500">{{ model.description }}</div>
                <div class="flex items-center space-x-2 mt-1">
                  <span v-if="model.isFree" class="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full">免费</span>
                  <span v-if="model.isNew" class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full">新</span>
                  <span v-if="model.isFast" class="px-2 py-0.5 bg-orange-100 text-orange-700 text-xs rounded-full">快速</span>
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="flex items-center space-x-1">
                <span v-for="i in model.quality" :key="i" class="text-yellow-400">⭐</span>
              </div>
              <div class="text-xs text-gray-500 mt-1">速度: {{ model.speed }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="p-3 bg-gray-50 border-t border-gray-100">
        <div class="text-xs text-gray-600 text-center">
          共 {{ filteredModels.length }} 个可用模型
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: 'pollinations-default'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'model-changed'])

// 响应式数据
const showSelector = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('all')

// 图像生成模型分类
const categories = [
  { id: 'all', name: '全部', icon: '🎨' },
  { id: 'pollinations', name: 'Pollinations', icon: '🌸' },
  { id: 'stable-diffusion', name: 'Stable Diffusion', icon: '🎯' },
  { id: 'midjourney', name: 'Midjourney', icon: '🎭' },
  { id: 'dalle', name: 'DALL-E', icon: '🤖' }
]

// 图像生成模型列表
const imageModels = ref([
  {
    id: 'pollinations-default',
    name: 'Pollinations 默认',
    description: '高质量图像生成，支持多种风格',
    category: 'pollinations',
    quality: 4,
    speed: 85,
    isFree: true,
    isNew: false,
    isFast: true,
    capabilities: ['图像生成', '风格转换', '高分辨率']
  },
  {
    id: 'pollinations-artistic',
    name: 'Pollinations 艺术',
    description: '专注于艺术创作的图像生成模型',
    category: 'pollinations',
    quality: 5,
    speed: 75,
    isFree: true,
    isNew: true,
    isFast: false,
    capabilities: ['艺术创作', '风格化', '创意设计']
  },
  {
    id: 'pollinations-realistic',
    name: 'Pollinations 写实',
    description: '生成逼真的照片级图像',
    category: 'pollinations',
    quality: 5,
    speed: 70,
    isFree: true,
    isNew: false,
    isFast: false,
    capabilities: ['写实图像', '人像生成', '风景摄影']
  }
])

// 计算属性
const currentModel = computed(() => {
  return imageModels.value.find(model => model.id === props.modelValue) || imageModels.value[0]
})

const filteredModels = computed(() => {
  let models = imageModels.value

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    models = models.filter(model => model.category === selectedCategory.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    models = models.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.description.toLowerCase().includes(query) ||
      model.capabilities.some(cap => cap.toLowerCase().includes(query))
    )
  }

  return models
})

// 方法
const selectModel = (model) => {
  emit('update:modelValue', model.id)
  emit('model-changed', model)
  showSelector.value = false
}

// 点击外部关闭选择器
const handleClickOutside = (event) => {
  if (!event.target.closest('.image-model-selector')) {
    showSelector.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.image-model-selector {
  position: relative;
}

.model-trigger:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
