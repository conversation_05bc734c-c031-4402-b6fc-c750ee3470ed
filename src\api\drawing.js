// 绘画相关API
import { request } from '@/utils/request'

/**
 * 生成图像
 */
export function generateImageApi(data) {
  return request.post('/drawing/generate', data)
}

/**
 * 获取绘画历史
 */
export function getDrawingHistoryApi(params) {
  return request.get('/drawing/history', params)
}

/**
 * 获取绘画详情
 */
export function getDrawingDetailApi(id) {
  return request.get(`/drawing/${id}`)
}

/**
 * 删除绘画
 */
export function deleteDrawingApi(id) {
  return request.delete(`/drawing/${id}`)
}

/**
 * 更新绘画信息
 */
export function updateDrawingApi(id, data) {
  return request.put(`/drawing/${id}`, data)
}

/**
 * 收藏/取消收藏绘画
 */
export function favoriteDrawingApi(id) {
  return request.post(`/drawing/${id}/favorite`)
}

/**
 * 点赞/取消点赞绘画
 */
export function likeDrawingApi(id) {
  return request.post(`/drawing/${id}/like`)
}

/**
 * 分享绘画
 */
export function shareDrawingApi(id, data) {
  return request.post(`/drawing/${id}/share`, data)
}

/**
 * 下载绘画
 */
export function downloadDrawingApi(id) {
  return request.download(`/drawing/${id}/download`)
}

/**
 * 图像放大
 */
export function upscaleImageApi(data) {
  return request.post('/drawing/upscale', data)
}

/**
 * 图像修复
 */
export function inpaintImageApi(data) {
  return request.post('/drawing/inpaint', data)
}

/**
 * 图像变体
 */
export function variateImageApi(data) {
  return request.post('/drawing/variate', data)
}

/**
 * 图像编辑
 */
export function editImageApi(data) {
  return request.post('/drawing/edit', data)
}

/**
 * 风格转换
 */
export function styleTransferApi(data) {
  return request.post('/drawing/style-transfer', data)
}

/**
 * 获取绘画风格列表
 */
export function getDrawingStylesApi() {
  return request.get('/drawing/styles')
}

/**
 * 获取绘画模型列表
 */
export function getDrawingModelsApi() {
  return request.get('/drawing/models')
}

/**
 * 获取模型详情
 */
export function getModelDetailApi(modelId) {
  return request.get(`/drawing/models/${modelId}`)
}

/**
 * 获取预设提示词
 */
export function getDrawingPromptsApi(params) {
  return request.get('/drawing/prompts', params)
}

/**
 * 创建自定义提示词
 */
export function createDrawingPromptApi(data) {
  return request.post('/drawing/prompts', data)
}

/**
 * 更新提示词
 */
export function updateDrawingPromptApi(id, data) {
  return request.put(`/drawing/prompts/${id}`, data)
}

/**
 * 删除提示词
 */
export function deleteDrawingPromptApi(id) {
  return request.delete(`/drawing/prompts/${id}`)
}

/**
 * 获取绘画任务状态
 */
export function getDrawingTaskStatusApi(taskId) {
  return request.get(`/drawing/tasks/${taskId}`)
}

/**
 * 取消绘画任务
 */
export function cancelDrawingTaskApi(taskId) {
  return request.post(`/drawing/tasks/${taskId}/cancel`)
}

/**
 * 获取绘画队列状态
 */
export function getDrawingQueueStatusApi() {
  return request.get('/drawing/queue/status')
}

/**
 * 获取绘画统计
 */
export function getDrawingStatsApi() {
  return request.get('/drawing/stats')
}

/**
 * 获取使用配额
 */
export function getDrawingQuotaApi() {
  return request.get('/drawing/quota')
}

/**
 * 上传参考图像
 */
export function uploadReferenceImageApi(formData) {
  return request.upload('/drawing/upload-reference', formData)
}

/**
 * 图像识别/描述
 */
export function describeImageApi(formData) {
  return request.upload('/drawing/describe', formData)
}

/**
 * 批量生成图像
 */
export function batchGenerateImagesApi(data) {
  return request.post('/drawing/batch-generate', data)
}

/**
 * 获取批量任务状态
 */
export function getBatchTaskStatusApi(batchId) {
  return request.get(`/drawing/batch/${batchId}`)
}

/**
 * 取消批量任务
 */
export function cancelBatchTaskApi(batchId) {
  return request.post(`/drawing/batch/${batchId}/cancel`)
}

/**
 * 创建绘画集合
 */
export function createDrawingCollectionApi(data) {
  return request.post('/drawing/collections', data)
}

/**
 * 获取绘画集合列表
 */
export function getDrawingCollectionsApi(params) {
  return request.get('/drawing/collections', params)
}

/**
 * 更新绘画集合
 */
export function updateDrawingCollectionApi(id, data) {
  return request.put(`/drawing/collections/${id}`, data)
}

/**
 * 删除绘画集合
 */
export function deleteDrawingCollectionApi(id) {
  return request.delete(`/drawing/collections/${id}`)
}

/**
 * 添加绘画到集合
 */
export function addToCollectionApi(collectionId, drawingId) {
  return request.post(`/drawing/collections/${collectionId}/add/${drawingId}`)
}

/**
 * 从集合移除绘画
 */
export function removeFromCollectionApi(collectionId, drawingId) {
  return request.delete(`/drawing/collections/${collectionId}/remove/${drawingId}`)
}

/**
 * 获取热门绘画
 */
export function getPopularDrawingsApi(params) {
  return request.get('/drawing/popular', params)
}

/**
 * 获取最新绘画
 */
export function getLatestDrawingsApi(params) {
  return request.get('/drawing/latest', params)
}

/**
 * 搜索绘画
 */
export function searchDrawingsApi(params) {
  return request.get('/drawing/search', params)
}

/**
 * 举报绘画
 */
export function reportDrawingApi(data) {
  return request.post('/drawing/report', data)
}

/**
 * 获取绘画评论
 */
export function getDrawingCommentsApi(drawingId, params) {
  return request.get(`/drawing/${drawingId}/comments`, params)
}

/**
 * 添加绘画评论
 */
export function addDrawingCommentApi(drawingId, data) {
  return request.post(`/drawing/${drawingId}/comments`, data)
}

/**
 * 删除绘画评论
 */
export function deleteDrawingCommentApi(commentId) {
  return request.delete(`/drawing/comments/${commentId}`)
}

/**
 * 点赞/取消点赞评论
 */
export function likeCommentApi(commentId) {
  return request.post(`/drawing/comments/${commentId}/like`)
}
