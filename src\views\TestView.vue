<template>
  <div class="test-view">
    <div class="test-container">
      <h1>应用初始化测试</h1>
      
      <div class="test-section">
        <h2>Store状态检查</h2>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">应用Store:</span>
            <span :class="['status', appStore ? 'success' : 'error']">
              {{ appStore ? '✅ 正常' : '❌ 失败' }}
            </span>
          </div>
          
          <div class="status-item">
            <span class="label">用户Store:</span>
            <span :class="['status', userStore ? 'success' : 'error']">
              {{ userStore ? '✅ 正常' : '❌ 失败' }}
            </span>
          </div>
          
          <div class="status-item">
            <span class="label">聊天Store:</span>
            <span :class="['status', chatStore ? 'success' : 'error']">
              {{ chatStore ? '✅ 正常' : '❌ 失败' }}
            </span>
          </div>
          
          <div class="status-item">
            <span class="label">绘画Store:</span>
            <span :class="['status', drawingStore ? 'success' : 'error']">
              {{ drawingStore ? '✅ 正常' : '❌ 失败' }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h2>功能测试</h2>
        <div class="test-buttons">
          <button @click="testLocalStorage" class="test-btn">测试本地存储</button>
          <button @click="testRouter" class="test-btn">测试路由</button>
          <button @click="testAPI" class="test-btn">测试API</button>
          <button @click="clearData" class="test-btn danger">清除数据</button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>系统信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">浏览器:</span>
            <span class="value">{{ browserInfo }}</span>
          </div>
          <div class="info-item">
            <span class="label">屏幕尺寸:</span>
            <span class="value">{{ screenSize }}</span>
          </div>
          <div class="info-item">
            <span class="label">本地存储:</span>
            <span class="value">{{ localStorageSupported ? '支持' : '不支持' }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h2>测试日志</h2>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
            <span class="timestamp">{{ log.timestamp }}</span>
            <span class="message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 使用响应式引用来安全地初始化 stores
const appStore = ref(null)
const userStore = ref(null)
const chatStore = ref(null)
const drawingStore = ref(null)

// 测试日志
const logs = ref([])

// 系统信息
const browserInfo = computed(() => {
  return navigator.userAgent.split(' ').slice(-2).join(' ')
})

const screenSize = computed(() => {
  return `${window.innerWidth} x ${window.innerHeight}`
})

const localStorageSupported = computed(() => {
  try {
    localStorage.setItem('test', 'test')
    localStorage.removeItem('test')
    return true
  } catch {
    return false
  }
})

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 测试本地存储
const testLocalStorage = () => {
  try {
    const testKey = 'test_' + Date.now()
    const testValue = { test: true, timestamp: Date.now() }
    
    localStorage.setItem(testKey, JSON.stringify(testValue))
    const retrieved = JSON.parse(localStorage.getItem(testKey))
    localStorage.removeItem(testKey)
    
    if (retrieved.test === testValue.test) {
      addLog('本地存储测试成功', 'success')
      ElMessage.success('本地存储测试成功')
    } else {
      throw new Error('数据不匹配')
    }
  } catch (error) {
    addLog(`本地存储测试失败: ${error.message}`, 'error')
    ElMessage.error('本地存储测试失败')
  }
}

// 测试路由
const testRouter = () => {
  try {
    addLog('当前路由: ' + router.currentRoute.value.path, 'info')
    ElMessage.info('路由信息已记录到日志')
  } catch (error) {
    addLog(`路由测试失败: ${error.message}`, 'error')
    ElMessage.error('路由测试失败')
  }
}

// 测试API
const testAPI = async () => {
  try {
    addLog('开始API测试...', 'info')
    // 这里可以添加实际的API测试
    await new Promise(resolve => setTimeout(resolve, 1000))
    addLog('API测试完成（模拟）', 'success')
    ElMessage.success('API测试完成')
  } catch (error) {
    addLog(`API测试失败: ${error.message}`, 'error')
    ElMessage.error('API测试失败')
  }
}

// 清除数据
const clearData = () => {
  try {
    localStorage.clear()
    sessionStorage.clear()
    logs.value = []
    addLog('所有数据已清除', 'warning')
    ElMessage.warning('所有数据已清除')
  } catch (error) {
    addLog(`清除数据失败: ${error.message}`, 'error')
    ElMessage.error('清除数据失败')
  }
}

onMounted(() => {
  addLog('测试页面已加载', 'success')
})
</script>

<style lang="scss" scoped>
.test-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 2rem;

  .test-container {
    max-width: 1200px;
    margin: 0 auto;

    h1 {
      text-align: center;
      color: #1f2937;
      margin-bottom: 2rem;
      font-size: 2rem;
      font-weight: 700;
    }

    .test-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      h2 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .status-grid, .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;

        .status-item, .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          background: #f8fafc;
          border-radius: 8px;

          .label {
            font-weight: 500;
            color: #374151;
          }

          .status {
            &.success { color: #10b981; }
            &.error { color: #ef4444; }
          }

          .value {
            color: #6b7280;
            font-family: monospace;
          }
        }
      }

      .test-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .test-btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          background: #3b82f6;
          color: white;

          &:hover {
            background: #2563eb;
            transform: translateY(-1px);
          }

          &.danger {
            background: #ef4444;

            &:hover {
              background: #dc2626;
            }
          }
        }
      }

      .log-container {
        max-height: 300px;
        overflow-y: auto;
        background: #1f2937;
        border-radius: 8px;
        padding: 1rem;

        .log-item {
          display: flex;
          gap: 1rem;
          margin-bottom: 0.5rem;
          font-family: monospace;
          font-size: 0.875rem;

          .timestamp {
            color: #9ca3af;
            flex-shrink: 0;
          }

          .message {
            color: #e5e7eb;
          }

          &.success .message { color: #10b981; }
          &.error .message { color: #ef4444; }
          &.warning .message { color: #f59e0b; }
        }
      }
    }
  }
}
</style>
