/**
 * 庆祝动画工具函数
 * 提供撒花动画和庆祝弹窗功能
 */

import { ElMessageBox } from 'element-plus'

/**
 * 创建撒花动画
 * @param {Object} options 配置选项
 * @param {number} options.count - 撒花数量，默认50
 * @param {number} options.duration - 动画持续时间(秒)，默认5
 * @param {Array} options.emojis - 自定义emoji数组
 * @param {Object} options.size - 大小范围 {min: 16, max: 32}
 */
export const createConfetti = (options = {}) => {
  const {
    count = 50,
    duration = 5,
    emojis = ['🎉', '🎊', '✨', '🎈', '🌟', '💫', '🎆', '🎇', '🏆', '👏', '🔑', '✅', '🚀', '💎', '⭐'],
    size = { min: 16, max: 32 }
  } = options

  const confettiContainer = document.createElement('div')
  confettiContainer.className = 'confetti-container'
  document.body.appendChild(confettiContainer)

  for (let i = 0; i < count; i++) {
    const confettiPiece = document.createElement('div')
    confettiPiece.className = 'confetti-piece'
    confettiPiece.textContent = emojis[Math.floor(Math.random() * emojis.length)]

    // 随机位置
    confettiPiece.style.left = Math.random() * 100 + '%'
    confettiPiece.style.animationDelay = Math.random() * 2 + 's'
    confettiPiece.style.animationDuration = (Math.random() * 3 + 2) + 's'

    // 随机大小
    const pieceSize = Math.random() * (size.max - size.min) + size.min
    confettiPiece.style.fontSize = pieceSize + 'px'

    confettiContainer.appendChild(confettiPiece)
  }

  // 清理动画
  setTimeout(() => {
    if (confettiContainer && confettiContainer.parentNode) {
      confettiContainer.parentNode.removeChild(confettiContainer)
    }
  }, duration * 1000)
}

/**
 * 显示庆祝弹窗
 * @param {Object} options 配置选项
 * @param {string} options.title - 弹窗标题
 * @param {string} options.message - 弹窗消息
 * @param {string} options.type - 弹窗类型 success/info/warning/error
 * @param {boolean} options.showConfetti - 是否显示撒花动画
 * @param {Object} options.confettiOptions - 撒花动画配置
 */
export const showCelebrationModal = (options = {}) => {
  const {
    title = '🎊 操作完成',
    message = '操作已成功完成！',
    type = 'success',
    showConfetti = true,
    confettiOptions = {}
  } = options

  if (showConfetti) {
    createConfetti(confettiOptions)
  }

  return ElMessageBox({
    title,
    message,
    type,
    confirmButtonText: '太棒了！',
    showCancelButton: false,
    center: true,
    customClass: 'celebration-modal',
    dangerouslyUseHTMLString: true
  }).catch(() => {
    // 用户关闭弹窗
  })
}

/**
 * API检测完成庆祝
 * @param {number} validCount - 有效数量
 * @param {number} totalCount - 总数量
 * @param {string} itemName - 项目名称，如"密钥"、"用户"等
 */
export const celebrateApiTestCompletion = (validCount, totalCount, itemName = '项目') => {
  if (totalCount === 0) return

  const successRate = Math.round((validCount / totalCount) * 100)
  const invalidCount = totalCount - validCount

  // 触发撒花动画
  createConfetti({
    count: Math.min(50, totalCount * 2), // 根据数量调整撒花数量
    duration: successRate >= 80 ? 6 : 4 // 成功率高时动画更长
  })

  // 延迟显示庆祝消息
  setTimeout(() => {
    let message = `🎉 检测任务完成！<br><br>`
    message += `📊 总共检测：<strong>${totalCount}</strong> 个${itemName}<br>`
    message += `✅ 有效${itemName}：<strong>${validCount}</strong> 个<br>`
    message += `❌ 无效${itemName}：<strong>${invalidCount}</strong> 个<br>`
    message += `📈 成功率：<strong>${successRate}%</strong>`

    // 根据成功率添加庆祝文字
    let celebrationText = ''
    let modalType = 'success'
    
    if (successRate >= 90) {
      celebrationText = '<br><br>🏆 完美！成功率极高！'
      modalType = 'success'
    } else if (successRate >= 70) {
      celebrationText = '<br><br>🎯 太棒了！成功率很高！'
      modalType = 'success'
    } else if (successRate >= 50) {
      celebrationText = '<br><br>👍 不错的结果！'
      modalType = 'success'
    } else if (successRate > 0) {
      celebrationText = '<br><br>💪 继续加油！'
      modalType = 'info'
    } else {
      celebrationText = '<br><br>🔍 建议检查格式或来源'
      modalType = 'warning'
    }

    message += celebrationText

    showCelebrationModal({
      title: '🎊 检测完成',
      message,
      type: modalType,
      showConfetti: false // 已经显示过了
    })
  }, 500)
}

/**
 * 批量操作完成庆祝
 * @param {number} successCount - 成功数量
 * @param {number} totalCount - 总数量
 * @param {string} operation - 操作名称，如"删除"、"更新"等
 * @param {string} itemName - 项目名称
 */
export const celebrateBatchOperation = (successCount, totalCount, operation = '操作', itemName = '项目') => {
  if (totalCount === 0) return

  const successRate = Math.round((successCount / totalCount) * 100)
  const failCount = totalCount - successCount

  if (successRate === 100) {
    // 全部成功时显示撒花
    createConfetti({
      count: 30,
      duration: 3,
      emojis: ['🎉', '✅', '🎊', '🌟', '💫', '🏆']
    })

    setTimeout(() => {
      showCelebrationModal({
        title: `🎉 ${operation}完成`,
        message: `所有 <strong>${totalCount}</strong> 个${itemName}${operation}成功！<br><br>🏆 完美执行！`,
        type: 'success',
        showConfetti: false
      })
    }, 300)
  } else if (successCount > 0) {
    // 部分成功
    setTimeout(() => {
      showCelebrationModal({
        title: `📊 ${operation}完成`,
        message: `${operation}完成：<br>✅ 成功：<strong>${successCount}</strong> 个<br>❌ 失败：<strong>${failCount}</strong> 个<br><br>📈 成功率：<strong>${successRate}%</strong>`,
        type: successRate >= 70 ? 'success' : 'warning',
        showConfetti: successRate >= 70,
        confettiOptions: { count: 20, duration: 2 }
      })
    }, 300)
  }
}

/**
 * 简单成功庆祝
 * @param {string} message - 庆祝消息
 * @param {boolean} showConfetti - 是否显示撒花
 */
export const celebrateSuccess = (message = '操作成功！', showConfetti = true) => {
  if (showConfetti) {
    createConfetti({
      count: 20,
      duration: 3,
      emojis: ['🎉', '✅', '🌟', '💫', '👏']
    })
  }

  setTimeout(() => {
    showCelebrationModal({
      title: '🎉 成功',
      message,
      type: 'success',
      showConfetti: false
    })
  }, 300)
}

// 确保CSS样式被注入
const injectCelebrationStyles = () => {
  if (document.getElementById('celebration-styles')) return

  const style = document.createElement('style')
  style.id = 'celebration-styles'
  style.textContent = `
    .confetti-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 9999;
      overflow: hidden;
    }

    .confetti-piece {
      position: absolute;
      top: -50px;
      animation: confetti-fall linear infinite;
      user-select: none;
      pointer-events: none;
    }

    @keyframes confetti-fall {
      0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
      }
    }

    .confetti-piece:nth-child(odd) {
      animation-timing-function: ease-in-out;
    }

    .confetti-piece:nth-child(even) {
      animation-timing-function: ease-in;
    }

    .confetti-piece:nth-child(3n) {
      animation-duration: 3s !important;
    }

    .confetti-piece:nth-child(4n) {
      animation-duration: 2.5s !important;
    }

    .confetti-piece:nth-child(5n) {
      animation-duration: 4s !important;
    }

    .celebration-modal .el-message-box {
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .celebration-modal .el-message-box__header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 12px 12px 0 0;
      padding: 20px;
    }

    .celebration-modal .el-message-box__title {
      color: white;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .celebration-modal .el-message-box__content {
      padding: 20px;
      font-size: 14px;
      line-height: 1.6;
    }

    .celebration-modal .el-message-box__btns {
      padding: 10px 20px 20px;
    }

    .celebration-modal .el-button--primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 600;
    }

    .celebration-modal .el-button--primary:hover {
      background: linear-gradient(135deg, #5a6fd8, #6a4190);
    }
  `
  document.head.appendChild(style)
}

// 自动注入样式
if (typeof window !== 'undefined') {
  injectCelebrationStyles()
}
