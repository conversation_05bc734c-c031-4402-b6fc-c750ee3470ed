<!--
  登录状态初始化组件
  在应用启动时自动初始化和恢复登录状态
-->
<template>
  <div v-if="showInitializing" class="login-state-initializer">
    <div class="initializer-overlay">
      <div class="initializer-content">
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <p class="loading-text">{{ initializingText }}</p>
        <div v-if="showProgress" class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import loginStateManager from '@/utils/loginStateManager'

// Props
const props = defineProps({
  // 是否显示初始化过程
  showLoading: {
    type: Boolean,
    default: false
  },
  // 最小显示时间（毫秒）
  minDisplayTime: {
    type: Number,
    default: 800
  },
  // 是否显示进度条
  showProgressBar: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['initialized', 'login-restored', 'login-cleared'])

// 响应式数据
const showInitializing = ref(false)
const initializingText = ref('正在初始化...')
const progress = ref(0)
const showProgress = ref(props.showProgressBar)

// 用户store
const userStore = useUserStore()

// 初始化步骤
const initSteps = [
  { text: '检查登录状态...', duration: 200 },
  { text: '验证用户信息...', duration: 300 },
  { text: '恢复用户设置...', duration: 200 },
  { text: '初始化完成', duration: 100 }
]

// 执行初始化步骤
const executeInitSteps = async () => {
  let currentProgress = 0
  const stepProgress = 100 / initSteps.length

  for (let i = 0; i < initSteps.length; i++) {
    const step = initSteps[i]
    initializingText.value = step.text
    
    // 更新进度
    currentProgress += stepProgress
    progress.value = Math.min(currentProgress, 100)
    
    // 等待步骤完成
    await new Promise(resolve => setTimeout(resolve, step.duration))
  }
}

// 初始化登录状态
const initializeLoginState = async () => {
  const startTime = Date.now()
  
  try {
    console.log('🚀 开始初始化登录状态...')
    
    if (props.showLoading) {
      showInitializing.value = true
      
      // 如果显示进度条，执行步骤动画
      if (props.showProgressBar) {
        // 并行执行初始化和动画
        await Promise.all([
          userStore.initUserState(),
          executeInitSteps()
        ])
      } else {
        initializingText.value = '正在初始化用户状态...'
        await userStore.initUserState()
      }
    } else {
      // 静默初始化
      await userStore.initUserState()
    }
    
    // 检查初始化结果
    const loginSummary = loginStateManager.getSummary()
    console.log('📊 登录状态摘要:', loginSummary)
    
    // 发出相应事件
    if (loginSummary.isLoggedIn) {
      console.log('✅ 登录状态已恢复:', loginSummary.username)
      emit('login-restored', {
        username: loginSummary.username,
        role: loginSummary.role,
        duration: loginSummary.loginDuration
      })
    } else {
      console.log('ℹ️ 未检测到有效的登录状态')
      emit('login-cleared')
    }
    
    // 确保最小显示时间
    const elapsedTime = Date.now() - startTime
    const remainingTime = Math.max(0, props.minDisplayTime - elapsedTime)
    
    if (remainingTime > 0 && props.showLoading) {
      await new Promise(resolve => setTimeout(resolve, remainingTime))
    }
    
    console.log('✅ 登录状态初始化完成')
    emit('initialized', loginSummary)
    
  } catch (error) {
    console.error('❌ 登录状态初始化失败:', error)
    
    // 清理可能损坏的状态
    loginStateManager.cleanup()
    
    emit('initialized', { 
      isLoggedIn: false, 
      error: error.message 
    })
  } finally {
    if (props.showLoading) {
      showInitializing.value = false
    }
  }
}

// 设置存储监听器（多标签页同步）
const setupStorageSync = () => {
  const cleanup = loginStateManager.setupListener((event) => {
    console.log('🔄 检测到其他标签页的登录状态变化:', event.key)
    
    // 重新初始化状态
    nextTick(() => {
      userStore.initUserState()
    })
  })
  
  // 在组件卸载时清理监听器
  return cleanup
}

// 生命周期
onMounted(async () => {
  console.log('🎬 LoginStateInitializer 组件已挂载')
  
  // 设置存储同步
  const cleanupStorageListener = setupStorageSync()
  
  // 初始化登录状态
  await initializeLoginState()
  
  // 定期清理过期状态（每5分钟检查一次）
  const cleanupInterval = setInterval(() => {
    loginStateManager.cleanup()
  }, 5 * 60 * 1000)
  
  // 组件卸载时清理
  const cleanup = () => {
    cleanupStorageListener()
    clearInterval(cleanupInterval)
  }
  
  // Vue 3 的 onUnmounted 等价物
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanup)
  }
})

// 暴露方法给父组件
defineExpose({
  reinitialize: initializeLoginState,
  getSummary: () => loginStateManager.getSummary(),
  clearState: () => loginStateManager.clear()
})
</script>

<style lang="scss" scoped>
.login-state-initializer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.initializer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.initializer-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
}

.loading-spinner {
  margin-bottom: 1.5rem;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }
}

.loading-text {
  color: #374151;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 1rem;
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
    transition: width 0.3s ease;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .initializer-overlay {
    background: rgba(17, 24, 39, 0.95);
  }
  
  .initializer-content {
    background: #1f2937;
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  .loading-text {
    color: #d1d5db;
  }
  
  .spinner {
    border-color: #374151;
    border-top-color: #60a5fa;
  }
  
  .progress-bar {
    background: #374151;
  }
}
</style>
