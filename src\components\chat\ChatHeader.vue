<template>
  <div class="chat-header">
    <div class="header-left">
      <div class="chat-title-section">
        <div class="ai-avatar">
          <div class="avatar-inner">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="avatar-status">
            <div class="status-dot"></div>
          </div>
        </div>
        
        <div class="title-content">
          <h3 class="conversation-title">{{ conversationTitle }}</h3>
          <div class="model-info">
            <div class="model-badge" :class="getModelClass(selectedModel)">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              {{ getModelDisplayName(selectedModel) }}
            </div>
            <div class="connection-status">
              <div class="status-indicator online">
                <div class="pulse-dot"></div>
                <span>在线</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="header-right">
      <!-- 对话统计 -->
      <div class="chat-stats">
        <div class="stat-item">
          <div class="stat-content">
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ messageCount }}</span>
              <span class="stat-label">消息</span>
            </div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-content">
            <div class="stat-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ formatDuration(duration) }}</span>
              <span class="stat-label">时长</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能按钮组 -->
      <div class="chat-actions">
        <div class="action-group">
          <button 
            @click="$emit('toggle-voice-mode')" 
            :class="['action-btn', 'voice-btn', { active: voiceModeEnabled }]" 
            title="语音模式"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" stroke-width="2"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <button @click="$emit('export-chat')" class="action-btn" title="导出对话">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
              <path d="M7 10l5-5 5 5" stroke="currentColor" stroke-width="2"/>
              <path d="M12 15V5" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <button @click="$emit('show-settings')" class="action-btn" title="设置">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a3 3 0 0 1 0 6 3 3 0 0 1 0-6z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <div class="action-divider"></div>

          <button @click="$emit('clear-chat')" class="action-btn danger" title="清空对话">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  conversationTitle: {
    type: String,
    default: '新对话'
  },
  selectedModel: {
    type: String,
    default: 'gpt-3.5-turbo'
  },
  messageCount: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 0
  },
  voiceModeEnabled: {
    type: Boolean,
    default: false
  }
})

defineEmits([
  'toggle-voice-mode',
  'export-chat',
  'show-settings',
  'clear-chat'
])

const getModelDisplayName = (model) => {
  const modelNames = {
    'gpt-3.5-turbo': 'GPT-3.5 Turbo',
    'gpt-4': 'GPT-4',
    'claude-3': 'Claude-3'
  }
  return modelNames[model] || model
}

const getModelClass = (model) => {
  if (model === 'gpt-4' || model === 'claude-3') {
    return 'pro-model'
  }
  return 'free-model'
}

const formatDuration = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.chat-header {
  padding: 1.5rem 2rem;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;

    .chat-title-section {
      display: flex;
      align-items: center;
      gap: 1rem;

      .ai-avatar {
        position: relative;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;

        .avatar-inner {
          position: relative;
          z-index: 2;
        }

        .avatar-status {
          position: absolute;
          bottom: 2px;
          right: 2px;
          z-index: 3;

          .status-dot {
            width: 12px;
            height: 12px;
            background: #10b981;
            border-radius: 50%;
            border: 2px solid white;
            animation: pulse 2s infinite;
          }
        }
      }

      .title-content {
        .conversation-title {
          margin: 0;
          font-size: 1.25rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .model-info {
          display: flex;
          align-items: center;
          gap: 1rem;

          .model-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;

            &.free-model {
              background: rgba(16, 185, 129, 0.1);
              color: #059669;
            }

            &.pro-model {
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
              color: white;
            }
          }

          .connection-status {
            .status-indicator {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 14px;
              color: rgba(148, 163, 184, 0.8);

              &.online {
                color: #10b981;

                .pulse-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: #10b981;
                  animation: pulse 2s infinite;
                }
              }
            }
          }
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 2rem;

    .chat-stats {
      display: flex;
      gap: 1rem;

      .stat-item {
        .stat-content {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          background: rgba(148, 163, 184, 0.1);
          border-radius: 12px;
          min-width: 80px;

          .stat-icon {
            color: #6366f1;
          }

          .stat-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .stat-value {
              font-size: 16px;
              font-weight: 700;
              color: white;
              line-height: 1;
            }

            .stat-label {
              font-size: 12px;
              color: rgba(148, 163, 184, 0.8);
              margin-top: 2px;
            }
          }
        }
      }
    }

    .chat-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(148, 163, 184, 0.1);
        padding: 0.5rem;
        border-radius: 16px;

        .action-btn {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: transparent;
          border: none;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          color: rgba(148, 163, 184, 0.8);
          position: relative;

          &:hover {
            background: rgba(148, 163, 184, 0.2);
            color: #6366f1;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.voice-btn.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;

            &::after {
              content: '';
              position: absolute;
              top: -2px;
              right: -2px;
              width: 8px;
              height: 8px;
              background: #ef4444;
              border-radius: 50%;
              border: 2px solid white;
              animation: pulse 1s infinite;
            }
          }

          &.danger {
            &:hover {
              background: rgba(239, 68, 68, 0.1);
              color: #ef4444;
            }
          }
        }

        .action-divider {
          width: 1px;
          height: 24px;
          background: rgba(148, 163, 184, 0.3);
          margin: 0 0.5rem;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.05); 
  }
}
</style>
