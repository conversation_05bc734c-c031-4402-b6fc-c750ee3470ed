# 🚀 快速开始指南

## 📋 5分钟快速体验

### 1. 环境准备
```bash
# 确保 Node.js 版本 16+
node --version

# 克隆项目
git clone <repository-url>
cd ai-creative-platform

# 安装依赖
npm install
```

### 2. 启动项目
```bash
# 启动开发服务器
npm run dev

# 浏览器访问
http://localhost:5173
```

### 3. 立即体验
- 🤖 **AI 对话**: 访问 `/chat` 页面，选择任意模型开始对话
- 🎨 **图像生成**: 访问 `/drawing` 页面，输入描述生成图像
- 🎵 **音频处理**: 访问 `/test` 页面，体验文本转语音
- 🧪 **功能测试**: 访问 `/test` 页面，测试所有 AI 功能

## 🎯 核心功能快速上手

### AI 对话聊天
1. 进入聊天页面
2. 选择合适的 AI 模型：
   - **日常对话**: `gemini-2.5-flash` (推荐)
   - **专业分析**: `gemini-2.5-pro`
   - **快速响应**: `gemini-2.5-flash-lite`
   - **代码编程**: `qwen-coder`
3. 输入问题，享受智能对话

### AI 图像生成
1. 进入绘画页面
2. 输入图像描述 (中文或英文)
3. 选择图像尺寸和质量
4. 点击生成，等待创作完成
5. 下载或分享您的作品

### 音频处理
1. 进入测试页面
2. **文本转语音**:
   - 输入文本内容
   - 选择语音类型 (alloy, echo, fable 等)
   - 点击生成并播放
3. **音频生成**:
   - 描述想要的音频 (如"轻松的背景音乐")
   - 生成并下载音频文件

## 🔧 配置说明

### 免费功能 (无需配置)
- ✅ **Pollinations.AI**: 25+ 模型完全免费
- ✅ **图像生成**: 无水印专业图像
- ✅ **音频处理**: 文本转语音和音频生成
- ✅ **多模态**: 文本、图像、音频支持

### 可选配置 (GeminiPool)
如需使用 Gemini 2.5 系列模型，可配置 API 密钥：

```env
# .env 文件
VITE_GEMINI_API_KEY=your-api-key-here
```

## 🎨 推荐模型选择

### 🚀 **新手推荐**
- **聊天对话**: `gemini-2.5-flash` - 平衡速度和质量
- **图像生成**: 默认设置即可 - 专业无水印
- **音频处理**: `alloy` 语音 - 自然流畅

### 💼 **专业用户**
- **复杂分析**: `gemini-2.5-pro` - 最强推理能力
- **代码编程**: `qwen-coder` - 专业编程助手
- **创意写作**: `rtist` - 创意内容生成
- **快速查询**: `gemini-2.5-flash-lite` - 极速响应

### 🔬 **技术探索**
- **思维链推理**: `gemini-2.0-flash-thinking-exp-1219`
- **实验功能**: `gemini-2.0-flash-exp`
- **无审查创作**: `unity` 或 `evil`

## 📱 使用技巧

### 对话技巧
- **明确问题**: 描述越详细，回答越准确
- **分步提问**: 复杂问题可以分解为多个步骤
- **上下文**: 利用多轮对话的上下文记忆
- **模型切换**: 不同任务选择不同的专业模型

### 图像生成技巧
- **详细描述**: 包含风格、颜色、构图等细节
- **英文描述**: 英文描述通常效果更好
- **尺寸选择**: 根据用途选择合适的尺寸
- **质量设置**: 重要作品选择高质量模式

### 音频处理技巧
- **语音选择**: 不同语音适合不同场景
- **文本长度**: 适中的文本长度效果最佳
- **音频描述**: 音频生成时描述要具体生动

## 🧪 测试和调试

### 功能测试页面
访问 `/test` 页面可以：
- 测试所有 AI 模型的响应
- 比较不同模型的性能
- 验证音频功能
- 查看详细的错误信息

### 常见问题排查
1. **模型无响应**:
   - 检查网络连接
   - 尝试切换其他模型
   - 查看浏览器控制台错误

2. **图像生成失败**:
   - 简化描述内容
   - 检查描述是否包含敏感词
   - 尝试重新生成

3. **音频播放问题**:
   - 检查浏览器音频权限
   - 尝试不同的语音选项
   - 确认音频格式支持

## 📚 进阶学习

### 开发文档
- [完整集成文档](./POLLINATIONS_COMPLETE_INTEGRATION.md)
- [Gemini 模型说明](./GEMINI_2.5_MODELS.md)
- [API 参考文档](./src/services/)

### 自定义开发
```javascript
// 调用 AI 模型
import { generateChatCompletion } from '@/services/pollinationsApi.js'

const result = await generateChatCompletion([
  { role: 'user', content: '你的问题' }
], {
  model: 'gemini-2.5-flash',
  temperature: 0.7
})
```

### 社区资源
- GitHub Issues: 问题反馈和功能建议
- 文档更新: 持续更新的使用指南
- 示例代码: 丰富的使用示例

## 🎉 开始创作

现在您已经掌握了基础使用方法，可以开始您的 AI 创作之旅了！

### 创作建议
1. **从简单开始**: 先尝试基础对话和图像生成
2. **探索不同模型**: 体验各种 AI 模型的特色
3. **记录好作品**: 保存您满意的创作结果
4. **分享交流**: 与其他用户分享创作经验

### 获得帮助
- 📖 查看详细文档
- 🧪 使用测试页面调试
- 💬 在社区提问交流
- 🐛 报告问题和建议

---

**🚀 祝您创作愉快！释放 AI 的无限创意潜能！**
