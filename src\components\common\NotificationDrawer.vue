<template>
  <el-drawer
    v-model="visible"
    title="通知"
    direction="rtl"
    size="400px"
    class="notification-drawer"
  >
    <div class="drawer-content">
      <!-- 通知筛选 -->
      <div class="notification-filters">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="全部" name="all" />
          <el-tab-pane label="系统" name="system" />
          <el-tab-pane label="互动" name="interaction" />
          <el-tab-pane label="关注" name="follow" />
        </el-tabs>
      </div>

      <!-- 通知列表 -->
      <div class="notification-list">
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          class="notification-item"
          :class="{ unread: !notification.read }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-avatar">
            <el-avatar
              v-if="notification.avatar"
              :src="notification.avatar"
              :size="40"
            />
            <div v-else class="system-avatar">
              <el-icon>
                <component :is="getNotificationIcon(notification.type)" />
              </el-icon>
            </div>
          </div>

          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
          </div>

          <div class="notification-actions">
            <el-button
              v-if="!notification.read"
              type="text"
              size="small"
              @click.stop="markAsRead(notification.id)"
            >
              标记已读
            </el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="deleteNotification(notification.id)"
            >
              删除
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredNotifications.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Bell /></el-icon>
          <p>暂无通知</p>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="drawer-footer">
        <el-button @click="markAllAsRead" :disabled="unreadCount === 0">
          全部标记已读
        </el-button>
        <el-button @click="clearAllNotifications" type="danger" plain>
          清空通知
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatRelativeTime } from '@/utils/common'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const activeTab = ref('all')
const notifications = ref([
  {
    id: 1,
    type: 'like',
    title: '新的点赞',
    message: '用户 张三 点赞了您的作品《梦幻森林》',
    avatar: '/demo-avatar-1.jpg',
    read: false,
    createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
  },
  {
    id: 2,
    type: 'comment',
    title: '新的评论',
    message: '用户 李四 评论了您的作品：这幅画太美了！',
    avatar: '/demo-avatar-2.jpg',
    read: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
  },
  {
    id: 3,
    type: 'follow',
    title: '新的关注者',
    message: '用户 王五 关注了您',
    avatar: '/demo-avatar-3.jpg',
    read: true,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: 4,
    type: 'system',
    title: '系统通知',
    message: '您的作品《未来城市》已通过审核',
    avatar: null,
    read: false,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
  },
])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const filteredNotifications = computed(() => {
  if (activeTab.value === 'all') {
    return notifications.value
  }
  
  const typeMap = {
    system: ['system'],
    interaction: ['like', 'comment'],
    follow: ['follow'],
  }
  
  const types = typeMap[activeTab.value] || []
  return notifications.value.filter(n => types.includes(n.type))
})

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 方法
const handleTabChange = (tab) => {
  activeTab.value = tab
}

const handleNotificationClick = (notification) => {
  if (!notification.read) {
    markAsRead(notification.id)
  }
  
  // 根据通知类型跳转到相应页面
  // TODO: 实现页面跳转逻辑
  console.log('点击通知:', notification)
}

const markAsRead = (id) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.read = true
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(n => {
    n.read = true
  })
  ElMessage.success('已全部标记为已读')
}

const deleteNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
    ElMessage.success('通知已删除')
  }
}

const clearAllNotifications = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    
    notifications.value = []
    ElMessage.success('通知已清空')
  } catch (error) {
    // 用户取消
  }
}

const getNotificationIcon = (type) => {
  const iconMap = {
    like: 'Star',
    comment: 'ChatDotRound',
    follow: 'User',
    system: 'Bell',
  }
  return iconMap[type] || 'Bell'
}

const formatTime = (timestamp) => {
  return formatRelativeTime(timestamp)
}
</script>

<style lang="scss" scoped>
.notification-drawer {
  :deep(.el-drawer__header) {
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    margin-bottom: 0;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-filters {
  padding: 0 $spacing-lg;
  border-bottom: 1px solid $border-color-extra-light;

  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-md 0;

  .notification-item {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-md $spacing-lg;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;

    &:hover {
      background-color: $bg-color-light;
    }

    &.unread {
      background-color: $primary-light-9;
      border-left-color: $primary-color;
    }

    .notification-avatar {
      flex-shrink: 0;

      .system-avatar {
        width: 40px;
        height: 40px;
        background: $primary-light-8;
        border-radius: $border-radius-circle;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $primary-color;

        .el-icon {
          font-size: 1.2rem;
        }
      }
    }

    .notification-content {
      flex: 1;
      min-width: 0;

      .notification-title {
        font-size: $font-size-sm;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
      }

      .notification-message {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        line-height: 1.4;
        margin-bottom: $spacing-xs;
        @include text-ellipsis(2);
      }

      .notification-time {
        font-size: $font-size-xs;
        color: $text-color-placeholder;
      }
    }

    .notification-actions {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      opacity: 0;
      transition: opacity 0.2s ease;

      .el-button {
        padding: 0;
        font-size: $font-size-xs;
      }
    }

    &:hover .notification-actions {
      opacity: 1;
    }
  }

  .empty-state {
    text-align: center;
    padding: $spacing-xl;
    color: $text-color-secondary;

    .empty-icon {
      font-size: 3rem;
      margin-bottom: $spacing-md;
      color: $text-color-placeholder;
    }

    p {
      margin: 0;
      font-size: $font-size-base;
    }
  }
}

.drawer-footer {
  padding: $spacing-lg;
  border-top: 1px solid $border-color-light;
  display: flex;
  gap: $spacing-md;

  .el-button {
    flex: 1;
  }
}
</style>
