// API统一导出
export * from './user'
export * from './chat'
export * from './drawing'
export * from './gallery'

// 通用API
import { request } from '@/utils/request'

/**
 * 获取系统配置
 */
export function getSystemConfigApi() {
  return request.get('/system/config')
}

/**
 * 获取系统状态
 */
export function getSystemStatusApi() {
  return request.get('/system/status')
}

/**
 * 获取系统公告
 */
export function getSystemAnnouncementsApi(params) {
  return request.get('/system/announcements', params)
}

/**
 * 获取帮助文档
 */
export function getHelpDocsApi(params) {
  return request.get('/system/help', params)
}

/**
 * 提交反馈
 */
export function submitFeedbackApi(data) {
  return request.post('/system/feedback', data)
}

/**
 * 获取版本信息
 */
export function getVersionInfoApi() {
  return request.get('/system/version')
}

/**
 * 检查更新
 */
export function checkUpdateApi() {
  return request.get('/system/check-update')
}

/**
 * 上传文件
 */
export function uploadFileApi(formData, onProgress) {
  return request.upload('/upload', formData, {
    onUploadProgress: onProgress
  })
}

/**
 * 删除文件
 */
export function deleteFileApi(fileId) {
  return request.delete(`/upload/${fileId}`)
}

/**
 * 获取文件信息
 */
export function getFileInfoApi(fileId) {
  return request.get(`/upload/${fileId}`)
}

/**
 * 获取验证码
 */
export function getCaptchaApi() {
  return request.get('/captcha')
}

/**
 * 验证验证码
 */
export function verifyCaptchaApi(data) {
  return request.post('/captcha/verify', data)
}

/**
 * 获取地区列表
 */
export function getRegionsApi() {
  return request.get('/common/regions')
}

/**
 * 获取语言列表
 */
export function getLanguagesApi() {
  return request.get('/common/languages')
}

/**
 * 获取时区列表
 */
export function getTimezonesApi() {
  return request.get('/common/timezones')
}

/**
 * 搜索建议
 */
export function getSearchSuggestionsApi(params) {
  return request.get('/search/suggestions', params)
}

/**
 * 全局搜索
 */
export function globalSearchApi(params) {
  return request.get('/search', params)
}

/**
 * 获取热门搜索
 */
export function getHotSearchApi() {
  return request.get('/search/hot')
}

/**
 * 记录搜索历史
 */
export function recordSearchApi(data) {
  return request.post('/search/record', data)
}

/**
 * 获取搜索历史
 */
export function getSearchHistoryApi(params) {
  return request.get('/search/history', params)
}

/**
 * 清除搜索历史
 */
export function clearSearchHistoryApi() {
  return request.delete('/search/history')
}

/**
 * 获取统计数据
 */
export function getStatisticsApi(params) {
  return request.get('/statistics', params)
}

/**
 * 获取分析报告
 */
export function getAnalyticsApi(params) {
  return request.get('/analytics', params)
}

/**
 * 导出数据
 */
export function exportDataApi(params) {
  return request.download('/export', params)
}

/**
 * 导入数据
 */
export function importDataApi(formData) {
  return request.upload('/import', formData)
}

/**
 * 获取导入/导出历史
 */
export function getImportExportHistoryApi(params) {
  return request.get('/import-export/history', params)
}

/**
 * 健康检查
 */
export function healthCheckApi() {
  return request.get('/health')
}

/**
 * 获取API文档
 */
export function getApiDocsApi() {
  return request.get('/docs')
}

// 默认导出request实例
export { request } from '@/utils/request'
