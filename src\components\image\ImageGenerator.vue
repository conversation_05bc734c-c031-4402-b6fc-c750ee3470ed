<template>
  <div class="image-generator">
    <div class="generator-header">
      <h2>AI图片生成</h2>
      <p>使用Pollinations AI生成高质量图片</p>
    </div>

    <div class="generator-form">
      <div class="prompt-section">
        <label for="prompt">描述您想要的图片：</label>
        <textarea
          id="prompt"
          v-model="prompt"
          placeholder="例如：一只可爱的小猫坐在彩虹上，卡通风格，高清画质"
          rows="3"
          class="prompt-input"
        ></textarea>
      </div>

      <div class="options-section">
        <div class="option-group">
          <label>图片尺寸：</label>
          <select v-model="options.size" class="size-select">
            <option value="512x512">正方形 (512×512)</option>
            <option value="768x512">横向 (768×512)</option>
            <option value="512x768">纵向 (512×768)</option>
            <option value="1024x1024">大正方形 (1024×1024)</option>
            <option value="1024x768">大横向 (1024×768)</option>
            <option value="768x1024">大纵向 (768×1024)</option>
          </select>
        </div>

        <div class="option-group">
          <label>随机种子：</label>
          <input
            v-model="options.seed"
            type="number"
            placeholder="留空随机生成"
            class="seed-input"
          />
        </div>

        <div class="option-group">
          <label>
            <input v-model="options.enhance" type="checkbox" />
            增强画质
          </label>
        </div>

        <div class="option-group">
          <label>
            <input v-model="options.safe" type="checkbox" />
            安全模式
          </label>
        </div>
      </div>

      <div class="action-section">
        <button
          @click="generateSingleImage"
          :disabled="!prompt.trim() || loading"
          class="generate-btn primary"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '生成中...' : '生成图片' }}
        </button>

        <button
          @click="generateMultipleImages"
          :disabled="!prompt.trim() || loading"
          class="generate-btn secondary"
        >
          生成4张变体
        </button>
      </div>
    </div>

    <div v-if="results.length > 0" class="results-section">
      <h3>生成结果</h3>
      <div class="images-grid">
        <div
          v-for="(result, index) in results"
          :key="index"
          class="image-card"
        >
          <div v-if="result.success" class="image-container">
            <img :src="result.imageUrl" :alt="result.prompt" />
            <div class="image-overlay">
              <button @click="downloadImage(result)" class="overlay-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                  <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                下载
              </button>
              <button @click="copyImageUrl(result)" class="overlay-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
                复制链接
              </button>
            </div>
          </div>
          <div v-else class="error-container">
            <div class="error-icon">❌</div>
            <p class="error-message">{{ result.error }}</p>
          </div>
        </div>
      </div>
    </div>

    <div v-if="error" class="error-section">
      <div class="error-alert">
        <strong>生成失败：</strong>{{ error }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { generateImage, generateMultipleImages } from '@/services/pollinationsApi.js'

// 响应式数据
const prompt = ref('')
const loading = ref(false)
const error = ref('')
const results = ref([])

const options = ref({
  size: '512x512',
  seed: null,
  enhance: false,
  safe: true
})

// 计算属性
const imageOptions = computed(() => {
  const [width, height] = options.value.size.split('x').map(Number)
  return {
    width,
    height,
    seed: options.value.seed || undefined,
    enhance: options.value.enhance,
    safe: options.value.safe
  }
})

// 方法
const generateSingleImage = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入图片描述')
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await generateImage(prompt.value, imageOptions.value)
    
    if (result.success) {
      results.value.unshift(result)
      ElMessage.success('图片生成成功！')
    } else {
      error.value = result.error
      ElMessage.error('图片生成失败')
    }
  } catch (err) {
    error.value = err.message
    ElMessage.error('生成过程中出现错误')
  } finally {
    loading.value = false
  }
}

const generateMultipleImages = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入图片描述')
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 生成4个不同种子的变体
    const prompts = Array(4).fill(prompt.value)
    const multiOptions = {
      ...imageOptions.value,
      seed: undefined // 让每张图片使用不同的随机种子
    }

    const result = await generateMultipleImages(prompts, multiOptions)
    
    if (result.success) {
      results.value.unshift(...result.successful)
      ElMessage.success(`成功生成 ${result.successCount} 张图片！`)
      
      if (result.failCount > 0) {
        ElMessage.warning(`${result.failCount} 张图片生成失败`)
      }
    } else {
      error.value = result.error
      ElMessage.error('批量生成失败')
    }
  } catch (err) {
    error.value = err.message
    ElMessage.error('生成过程中出现错误')
  } finally {
    loading.value = false
  }
}

const downloadImage = async (result) => {
  try {
    const link = document.createElement('a')
    link.href = result.imageUrl
    link.download = `ai-generated-${Date.now()}.png`
    link.click()
    ElMessage.success('图片下载开始')
  } catch (err) {
    ElMessage.error('下载失败')
  }
}

const copyImageUrl = async (result) => {
  try {
    await navigator.clipboard.writeText(result.imageUrl)
    ElMessage.success('图片链接已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}
</script>

<style lang="scss" scoped>
.image-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.generator-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.generator-form {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.prompt-section {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .prompt-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    resize: vertical;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.options-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.option-group {
  label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  select, input[type="number"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
    }
  }
  
  input[type="checkbox"] {
    margin-right: 0.5rem;
  }
}

.action-section {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.generate-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.primary {
    background: #3b82f6;
    color: white;
    
    &:hover:not(:disabled) {
      background: #2563eb;
    }
  }
  
  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover:not(:disabled) {
      background: #e5e7eb;
    }
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-section {
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.image-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  
  img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  &:hover .image-overlay {
    opacity: 1;
  }
  
  .overlay-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
    color: #374151;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.2s ease;
    
    &:hover {
      background: #f3f4f6;
    }
  }
}

.error-container {
  padding: 2rem;
  text-align: center;
  
  .error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .error-message {
    color: #ef4444;
    font-size: 0.875rem;
  }
}

.error-section {
  .error-alert {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 1rem;
    border-radius: 8px;
  }
}

@media (max-width: 768px) {
  .image-generator {
    padding: 1rem;
  }
  
  .action-section {
    flex-direction: column;
  }
  
  .images-grid {
    grid-template-columns: 1fr;
  }
}
</style>
