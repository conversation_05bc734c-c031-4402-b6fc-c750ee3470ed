<template>
  <div v-if="showDebug" class="user-status-debug">
    <div class="debug-panel">
      <h4>用户状态调试</h4>
      <div class="debug-info">
        <p><strong>登录状态:</strong> {{ isLoggedIn ? '已登录' : '未登录' }}</p>
        <p><strong>用户角色:</strong> {{ userRole }}</p>
        <p><strong>是否管理员:</strong> {{ isAdmin ? '是' : '否' }}</p>
        <p><strong>用户名:</strong> {{ userName }}</p>
        <p><strong>Token:</strong> {{ hasToken ? '存在' : '不存在' }}</p>
        <p><strong>用户信息:</strong> {{ userInfo ? '已加载' : '未加载' }}</p>
      </div>
      <div class="debug-actions">
        <button @click="refreshUserInfo" class="debug-btn">刷新用户信息</button>
        <button @click="loginAsAdmin" class="debug-btn">登录为管理员</button>
        <button @click="logout" class="debug-btn">退出登录</button>
        <button @click="toggleDebug" class="debug-btn close">关闭</button>
      </div>
    </div>
  </div>
  <div v-else class="debug-trigger">
    <button @click="toggleDebug" class="debug-trigger-btn">🐛</button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'

const userStore = useUserStore()
const showDebug = ref(false)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const isAdmin = computed(() => userStore.isAdmin)
const userName = computed(() => userStore.userName)
const userRole = computed(() => userStore.userRole)
const userInfo = computed(() => userStore.userInfo)
const hasToken = computed(() => !!getToken())

// 方法
const toggleDebug = () => {
  showDebug.value = !showDebug.value
}

const refreshUserInfo = async () => {
  try {
    await userStore.initUserInfo()
    console.log('用户信息已刷新')
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

const loginAsAdmin = async () => {
  try {
    const result = await userStore.login({
      username: 'admin',
      password: '123456',
      rememberMe: false
    })
    if (result.success) {
      console.log('管理员登录成功')
    }
  } catch (error) {
    console.error('管理员登录失败:', error)
  }
}

const logout = async () => {
  try {
    await userStore.logout()
    console.log('已退出登录')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

// 仅在开发环境显示
const isDev = import.meta.env.DEV
</script>

<style scoped>
.user-status-debug {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-size: 12px;
  max-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.debug-panel h4 {
  margin: 0 0 12px 0;
  color: #00ff00;
  font-size: 14px;
}

.debug-info p {
  margin: 4px 0;
  line-height: 1.4;
}

.debug-actions {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.debug-btn {
  padding: 4px 8px;
  background: #333;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background 0.2s;
}

.debug-btn:hover {
  background: #555;
}

.debug-btn.close {
  background: #ff4444;
}

.debug-trigger {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 9999;
}

.debug-trigger-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.debug-trigger-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}
</style>
