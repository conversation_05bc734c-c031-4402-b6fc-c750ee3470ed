<template>
  <div class="api-key-config">
    <div class="config-header">
      <h2 class="config-title">API 配置</h2>
      <p class="config-subtitle">配置您的AI服务提供商API密钥</p>
    </div>

    <!-- API提供商列表 -->
    <div class="providers-list">
      <div 
        v-for="provider in providers" 
        :key="provider.id"
        class="provider-card"
        :class="{ 'active': selectedProvider?.id === provider.id }"
        @click="selectProvider(provider)"
      >
        <div class="provider-icon">
          <img v-if="provider.icon" :src="provider.icon" :alt="provider.name" />
          <div v-else class="provider-letter">{{ provider.name.charAt(0) }}</div>
        </div>
        <div class="provider-info">
          <h3 class="provider-name">{{ provider.name }}</h3>
          <p class="provider-description">{{ provider.description }}</p>
          <div class="provider-status">
            <el-tag 
              :type="getProviderStatus(provider).type" 
              size="small"
            >
              {{ getProviderStatus(provider).text }}
            </el-tag>
          </div>
        </div>
        <div class="provider-actions">
          <el-button 
            v-if="provider.configured" 
            type="success" 
            size="small" 
            circle
            @click.stop="testProvider(provider)"
            :loading="testingProviders.has(provider.id)"
          >
            <el-icon><Check /></el-icon>
          </el-button>
          <el-button 
            type="primary" 
            size="small" 
            circle
            @click.stop="configureProvider(provider)"
          >
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加新提供商按钮 -->
    <div class="add-provider">
      <el-button 
        type="primary" 
        :icon="Plus" 
        @click="showAddProviderDialog = true"
        class="add-provider-btn"
      >
        添加提供商
      </el-button>
    </div>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      :title="`配置 ${selectedProvider?.name}`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedProvider" class="config-form">
        <div class="provider-header">
          <div class="provider-icon large">
            <img v-if="selectedProvider.icon" :src="selectedProvider.icon" :alt="selectedProvider.name" />
            <div v-else class="provider-letter">{{ selectedProvider.name.charAt(0) }}</div>
          </div>
          <div class="provider-details">
            <h3>{{ selectedProvider.name }}</h3>
            <p>{{ selectedProvider.description }}</p>
          </div>
        </div>

        <el-form :model="configForm" label-width="100px" class="config-form-content">
          <el-form-item label="API 密钥">
            <el-input
              v-model="configForm.apiKey"
              type="password"
              placeholder="请输入API密钥"
              show-password
              clearable
            >
              <template #prepend>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="API 地址">
            <el-input
              v-model="configForm.baseUrl"
              placeholder="请输入API基础地址"
              clearable
            >
              <template #prepend>
                <el-icon><Link /></el-icon>
              </template>
            </el-input>
            <div class="form-help">
              默认: {{ selectedProvider.defaultBaseUrl }}
            </div>
          </el-form-item>

          <el-form-item label="模型">
            <el-select
              v-model="configForm.models"
              multiple
              filterable
              allow-create
              placeholder="选择或输入模型名称"
              class="full-width"
            >
              <el-option
                v-for="model in selectedProvider.availableModels"
                :key="model"
                :label="model"
                :value="model"
              />
            </el-select>
            <div class="form-help">
              可以选择预设模型或手动输入模型名称
            </div>
          </el-form-item>

          <el-form-item label="超时时间">
            <el-input-number
              v-model="configForm.timeout"
              :min="5"
              :max="300"
              :step="5"
              controls-position="right"
            />
            <span class="input-suffix">秒</span>
          </el-form-item>

          <el-form-item label="最大重试">
            <el-input-number
              v-model="configForm.maxRetries"
              :min="0"
              :max="10"
              :step="1"
              controls-position="right"
            />
            <span class="input-suffix">次</span>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="testConfiguration"
            :loading="testingConfig"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <el-button 
            type="success" 
            @click="saveConfiguration"
            :loading="savingConfig"
          >
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加提供商对话框 -->
    <el-dialog
      v-model="showAddProviderDialog"
      title="添加提供商"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="addProviderForm" label-width="100px">
        <el-form-item label="提供商名称">
          <el-input
            v-model="addProviderForm.name"
            placeholder="例如: OpenAI"
          />
        </el-form-item>

        <el-form-item label="提供商类型">
          <el-select v-model="addProviderForm.type" placeholder="选择类型" class="full-width">
            <el-option label="OpenAI" value="openai" />
            <el-option label="OpenAI-Response" value="openai-response" />
            <el-option label="Gemini" value="gemini" />
            <el-option label="Anthropic" value="anthropic" />
            <el-option label="Azure OpenAI" value="azure-openai" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item label="API 地址">
          <el-input
            v-model="addProviderForm.baseUrl"
            placeholder="https://api.example.com/v1"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddProviderDialog = false">取消</el-button>
          <el-button type="primary" @click="addProvider">添加</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Setting, Check, Key, Link, Connection } from '@element-plus/icons-vue'

// 响应式数据
const providers = ref([
  {
    id: 'openrouter',
    name: 'OpenRouter',
    description: '支持多种AI模型的统一API接口',
    type: 'openai',
    defaultBaseUrl: 'https://openrouter.ai/api/v1',
    configured: true,
    apiKey: '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',
    baseUrl: 'https://openrouter.ai/api/v1',
    models: ['deepseek/deepseek-chat-v3-0324:free', 'qwen/qwen3-coder:free'],
    availableModels: [
      'deepseek/deepseek-chat-v3-0324:free',
      'deepseek/deepseek-r1-0528:free',
      'qwen/qwen3-coder:free',
      'google/gemini-2.0-flash-exp:free',
      'mistralai/mistral-nemo:free'
    ],
    timeout: 60,
    maxRetries: 3,
    lastTested: new Date(),
    status: 'active'
  },
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'OpenAI官方API服务',
    type: 'openai',
    defaultBaseUrl: 'https://api.openai.com/v1',
    configured: false,
    availableModels: [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4-turbo',
      'gpt-3.5-turbo'
    ],
    timeout: 60,
    maxRetries: 3,
    status: 'inactive'
  },
  {
    id: 'targon',
    name: 'Targon',
    description: 'Targon API - 支持多种主流AI模型',
    type: 'openai',
    defaultBaseUrl: 'https://api.targon.com/v1',
    configured: false,
    availableModels: [], // 动态从API获取
    timeout: 60,
    maxRetries: 3,
    status: 'inactive'
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'Claude AI模型服务',
    type: 'anthropic',
    defaultBaseUrl: 'https://api.anthropic.com',
    configured: false,
    availableModels: [
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022',
      'claude-3-opus-20240229'
    ],
    timeout: 60,
    maxRetries: 3,
    status: 'inactive'
  }
])

const selectedProvider = ref(null)
const showConfigDialog = ref(false)
const showAddProviderDialog = ref(false)
const testingProviders = ref(new Set())
const testingConfig = ref(false)
const savingConfig = ref(false)

const configForm = reactive({
  apiKey: '',
  baseUrl: '',
  models: [],
  timeout: 60,
  maxRetries: 3
})

const addProviderForm = reactive({
  name: '',
  type: '',
  baseUrl: ''
})

// 计算属性
const getProviderStatus = (provider) => {
  if (!provider.configured) {
    return { type: 'info', text: '未配置' }
  }
  if (provider.status === 'active') {
    return { type: 'success', text: '正常' }
  }
  if (provider.status === 'error') {
    return { type: 'danger', text: '异常' }
  }
  return { type: 'warning', text: '未知' }
}

// 方法
const selectProvider = (provider) => {
  selectedProvider.value = provider
}

const configureProvider = (provider) => {
  selectedProvider.value = provider
  
  // 填充表单
  configForm.apiKey = provider.apiKey || ''
  configForm.baseUrl = provider.baseUrl || provider.defaultBaseUrl
  configForm.models = provider.models || []
  configForm.timeout = provider.timeout || 60
  configForm.maxRetries = provider.maxRetries || 3
  
  showConfigDialog.value = true
}

const testProvider = async (provider) => {
  testingProviders.value.add(provider.id)

  try {
    if (provider.id === 'targon') {
      // 对于 Targon API，使用真实的 API 测试
      const targonApi = await import('@/services/targonApi')
      const api = targonApi.default

      if (!provider.apiKey) {
        throw new Error('请先配置API密钥')
      }

      api.setApiKey(provider.apiKey)
      api.setBaseURL(provider.baseUrl || provider.defaultBaseUrl)

      // 测试连接并获取模型列表
      const testResult = await api.testConnection()

      if (testResult.success) {
        // 获取模型列表
        const modelsResult = await api.getModels()
        if (modelsResult.success) {
          provider.availableModels = modelsResult.data.map(model => model.id)
          ElMessage.success(`${provider.name} 连接成功，获取到 ${modelsResult.data.length} 个模型`)
        } else {
          ElMessage.success(`${provider.name} 连接成功，但获取模型列表失败`)
        }

        provider.status = 'active'
        provider.lastTested = new Date()
      } else {
        throw new Error(testResult.message)
      }
    } else {
      // 其他提供商的模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000))

      const success = Math.random() > 0.3

      if (success) {
        provider.status = 'active'
        provider.lastTested = new Date()
        ElMessage.success(`${provider.name} 连接测试成功`)
      } else {
        provider.status = 'error'
        ElMessage.error(`${provider.name} 连接测试失败`)
      }
    }
  } catch (error) {
    provider.status = 'error'
    ElMessage.error(`${provider.name} 测试失败: ${error.message}`)
  } finally {
    testingProviders.value.delete(provider.id)
  }
}

const testConfiguration = async () => {
  if (!configForm.apiKey) {
    ElMessage.warning('请输入API密钥')
    return
  }
  
  testingConfig.value = true
  
  try {
    // 模拟测试配置
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const success = Math.random() > 0.2
    
    if (success) {
      ElMessage.success('配置测试成功！')
    } else {
      ElMessage.error('配置测试失败，请检查API密钥和地址')
    }
  } catch (error) {
    ElMessage.error(`测试失败: ${error.message}`)
  } finally {
    testingConfig.value = false
  }
}

const saveConfiguration = async () => {
  if (!configForm.apiKey) {
    ElMessage.warning('请输入API密钥')
    return
  }

  savingConfig.value = true

  try {
    // 对于 Targon API，先获取模型列表
    if (selectedProvider.value && selectedProvider.value.id === 'targon') {
      const targonApi = await import('@/services/targonApi')
      const api = targonApi.default

      api.setApiKey(configForm.apiKey)
      api.setBaseURL(configForm.baseUrl)

      try {
        const modelsResult = await api.getModels()
        if (modelsResult.success) {
          selectedProvider.value.availableModels = modelsResult.data.map(model => model.id)
          ElMessage.success(`获取到 ${modelsResult.data.length} 个可用模型`)
        }
      } catch (error) {
        console.warn('获取模型列表失败:', error)
      }
    }

    // 保存配置
    await new Promise(resolve => setTimeout(resolve, 500))

    // 更新提供商配置
    if (selectedProvider.value) {
      selectedProvider.value.apiKey = configForm.apiKey
      selectedProvider.value.baseUrl = configForm.baseUrl
      selectedProvider.value.models = configForm.models
      selectedProvider.value.timeout = configForm.timeout
      selectedProvider.value.maxRetries = configForm.maxRetries
      selectedProvider.value.configured = true
      selectedProvider.value.status = 'active'

      // 对于 Targon，将 API 密钥保存到 localStorage
      if (selectedProvider.value.id === 'targon') {
        localStorage.setItem('targon_api_key', configForm.apiKey)
        localStorage.setItem('targon_base_url', configForm.baseUrl)
      }
    }

    ElMessage.success('配置保存成功！')
    showConfigDialog.value = false
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    savingConfig.value = false
  }
}

const addProvider = () => {
  if (!addProviderForm.name || !addProviderForm.type) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  const newProvider = {
    id: `custom-${Date.now()}`,
    name: addProviderForm.name,
    description: `自定义${addProviderForm.type}提供商`,
    type: addProviderForm.type,
    defaultBaseUrl: addProviderForm.baseUrl,
    configured: false,
    availableModels: [],
    timeout: 60,
    maxRetries: 3,
    status: 'inactive'
  }
  
  providers.value.push(newProvider)
  
  // 重置表单
  addProviderForm.name = ''
  addProviderForm.type = ''
  addProviderForm.baseUrl = ''
  
  showAddProviderDialog.value = false
  ElMessage.success('提供商添加成功！')
}

// 初始化 Targon 配置
const initTargonConfig = () => {
  const targonProvider = providers.value.find(p => p.id === 'targon')
  if (targonProvider) {
    const savedApiKey = localStorage.getItem('targon_api_key')
    const savedBaseUrl = localStorage.getItem('targon_base_url')

    if (savedApiKey) {
      targonProvider.apiKey = savedApiKey
      targonProvider.configured = true
      targonProvider.status = 'active'
    }

    if (savedBaseUrl) {
      targonProvider.baseUrl = savedBaseUrl
    }
  }
}

// 生命周期
onMounted(() => {
  // 初始化 Targon 配置
  initTargonConfig()

  // 初始化时选择第一个提供商
  if (providers.value.length > 0) {
    selectedProvider.value = providers.value[0]
  }
})
</script>

<style scoped>
.api-key-config {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.config-header {
  text-align: center;
  margin-bottom: 30px;
}

.config-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.config-subtitle {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.providers-list {
  display: grid;
  gap: 16px;
  margin-bottom: 20px;
}

.provider-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.provider-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.provider-card.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.provider-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #f5f7fa;
  flex-shrink: 0;
}

.provider-icon.large {
  width: 64px;
  height: 64px;
}

.provider-icon img {
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

.provider-letter {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.provider-info {
  flex: 1;
  min-width: 0;
}

.provider-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.provider-description {
  font-size: 14px;
  color: #606266;
  margin: 0 0 8px 0;
}

.provider-status {
  display: flex;
  align-items: center;
}

.provider-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.add-provider {
  text-align: center;
  padding: 20px;
}

.add-provider-btn {
  padding: 12px 24px;
  font-size: 16px;
}

.config-form {
  padding: 0;
}

.provider-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.provider-details {
  margin-left: 16px;
}

.provider-details h3 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.provider-details p {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.config-form-content {
  margin-top: 20px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.full-width {
  width: 100%;
}

.input-suffix {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .api-key-config {
    padding: 10px;
  }
  
  .provider-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .provider-icon {
    margin-right: 0;
  }
  
  .provider-actions {
    justify-content: center;
  }
  
  .provider-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .provider-details {
    margin-left: 0;
  }
}
</style>
