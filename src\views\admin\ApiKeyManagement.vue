<template>
  <AdminGuard>
    <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">API密钥管理中心</h1>
              <p class="mt-1 text-sm text-gray-500">
                管理OpenRouter API密钥，配置轮询策略和监控系统状态
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <el-badge :value="activeKeysCount" :max="99" class="mr-2">
                <el-button type="success" :icon="Plus" @click="showAddDialog = true">
                  添加密钥
                </el-button>
              </el-badge>
              <el-button type="primary" :icon="Upload" @click="showBatchImportDialog = true">
                批量导入
              </el-button>
              <el-button
                type="warning"
                :icon="Operation"
                :loading="batchTesting"
                @click="batchTestAllKeys"
              >
                一键测试
              </el-button>
              <el-button
                type="primary"
                :icon="Refresh"
                :loading="refreshing"
                @click="refreshAllData"
              >
                刷新数据
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 系统状态概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <el-icon class="text-blue-600"><Key /></el-icon>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">总密钥数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ totalKeysCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <el-icon class="text-green-600"><CircleCheckFilled /></el-icon>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">可用密钥</p>
              <p class="text-2xl font-semibold text-gray-900">{{ activeKeysCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <el-icon class="text-yellow-600"><Timer /></el-icon>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">平均响应时间</p>
              <p class="text-2xl font-semibold text-gray-900">{{ averageResponseTime }}ms</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <el-icon class="text-purple-600"><TrendCharts /></el-icon>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">总成功率</p>
              <p class="text-2xl font-semibold text-gray-900">{{ overallSuccessRate }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统配置 -->
      <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">系统配置</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                密钥轮询
              </label>
              <el-switch
                v-model="systemConfig.keyRotationEnabled"
                @change="updateSystemConfig"
                active-text="启用"
                inactive-text="禁用"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                失败阈值
              </label>
              <el-input-number
                v-model="systemConfig.failureThreshold"
                @change="updateSystemConfig"
                :min="1"
                :max="10"
                size="small"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                健康检查间隔 (分钟)
              </label>
              <el-input-number
                v-model="healthCheckIntervalMinutes"
                @change="updateHealthCheckInterval"
                :min="1"
                :max="60"
                size="small"
              />
            </div>
          </div>
          
          <div class="mt-6 flex space-x-3">
            <button
              class="btn btn-warning"
              :disabled="healthCheckLoading"
              @click="performHealthCheck"
            >
              {{ healthCheckLoading ? '检查中...' : '执行健康检查' }}
            </button>
            <button
              class="btn btn-info"
              @click="rotateCurrentKey"
            >
              手动轮换密钥
            </button>
            <button
              class="btn btn-danger"
              @click="resetAllKeys"
            >
              重置所有密钥
            </button>
          </div>
        </div>
      </div>

      <!-- 密钥列表 -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">API密钥列表</h2>
            <div class="flex items-center space-x-2">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索密钥..."
                size="small"
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  密钥信息
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  使用统计
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  性能指标
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后活动
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr 
                v-for="(keyInfo, index) in filteredKeys" 
                :key="index"
                :class="{ 'bg-blue-50': keyInfo.isCurrent }"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">#{{ keyInfo.index }}</span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ maskApiKey(keyInfo.keyPreview || 'sk-or-v1-***') }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ keyInfo.isCurrent ? '当前使用' : '备用密钥' }}
                      </div>
                    </div>
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    keyInfo.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  ]">
                    {{ keyInfo.isActive ? '正常' : '异常' }}
                  </span>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>总请求: {{ keyInfo.totalRequests || 0 }}</div>
                  <div>成功: {{ keyInfo.successfulRequests || 0 }}</div>
                  <div>失败: {{ keyInfo.failureCount || 0 }}</div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>成功率: {{ keyInfo.successRate || '0.0' }}%</div>
                  <div>响应时间: {{ keyInfo.responseTime || 0 }}ms</div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div v-if="keyInfo.lastUsed">
                    {{ formatDateTime(keyInfo.lastUsed) }}
                  </div>
                  <div v-else class="text-gray-400">从未使用</div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="action-buttons">
                    <button
                      v-if="!keyInfo.isCurrent"
                      class="btn btn-primary"
                      @click="switchToKey(index)"
                    >
                      切换
                    </button>
                    <button
                      class="btn btn-info"
                      @click="testKey(index)"
                      :disabled="keyInfo.testing"
                    >
                      {{ keyInfo.testing ? '测试中...' : '测试' }}
                    </button>
                    <button
                      class="btn btn-danger"
                      @click="confirmDeleteKey(index)"
                      :disabled="totalKeysCount <= 1"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <div v-if="filteredKeys.length === 0" class="text-center py-12">
            <el-icon class="text-gray-400 text-4xl mb-4"><Key /></el-icon>
            <p class="text-gray-500">暂无API密钥</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加密钥对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加API密钥"
      width="500px"
      :before-close="handleCloseAddDialog"
    >
      <el-form :model="newKeyForm" :rules="keyRules" ref="keyFormRef" label-width="100px">
        <el-form-item label="密钥名称" prop="name">
          <el-input
            v-model="newKeyForm.name"
            placeholder="为密钥设置一个易识别的名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="key">
          <el-input
            v-model="newKeyForm.key"
            type="password"
            placeholder="sk-or-v1-..."
            show-password
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="newKeyForm.description"
            type="textarea"
            placeholder="可选：描述此密钥的用途或来源"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="newKeyForm.priority" placeholder="选择优先级">
            <el-option label="高优先级" :value="1" />
            <el-option label="普通优先级" :value="2" />
            <el-option label="低优先级" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="handleCloseAddDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="addNewKey"
            :loading="addingKey"
          >
            添加密钥
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showBatchImportDialog"
      title="批量导入API密钥"
      width="600px"
      :before-close="handleCloseBatchImportDialog"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            API密钥列表
          </label>
          <el-input
            v-model="batchImportText"
            type="textarea"
            :rows="8"
            placeholder="请输入 OpenAI API KEY，多个以英文逗号、分号或换行分隔

示例格式：
sk-xxx1,sk-xxx2
sk-xxx3;sk-xxx4
sk-xxx5"
            class="w-full"
          />
        </div>

        <div class="text-sm text-gray-500">
          <p>支持的分隔符：英文逗号(,)、分号(;)、换行符</p>
          <p>将自动过滤空行和重复密钥</p>
        </div>

        <div v-if="parsedKeys.length > 0" class="bg-blue-50 p-3 rounded">
          <p class="text-sm text-blue-700 font-medium">
            检测到 {{ parsedKeys.length }} 个有效密钥
          </p>
          <div class="mt-2 max-h-32 overflow-y-auto">
            <div v-for="(key, index) in parsedKeys" :key="index" class="text-xs text-blue-600">
              {{ index + 1 }}. {{ key.substring(0, 10) }}...{{ key.substring(key.length - 4) }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="handleCloseBatchImportDialog">取消</el-button>
          <el-button
            type="primary"
            @click="performBatchImport"
            :loading="batchImporting"
            :disabled="parsedKeys.length === 0"
          >
            导入 {{ parsedKeys.length }} 个密钥
          </el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </AdminGuard>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createConfetti, celebrateBatchOperation } from '@/utils/celebration'
import {
  Plus, Refresh, Key, CircleCheckFilled, Timer, TrendCharts,
  Search, Upload, Operation
} from '@element-plus/icons-vue'
import AdminGuard from '@/components/admin/AdminGuard.vue'

// 导入API服务
import {
  rotateApiKey,
  performHealthCheck as performApiHealthCheck,
  resetAllApiKeys
} from '@/services/openrouterApi.js'
import { apiKeyStorage } from '@/services/apiKeyStorage.js'

// 响应式数据
const refreshing = ref(false)
const healthCheckLoading = ref(false)
const addingKey = ref(false)
const showAddDialog = ref(false)
const searchKeyword = ref('')

// 批量导入相关
const showBatchImportDialog = ref(false)
const batchImportText = ref('')
const batchImporting = ref(false)

// 批量测试相关
const batchTesting = ref(false)

const keyStatsList = ref([])
const systemConfig = ref({
  keyRotationEnabled: true,
  failureThreshold: 3,
  healthCheckInterval: 300000
})

const healthCheckIntervalMinutes = ref(5)

const newKeyForm = ref({
  name: '',
  key: '',
  description: '',
  priority: 2
})

const keyFormRef = ref(null)

// 表单验证规则
const keyRules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' },
    { pattern: /^sk-or-v1-/, message: '请输入有效的OpenRouter API密钥', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const totalKeysCount = computed(() => {
  const stats = apiKeyStorage.getStats()
  return stats.totalKeys
})

const activeKeysCount = computed(() => {
  const stats = apiKeyStorage.getStats()
  return stats.activeKeys
})

const averageResponseTime = computed(() => {
  const stats = apiKeyStorage.getStats()
  return stats.averageResponseTime
})

const overallSuccessRate = computed(() => {
  const stats = apiKeyStorage.getStats()
  return stats.overallSuccessRate
})

const filteredKeys = computed(() => {
  if (!searchKeyword.value) return keyStatsList.value

  return keyStatsList.value.filter(key => {
    const keyword = searchKeyword.value.toLowerCase()
    return (
      key.index.toString().includes(keyword) ||
      (key.keyPreview && key.keyPreview.toLowerCase().includes(keyword))
    )
  })
})

// 解析批量导入的密钥
const parsedKeys = computed(() => {
  if (!batchImportText.value.trim()) return []

  // 使用多种分隔符分割
  const keys = batchImportText.value
    .split(/[,;\n\r]+/)
    .map(key => key.trim())
    .filter(key => key.length > 0)
    .filter(key => key.startsWith('sk-'))

  // 去重
  return [...new Set(keys)]
})

// 方法
const refreshAllData = async () => {
  refreshing.value = true
  try {
    // 使用存储服务获取数据
    keyStatsList.value = apiKeyStorage.getAllKeys()
    systemConfig.value = apiKeyStorage.getConfig()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('刷新数据失败: ' + error.message)
  } finally {
    refreshing.value = false
  }
}

const updateSystemConfig = () => {
  try {
    apiKeyStorage.updateConfig(systemConfig.value)
  } catch (error) {
    console.error('更新配置失败:', error)
  }
}

const updateHealthCheckInterval = () => {
  systemConfig.value.healthCheckInterval = healthCheckIntervalMinutes.value * 60 * 1000
  updateSystemConfig()
}

const performHealthCheck = async () => {
  healthCheckLoading.value = true
  try {
    await performApiHealthCheck()
    ElMessage.success('健康检查完成')
    await refreshAllData()
  } catch (error) {
    ElMessage.error('健康检查失败: ' + error.message)
  } finally {
    healthCheckLoading.value = false
  }
}

const rotateCurrentKey = () => {
  try {
    rotateApiKey()
    ElMessage.success('密钥轮换成功')
    refreshAllData()
  } catch (error) {
    ElMessage.error('密钥轮换失败: ' + error.message)
  }
}

const resetAllKeys = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将重置所有密钥的状态，是否继续？',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    resetAllApiKeys()
    ElMessage.success('所有密钥状态已重置')
    await refreshAllData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败: ' + error.message)
    }
  }
}

const maskApiKey = (key) => {
  if (!key || key.length < 10) return key
  return key.substring(0, 10) + '***' + key.substring(key.length - 4)
}

const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const switchToKey = (index) => {
  try {
    const key = keyStatsList.value[index]
    if (key) {
      apiKeyStorage.switchToKey(key.id)
      refreshAllData()
    }
  } catch (error) {
    console.error('切换密钥失败:', error)
  }
}

const testKey = async (index) => {
  try {
    const key = keyStatsList.value[index]
    if (key) {
      // 设置测试状态
      key.testing = true
      ElMessage.info(`正在测试 ${key.name}...`)

      await apiKeyStorage.testKey(key.id)
      await refreshAllData()
    }
  } catch (error) {
    console.error('测试密钥失败:', error)
  } finally {
    // 清除测试状态
    const key = keyStatsList.value[index]
    if (key) {
      key.testing = false
    }
  }
}

const confirmDeleteKey = async (index) => {
  try {
    const key = keyStatsList.value[index]
    if (!key) return

    await ElMessageBox.confirm(
      `确定要删除密钥 "${key.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    apiKeyStorage.removeKey(key.id)
    await refreshAllData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除密钥失败:', error)
    }
  }
}

const addNewKey = async () => {
  if (!keyFormRef.value) return

  try {
    await keyFormRef.value.validate()
    addingKey.value = true

    // 使用存储服务添加密钥
    apiKeyStorage.addKey(newKeyForm.value)

    showAddDialog.value = false
    resetForm()
    await refreshAllData()
  } catch (error) {
    console.error('添加密钥失败:', error)
  } finally {
    addingKey.value = false
  }
}

const handleCloseAddDialog = () => {
  showAddDialog.value = false
  resetForm()
}

const resetForm = () => {
  newKeyForm.value = {
    name: '',
    key: '',
    description: '',
    priority: 2
  }
  if (keyFormRef.value) {
    keyFormRef.value.resetFields()
  }
}

// 批量导入相关方法
const handleCloseBatchImportDialog = () => {
  showBatchImportDialog.value = false
  batchImportText.value = ''
}

const performBatchImport = async () => {
  if (parsedKeys.value.length === 0) {
    ElMessage.warning('请输入有效的API密钥')
    return
  }

  try {
    batchImporting.value = true
    let successCount = 0
    let failCount = 0

    for (const [index, key] of parsedKeys.value.entries()) {
      try {
        // 检查密钥是否已存在
        const existingKeys = apiKeyStorage.getAllKeys()
        const exists = existingKeys.some(existingKey => existingKey.key === key)

        if (exists) {
          console.warn(`密钥已存在，跳过: ${key.substring(0, 10)}...`)
          failCount++
          continue
        }

        // 添加密钥
        apiKeyStorage.addKey({
          name: `批量导入密钥 ${index + 1}`,
          key: key,
          description: `批量导入于 ${new Date().toLocaleString()}`,
          priority: 2
        })

        successCount++
      } catch (error) {
        console.error(`添加密钥失败: ${key.substring(0, 10)}...`, error)
        failCount++
      }
    }

    // 显示结果
    if (successCount > 0) {
      ElMessage.success(`成功导入 ${successCount} 个密钥${failCount > 0 ? `，${failCount} 个失败` : ''}`)
    } else {
      ElMessage.error(`导入失败，${failCount} 个密钥无法添加`)
    }

    // 关闭对话框并刷新数据
    handleCloseBatchImportDialog()
    await refreshAllData()

  } catch (error) {
    console.error('批量导入失败:', error)
    ElMessage.error('批量导入过程中发生错误')
  } finally {
    batchImporting.value = false
  }
}

// 一键测试所有密钥
const batchTestAllKeys = async () => {
  if (keyStatsList.value.length === 0) {
    ElMessage.warning('没有可测试的密钥')
    return
  }

  try {
    batchTesting.value = true
    let successCount = 0
    let failCount = 0
    const totalKeys = keyStatsList.value.length

    ElMessage.info(`开始测试 ${totalKeys} 个密钥...`)

    for (const [index, key] of keyStatsList.value.entries()) {
      try {
        ElMessage.info(`正在测试密钥 ${index + 1}/${totalKeys}: ${key.name}`)

        const result = await apiKeyStorage.testKey(key.id)

        if (result.success) {
          successCount++
        } else {
          failCount++
        }

        // 每测试一个密钥后稍微延迟，避免请求过于频繁
        if (index < keyStatsList.value.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        console.error(`测试密钥 ${key.name} 失败:`, error)
        failCount++
      }
    }

    // 显示测试结果和庆祝动画
    if (successCount > 0 && failCount === 0) {
      // 全部成功时触发庆祝动画
      createConfetti({ count: 30, duration: 3 })
      ElMessage.success(`🎉 所有 ${successCount} 个密钥测试成功！`)
    } else if (successCount > 0) {
      // 部分成功时使用庆祝函数
      celebrateBatchOperation(successCount, successCount + failCount, '测试', 'API密钥')
    } else {
      ElMessage.error(`所有 ${failCount} 个密钥测试失败`)
    }

    // 刷新数据显示最新状态
    await refreshAllData()

  } catch (error) {
    console.error('批量测试失败:', error)
    ElMessage.error('批量测试过程中发生错误')
  } finally {
    batchTesting.value = false
  }
}



// 生命周期
onMounted(() => {
  refreshAllData()
})
</script>

<style scoped>
/* 组件样式 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

/* 自定义按钮样式 */
.btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
  text-align: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-info {
  background-color: #909399;
  border-color: #909399;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

.btn-danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #f78989;
  border-color: #f78989;
}

.btn-warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #ebb563;
  border-color: #ebb563;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .min-h-screen {
    padding: 1rem;
  }

  .max-w-7xl {
    max-width: 100%;
    padding: 0;
  }

  .py-6 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .px-4 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* 头部区域 */
  .flex.items-center.justify-between {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  /* 统计卡片网格 */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* 卡片内容 */
  .bg-white.rounded-lg.shadow-sm.p-6 {
    padding: 1rem;
  }

  /* 表格容器 */
  .overflow-x-auto {
    margin: 0 -0.5rem;
  }

  /* 表格样式 */
  .min-w-full {
    font-size: 0.875rem;
  }

  .px-6.py-4 {
    padding: 0.5rem;
  }

  .px-6.py-3 {
    padding: 0.5rem;
  }

  /* 操作按钮 */
  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;

    .btn {
      width: 100%;
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  }

  /* 批量操作区域 */
  .flex.flex-wrap.gap-4 {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* 搜索框 */
  .relative.flex-1 {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .min-h-screen {
    padding: 0.5rem;
  }

  /* 统计卡片单列显示 */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
    grid-template-columns: 1fr;
  }

  /* 卡片更紧凑 */
  .bg-white.rounded-lg.shadow-sm.p-6 {
    padding: 0.75rem;
  }

  /* 表格更紧凑 */
  .px-6.py-4,
  .px-6.py-3 {
    padding: 0.25rem;
  }

  .min-w-full {
    font-size: 0.75rem;
  }

  /* 隐藏部分列 */
  .hidden.md\\:table-cell {
    display: none !important;
  }

  /* 操作按钮更小 */
  .action-buttons .btn {
    font-size: 0.625rem;
    padding: 0.125rem 0.25rem;
    min-width: auto;
  }
}


</style>
