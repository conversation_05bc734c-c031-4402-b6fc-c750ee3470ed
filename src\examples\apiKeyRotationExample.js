// API密钥轮询使用示例
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService.js'
import { 
  getApiKeyStats, 
  getApiConfig, 
  addApiKey, 
  rotateApiKey, 
  performHealthCheck 
} from '@/services/openrouterApi.js'

// 示例1: 基本聊天 - 密钥轮询完全透明
export async function basicChatExample() {
  console.log('=== 基本聊天示例 ===')
  
  try {
    const messages = [
      { role: 'user', content: '你好，请介绍一下你自己' }
    ]
    
    const response = await generateChatCompletion(messages, {
      model: 'deepseek/deepseek-chat-v3-0324:free',
      temperature: 0.7,
      max_tokens: 200
    })
    
    console.log('AI回复:', response.content)
    console.log('使用模型:', response.model)
    
    // 查看当前密钥统计
    const stats = getApiKeyStats()
    console.log('当前密钥统计:', stats)
    
  } catch (error) {
    console.error('聊天失败:', error.message)
  }
}

// 示例2: 流式聊天 - 支持密钥轮询
export async function streamChatExample() {
  console.log('=== 流式聊天示例 ===')
  
  try {
    const messages = [
      { role: 'user', content: '请写一首关于AI的诗' }
    ]
    
    let fullResponse = ''
    
    await generateChatCompletionStream(messages, {
      model: 'deepseek/deepseek-r1:free',
      temperature: 0.8,
      max_tokens: 300
    }, (chunk) => {
      process.stdout.write(chunk)
      fullResponse += chunk
    })
    
    console.log('\n完整回复长度:', fullResponse.length)
    
  } catch (error) {
    console.error('流式聊天失败:', error.message)
  }
}

// 示例3: 密钥管理操作
export async function keyManagementExample() {
  console.log('=== 密钥管理示例 ===')
  
  try {
    // 获取当前配置
    const config = getApiConfig()
    console.log('API配置:', {
      totalKeys: config.totalKeys,
      activeKeys: config.activeKeys,
      keyRotationEnabled: config.keyRotationEnabled,
      healthCheckInterval: config.healthCheckInterval
    })
    
    // 获取密钥统计
    const stats = getApiKeyStats()
    console.log('密钥统计:')
    stats.forEach(keyStats => {
      console.log(`  密钥 #${keyStats.index}:`, {
        isActive: keyStats.isActive,
        isCurrent: keyStats.isCurrent,
        successRate: keyStats.successRate + '%',
        totalRequests: keyStats.totalRequests,
        failureCount: keyStats.failureCount
      })
    })
    
    // 手动轮换密钥
    console.log('执行密钥轮换...')
    rotateApiKey()
    
    // 再次查看统计
    const newStats = getApiKeyStats()
    const currentKey = newStats.find(key => key.isCurrent)
    console.log('轮换后当前密钥:', `#${currentKey.index}`)
    
  } catch (error) {
    console.error('密钥管理操作失败:', error.message)
  }
}

// 示例4: 健康检查
export async function healthCheckExample() {
  console.log('=== 健康检查示例 ===')
  
  try {
    console.log('开始健康检查...')
    await performHealthCheck()
    console.log('健康检查完成')
    
    // 查看检查后的状态
    const stats = getApiKeyStats()
    console.log('健康检查后的密钥状态:')
    stats.forEach(keyStats => {
      console.log(`  密钥 #${keyStats.index}:`, {
        isActive: keyStats.isActive,
        lastSuccess: keyStats.lastSuccess,
        lastFailure: keyStats.lastFailure,
        failureCount: keyStats.failureCount
      })
    })
    
  } catch (error) {
    console.error('健康检查失败:', error.message)
  }
}

// 示例5: 故障模拟和恢复
export async function failureRecoveryExample() {
  console.log('=== 故障恢复示例 ===')
  
  try {
    // 连续发送多个请求，观察密钥轮换
    console.log('发送连续请求，观察密钥轮换...')
    
    for (let i = 0; i < 5; i++) {
      console.log(`\n--- 请求 ${i + 1} ---`)
      
      try {
        const messages = [
          { role: 'user', content: `这是第${i + 1}个测试请求` }
        ]
        
        const startTime = Date.now()
        const response = await generateChatCompletion(messages, {
          model: 'deepseek/deepseek-chat-v3-0324:free',
          max_tokens: 50
        })
        const responseTime = Date.now() - startTime
        
        console.log('请求成功，响应时间:', responseTime + 'ms')
        console.log('回复长度:', response.content.length)
        
        // 查看当前使用的密钥
        const stats = getApiKeyStats()
        const currentKey = stats.find(key => key.isCurrent)
        console.log('当前密钥:', `#${currentKey.index}`)
        
      } catch (error) {
        console.log('请求失败:', error.message)
        
        // 查看失败后的密钥状态
        const stats = getApiKeyStats()
        stats.forEach(keyStats => {
          if (keyStats.failureCount > 0) {
            console.log(`密钥 #${keyStats.index} 失败次数: ${keyStats.failureCount}`)
          }
        })
      }
      
      // 等待一秒再发送下一个请求
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
  } catch (error) {
    console.error('故障恢复测试失败:', error.message)
  }
}

// 示例6: 性能对比测试
export async function performanceComparisonExample() {
  console.log('=== 性能对比示例 ===')
  
  try {
    const testMessage = '请简单介绍一下人工智能'
    const testCount = 3
    
    // 测试不同模型的性能
    const models = [
      'deepseek/deepseek-chat-v3-0324:free',
      'qwen/qwen3-coder:free',
      'meta-llama/llama-3.3-70b-instruct:free'
    ]
    
    for (const model of models) {
      console.log(`\n测试模型: ${model}`)
      
      const times = []
      let successCount = 0
      
      for (let i = 0; i < testCount; i++) {
        try {
          const startTime = Date.now()
          
          await generateChatCompletion([
            { role: 'user', content: testMessage }
          ], {
            model: model,
            max_tokens: 100
          })
          
          const responseTime = Date.now() - startTime
          times.push(responseTime)
          successCount++
          
          console.log(`  请求 ${i + 1}: ${responseTime}ms`)
          
        } catch (error) {
          console.log(`  请求 ${i + 1}: 失败 - ${error.message}`)
        }
        
        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      
      if (times.length > 0) {
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length
        console.log(`  平均响应时间: ${avgTime.toFixed(0)}ms`)
        console.log(`  成功率: ${(successCount / testCount * 100).toFixed(1)}%`)
      }
    }
    
    // 显示最终的密钥统计
    console.log('\n最终密钥统计:')
    const finalStats = getApiKeyStats()
    finalStats.forEach(keyStats => {
      console.log(`密钥 #${keyStats.index}:`, {
        总请求: keyStats.totalRequests,
        成功率: keyStats.successRate + '%',
        平均响应时间: keyStats.responseTime + 'ms'
      })
    })
    
  } catch (error) {
    console.error('性能对比测试失败:', error.message)
  }
}

// 运行所有示例
export async function runAllExamples() {
  console.log('🚀 开始API密钥轮询功能演示\n')
  
  await basicChatExample()
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await streamChatExample()
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await keyManagementExample()
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await healthCheckExample()
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await failureRecoveryExample()
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await performanceComparisonExample()
  
  console.log('\n✅ 所有示例演示完成！')
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples().catch(console.error)
}
