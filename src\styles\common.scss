// 通用样式类 - 使用现代@use语法
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  border: 1px solid #e2e8f0;

  &:hover {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  }
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

// 布局类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 (-$spacing-sm);
}

.col {
  flex: 1;
  padding: 0 $spacing-sm;
}

// 间距类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-xs { margin: $spacing-xs; }
.mt-xs { margin-top: $spacing-xs; }
.mr-xs { margin-right: $spacing-xs; }
.mb-xs { margin-bottom: $spacing-xs; }
.ml-xs { margin-left: $spacing-xs; }

.m-sm { margin: $spacing-sm; }
.mt-sm { margin-top: $spacing-sm; }
.mr-sm { margin-right: $spacing-sm; }
.mb-sm { margin-bottom: $spacing-sm; }
.ml-sm { margin-left: $spacing-sm; }

.m-md { margin: $spacing-md; }
.mt-md { margin-top: $spacing-md; }
.mr-md { margin-right: $spacing-md; }
.mb-md { margin-bottom: $spacing-md; }
.ml-md { margin-left: $spacing-md; }

.m-lg { margin: $spacing-lg; }
.mt-lg { margin-top: $spacing-lg; }
.mr-lg { margin-right: $spacing-lg; }
.mb-lg { margin-bottom: $spacing-lg; }
.ml-lg { margin-left: $spacing-lg; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-xs { padding: $spacing-xs; }
.pt-xs { padding-top: $spacing-xs; }
.pr-xs { padding-right: $spacing-xs; }
.pb-xs { padding-bottom: $spacing-xs; }
.pl-xs { padding-left: $spacing-xs; }

.p-sm { padding: $spacing-sm; }
.pt-sm { padding-top: $spacing-sm; }
.pr-sm { padding-right: $spacing-sm; }
.pb-sm { padding-bottom: $spacing-sm; }
.pl-sm { padding-left: $spacing-sm; }

.p-md { padding: $spacing-md; }
.pt-md { padding-top: $spacing-md; }
.pr-md { padding-right: $spacing-md; }
.pb-md { padding-bottom: $spacing-md; }
.pl-md { padding-left: $spacing-md; }

.p-lg { padding: $spacing-lg; }
.pt-lg { padding-top: $spacing-lg; }
.pr-lg { padding-right: $spacing-lg; }
.pb-lg { padding-bottom: $spacing-lg; }
.pl-lg { padding-left: $spacing-lg; }

// 文字类
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-info { color: $info-color; }

.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

// 显示类
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

// 位置类
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

// 浮动类
.float-left { float: left; }
.float-right { float: right; }
.float-none { float: none; }

// 宽高类
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// 圆角类
.rounded { border-radius: $border-radius-base; }
.rounded-sm { border-radius: $border-radius-small; }
.rounded-lg { border-radius: $border-radius-round; }
.rounded-circle { border-radius: $border-radius-circle; }

// 阴影类
.shadow { box-shadow: $box-shadow-base; }
.shadow-lg { box-shadow: $box-shadow-lg; }
.shadow-none { box-shadow: none; }

// 边框类
.border { border: 1px solid $border-color-base; }
.border-top { border-top: 1px solid $border-color-base; }
.border-right { border-right: 1px solid $border-color-base; }
.border-bottom { border-bottom: 1px solid $border-color-base; }
.border-left { border-left: 1px solid $border-color-base; }
.border-none { border: none; }

// 背景类
.bg-primary { background-color: $primary-color; }
.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-danger { background-color: $danger-color; }
.bg-info { background-color: $info-color; }
.bg-light { background-color: $bg-color-secondary; }
.bg-white { background-color: $bg-color; }

// AI创作平台特色类
.ai-gradient {
  @include gradient-bg();
}

.chat-container {
  height: calc(100vh - #{$header-height});
  overflow: hidden;
}

.drawing-canvas {
  background-color: $drawing-canvas-bg;
  border: 1px solid $border-color-light;
}

.gallery-card {
  @include card-style();
  box-shadow: $gallery-card-shadow;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
}
