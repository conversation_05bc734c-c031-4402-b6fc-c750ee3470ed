<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆角矩形 -->
  <rect width="512" height="512" rx="100" fill="url(#gradient)"/>

  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="200" fill="white" opacity="0.1"/>

  <!-- AI字母设计 -->
  <g fill="white" font-family="Arial, sans-serif" font-weight="bold">
    <!-- A字母 -->
    <path d="M160 360 L200 210 L240 360 M180 305 L220 305" stroke="white" stroke-width="14" fill="none"/>
    <!-- I字母 -->
    <rect x="290" y="210" width="26" height="150" fill="white"/>
    <rect x="280" y="210" width="46" height="22" fill="white"/>
    <rect x="280" y="338" width="46" height="22" fill="white"/>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="385" cy="125" r="12" fill="rgba(255,255,255,0.8)"/>
  <circle cx="125" cy="125" r="10" fill="rgba(255,255,255,0.6)"/>
  <circle cx="400" cy="385" r="11" fill="rgba(255,255,255,0.7)"/>
</svg>
