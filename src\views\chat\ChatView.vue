<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 chat-mobile-optimized">
    <!-- 移动端侧边栏遮罩层 -->
    <div
      class="sidebar-overlay"
      :class="{ active: !sidebarCollapsed }"
      @click="toggleSidebar"
    ></div>

    <!-- 主布局 -->
    <div class="flex h-screen chat-container">
      <!-- 侧边栏 -->
      <aside
        class="chat-sidebar bg-white border-r border-gray-200 transition-all duration-300 ease-in-out shadow-soft"
        :class="[
          sidebarCollapsed ? 'w-16' : 'w-80',
          { 'visible': !sidebarCollapsed }
        ]"
      >
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
          <button
            @click="toggleSidebar"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-600 hover:text-gray-900"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="transition-transform duration-200" :class="{ 'rotate-180': sidebarCollapsed }">
              <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
          <h1
            v-if="!sidebarCollapsed"
            class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent animate-fade-in"
          >
            AI 智能对话
          </h1>
        </div>

        <!-- 展开状态的侧边栏内容 -->
        <div v-if="!sidebarCollapsed" class="flex flex-col h-full">
          <!-- 模型选择器 -->
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between mb-2">
              <label class="block text-sm font-medium text-gray-700">选择AI模型</label>
              <button
                @click="showApiConfig = true"
                class="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                title="配置API密钥"
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke="currentColor" stroke-width="2"/>
                  <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>配置</span>
              </button>
            </div>

            <!-- 使用新的模型选择器组件 -->
            <ModelSelector
              v-model="selectedModel"
              @model-changed="handleModelChange"
            />

            <!-- 显示当前选中模型的详细信息 -->
            <div v-if="currentModelInfo" class="mt-3 p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span class="text-xs text-white font-bold">{{ currentModelInfo.name.charAt(0) }}</span>
                </div>
                <span class="text-sm font-medium text-gray-900">{{ currentModelInfo.name }}</span>
                <span v-if="currentModelInfo.isFree" class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">免费</span>
              </div>
              <p class="text-xs text-gray-600 mb-2">{{ currentModelInfo.description }}</p>
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span>速度: {{ currentModelInfo.speed || 'N/A' }}/100</span>
                <span>质量: {{ currentModelInfo.quality || 'N/A' }}/5</span>
              </div>
            </div>
          </div>

          <!-- 对话列表 -->
          <div class="flex-1 overflow-y-auto p-4 space-y-2">
            <div class="text-sm font-medium text-gray-500 mb-3">对话历史</div>
            <button
              v-for="conversation in chatStore.conversations"
              :key="conversation.id"
              @click="switchConversation(conversation.id)"
              class="w-full text-left p-3 rounded-lg transition-all duration-200 group"
              :class="conversation.id === chatStore.currentConversationId
                ? 'bg-blue-50 border border-blue-200 text-blue-900 shadow-sm'
                : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium truncate">{{ conversation.title || '新对话' }}</span>
                <svg
                  v-if="conversation.id === chatStore.currentConversationId"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-blue-500 flex-shrink-0"
                >
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ formatTime(conversation.updatedAt) }}
              </div>
            </button>
          </div>

          <!-- 新建对话按钮 -->
          <div class="p-4 border-t border-gray-100">
            <button
              @click="createNewConversation"
              class="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span class="font-medium">新建对话</span>
            </button>
          </div>
        </div>

        <!-- 折叠状态的侧边栏内容 -->
        <div v-else class="flex flex-col items-center py-4 space-y-4">
          <button
            @click="createNewConversation"
            class="p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            title="新建对话"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="chat-main flex-1 flex flex-col bg-white">
        <!-- 聊天头部 -->
        <header class="chat-header flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white/80 backdrop-blur-sm">
          <div class="header-content flex items-center space-x-3">
            <!-- 移动端汉堡菜单按钮 -->
            <button
              @click="toggleSidebar"
              class="mobile-menu-btn p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200 md:hidden"
              title="菜单"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>

            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse-gentle"></div>
            <h2 class="chat-title text-lg font-semibold text-gray-900">
              {{ chatStore.currentConversation?.title || '新对话' }}
            </h2>
            <span class="model-badge px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">
              {{ selectedModel }}
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="清空对话"
              @click="clearConversation"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="设置"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </header>

        <!-- 消息区域 -->
        <section class="flex-1 overflow-hidden">
          <MessageList
            ref="messageListRef"
            :messages="chatStore.value?.currentMessages || []"
            :has-messages="chatStore.value?.hasMessages || false"
            :loading="chatStore.value?.loading || false"
            :suggested-questions="suggestedQuestions"
            :should-auto-scroll="shouldAutoScroll"
            @send-message="sendMessage"
            @copy-message="copyMessage"
            @regenerate-message="regenerateMessage"
            @load-more="loadMoreMessages"
            @scroll-change="handleScrollChange"
          />
        </section>

        <!-- 输入区域 -->
        <footer class="border-t border-gray-200 bg-white/80 backdrop-blur-sm">
          <ChatInput
            ref="chatInputRef"
            :loading="chatStore.value?.loading || false"
            :disabled="false"
            @send-message="sendMessage"
            @start-voice-input="startVoiceInput"
            @file-upload="handleFileUpload"
          />
        </footer>
      </main>
    </div>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="showApiConfig"
      title="API密钥配置"
      width="80%"
      :close-on-click-modal="false"
      class="api-config-dialog"
    >
      <ApiKeyConfig @provider-configured="handleProviderConfigured" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { copyToClipboard } from '@/utils/common'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 导入聊天组件
import ChatSidebar from '@/components/chat/ChatSidebar.vue'
import ChatHeader from '@/components/chat/ChatHeader.vue'
import MessageList from '@/components/chat/MessageList.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import ModelSelector from '@/components/chat/ModelSelector.vue'
import ApiKeyConfig from '@/components/ApiKeyConfig.vue'

// 导入AI模型配置
import { getAIModels, getModelById } from '@/config/aiModels.js'

// 使用响应式引用来安全地初始化 stores
const chatStore = ref(null)
const userStore = ref(null)

// 响应式数据
const sidebarCollapsed = ref(false)
const showSettings = ref(false)
const showApiConfig = ref(false)
const selectedModel = ref('gemini-2.5-flash')
const voiceModeEnabled = ref(false)
const shouldAutoScroll = ref(true)
const aiModels = ref([])
const currentModelInfo = ref(null)

// 组件引用
const messageListRef = ref(null)
const chatInputRef = ref(null)

// 建议问题
const suggestedQuestions = [
  '帮我写一个创意故事',
  '解释一下人工智能的原理',
  '推荐一些学习编程的方法',
  '如何提高工作效率？',
  '分析这个问题的解决方案',
  '帮我制定学习计划',
]

// 计算属性
const currentConversationTitle = computed(() => {
  return chatStore.currentConversation?.title || '新对话'
})

// 方法
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return date.toLocaleDateString()
}

const handleScrollChange = (scrollInfo) => {
  shouldAutoScroll.value = scrollInfo.isNearBottom
}

const loadMoreMessages = async () => {
  if (chatStore.loading || !chatStore.hasMoreMessages) return
  await chatStore.loadMoreMessages()
}

// 加载AI模型
const loadAIModels = async () => {
  try {
    aiModels.value = await getAIModels()
    await updateCurrentModelInfo()
  } catch (error) {
    console.error('加载AI模型失败:', error)
    ElMessage.error('加载AI模型失败')
  }
}

// 更新当前模型信息
const updateCurrentModelInfo = async () => {
  try {
    currentModelInfo.value = await getModelById(selectedModel.value)
  } catch (error) {
    console.error('获取模型信息失败:', error)
  }
}

// 处理模型变更
const handleModelChange = async (modelId) => {
  selectedModel.value = modelId
  await updateCurrentModelInfo()
  ElMessage.success(`已切换到 ${currentModelInfo.value?.name || modelId}`)
}

const clearConversation = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空当前对话吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await chatStore.clearCurrentConversation()
    ElMessage.success('对话已清空')
  } catch (error) {
    // 用户取消操作
  }
}

const handleFileUpload = (files) => {
  console.log('文件上传:', files)
}

const startVoiceInput = (isRecording) => {
  console.log('语音输入:', isRecording)
}

const sendMessage = async (content) => {
  try {
    await chatStore.value.sendMessage(content)
    ElMessage.success('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请重试')
  }
}

const regenerateMessage = async (messageId) => {
  try {
    await chatStore.value.regenerateMessage(messageId)
    ElMessage.success('正在重新生成回答...')
  } catch (error) {
    console.error('重新生成失败:', error)
    ElMessage.error('重新生成失败，请重试')
  }
}

const copyMessage = async (content) => {
  try {
    await copyToClipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const createNewConversation = async () => {
  try {
    chatStore.value.createConversation()
    ElMessage.success('已创建新对话')
  } catch (error) {
    console.error('创建对话失败:', error)
    ElMessage.error('创建对话失败，请重试')
  }
}

const switchConversation = async (conversationId) => {
  try {
    chatStore.value.switchConversation(conversationId)
  } catch (error) {
    console.error('切换对话失败:', error)
    ElMessage.error('切换对话失败，请重试')
  }
}

const handleConversationCommand = async (command, conversationId) => {
  try {
    switch (command) {
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个对话吗？', '确认删除', {
          type: 'warning'
        })
        await chatStore.deleteConversation(conversationId)
        ElMessage.success('对话已删除')
        break
      case 'rename':
        const { value: newTitle } = await ElMessageBox.prompt('请输入新的对话标题', '重命名对话')
        if (newTitle) {
          await chatStore.renameConversation(conversationId, newTitle)
          ElMessage.success('对话已重命名')
        }
        break
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleVoiceMode = () => {
  voiceModeEnabled.value = !voiceModeEnabled.value
  ElMessage.info(voiceModeEnabled.value ? '语音模式已开启' : '语音模式已关闭')
}

// 处理API配置完成
const handleProviderConfigured = (provider) => {
  ElMessage.success(`${provider.name} 配置完成`)
  showApiConfig.value = false
  // 重新加载模型列表
  loadAIModels()
}

const exportChat = async () => {
  try {
    const messages = chatStore.currentMessages
    const content = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n')
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chat-${Date.now()}.txt`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('聊天记录已导出')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const clearCurrentChat = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '确认清空', {
      type: 'warning'
    })
    await chatStore.clearCurrentConversation()
    ElMessage.success('对话已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败，请重试')
    }
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 动态导入 stores
    const { useChatStore } = await import('@/stores/chat')
    const { useUserStore } = await import('@/stores/user')

    chatStore.value = useChatStore()
    userStore.value = useUserStore()

    await chatStore.value.initializeChat()

    // 加载AI模型
    await loadAIModels()

    marked.setOptions({
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value
          } catch (err) {
            console.error('代码高亮失败:', err)
          }
        }
        return hljs.highlightAuto(code).value
      },
      breaks: true,
      gfm: true
    })
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
})

onUnmounted(() => {
  // 清理资源
})

// 监听消息变化
watch(() => chatStore.currentMessages, () => {
  // 消息变化时的处理
}, { deep: true })
</script>

<style lang="scss" scoped>
/* 移动端适配样式 */
@media (max-width: 768px) {
  .min-h-screen {
    height: 100vh;
    overflow: hidden;
  }

  .flex.h-screen {
    height: 100vh;
    position: relative;
  }

  /* 侧边栏移动端适配 */
  aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    width: 85vw !important; // 使用视口宽度，最大85%
    max-width: 320px !important; // 最大宽度限制
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

    /* 当侧边栏展开时（sidebarCollapsed为false） */
    &.w-80 {
      transform: translateX(0);
    }

    /* 当侧边栏折叠时（sidebarCollapsed为true） */
    &.w-16 {
      transform: translateX(-100%);
    }
  }

  /* 移动端汉堡菜单按钮 */
  .mobile-menu-btn {
    display: flex !important;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    margin-right: 0.5rem;
  }

  /* 主内容区域 */
  .flex-1 {
    width: 100vw;
    margin-left: 0;
    padding: 0;
    position: relative;
  }

  /* 聊天主区域 */
  main {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* 聊天头部移动端优化 */
  header {
    padding: 0.75rem 1rem; // 减少头部内边距

    .flex.items-center.space-x-3 {
      gap: 0.5rem; // 减少元素间距

      h2 {
        font-size: 1rem; // 稍小标题
      }

      span {
        font-size: 0.7rem; // 更小的模型标签
        padding: 0.25rem 0.5rem;
      }
    }

    .flex.items-center.space-x-2 {
      gap: 0.25rem; // 减少按钮间距

      button {
        width: 36px; // 确保触摸目标足够大
        height: 36px;
        padding: 0.5rem;
      }
    }
  }

  /* 聊天区域 */
  .flex.flex-col.h-full {
    height: 100vh;
  }

  /* 消息列表 */
  .flex-1.overflow-y-auto {
    padding: 1rem 0.5rem;
  }

  /* 消息气泡 */
  .max-w-3xl {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  /* 输入区域 */
  .border-t.bg-white {
    padding: 0.75rem 0.5rem;
  }

  .max-w-4xl {
    max-width: 100%;
  }

  /* 输入框 */
  .flex.items-end.space-x-2 {
    gap: 0.5rem;
  }

  .flex-1.min-h-\\[44px\\] {
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 按钮组 */
  .flex.space-x-1 {
    gap: 0.25rem;
  }

  .w-10.h-10 {
    width: 2rem;
    height: 2rem;
  }

  /* 模型选择器 */
  .w-full.px-3.py-2 {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 对话历史 */
  .space-y-2 {
    gap: 0.5rem;
  }

  /* 工具栏按钮 */
  .flex.items-center.space-x-2 {
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  /* 侧边栏超小屏幕优化 */
  aside {
    width: 95vw !important; // 超小屏幕占95%宽度
    max-width: 300px !important;
  }

  /* 移动端汉堡菜单按钮优化 */
  .mobile-menu-btn {
    padding: 0.375rem;
    width: 36px;
    height: 36px;
    margin-right: 0.375rem;
  }

  /* 头部布局优化 */
  header {
    padding: 0.75rem 1rem;

    .flex.items-center.space-x-3 {
      gap: 0.5rem;
      flex-wrap: wrap;

      h2 {
        font-size: 1rem;
        line-height: 1.2;
      }

      span {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
      }
    }
  }

  /* 更小屏幕的优化 */
  .p-4 {
    padding: 0.75rem;
  }

  .p-6 {
    padding: 1rem;
  }

  /* 聊天头部超小屏幕优化 */
  header {
    padding: 0.5rem 0.75rem; // 更紧凑的头部内边距

    .flex.items-center.space-x-3 {
      gap: 0.375rem; // 进一步减少间距

      h2 {
        font-size: 0.9rem; // 更小标题
      }

      span {
        font-size: 0.65rem; // 更小的模型标签
        padding: 0.2rem 0.4rem;
      }

      .w-3.h-3 {
        width: 0.625rem; // 更小的状态指示器
        height: 0.625rem;
      }
    }

    .flex.items-center.space-x-2 {
      gap: 0.125rem; // 进一步减少按钮间距

      button {
        width: 32px; // 超小屏幕稍小按钮
        height: 32px;
        padding: 0.375rem;
      }
    }
  }

  /* 消息气泡更紧凑 */
  .rounded-2xl {
    border-radius: 1rem;
  }

  .p-4.max-w-\\[85\\%\\] {
    padding: 0.75rem;
    max-width: 90%;
  }

  /* 输入区域更紧凑 */
  .border-t.bg-white {
    padding: 0.5rem;
  }

  /* 按钮更小 */
  .w-10.h-10 {
    width: 1.75rem;
    height: 1.75rem;
  }

  /* 文字大小调整 */
  .text-xl {
    font-size: 1.125rem;
  }

  .text-lg {
    font-size: 1rem;
  }

  /* 侧边栏更窄 */
  aside {
    width: 260px !important;
  }
}

/* 侧边栏遮罩层 */
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }
}

/* 确保移动端输入框不会被虚拟键盘遮挡 */
@media (max-width: 768px) {
  .chat-input-container {
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 100;
  }

  /* iOS Safari 适配 */
  @supports (-webkit-touch-callout: none) {
    .chat-input-container {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

/* 滚动条优化 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* API配置对话框样式 */
:deep(.api-config-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
