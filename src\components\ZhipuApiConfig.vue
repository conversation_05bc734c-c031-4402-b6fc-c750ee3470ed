<!--
  智谱 AI API 配置组件
  用于配置智谱官方 API Key
-->
<template>
  <div class="zhipu-api-config">
    <el-card class="config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <h3>🧠 智谱 AI 官方 API</h3>
            <p class="description">配置智谱 GLM-4.5 系列模型的官方 API Key</p>
          </div>
          <el-tag :type="statusType" size="large">
            {{ statusText }}
          </el-tag>
        </div>
      </template>

      <!-- API Key 配置 -->
      <div class="config-section">
        <h4>🔑 API Key 配置</h4>
        <div class="api-key-input">
          <el-input
            v-model="apiKey"
            type="password"
            placeholder="请输入智谱 API Key (如: 50ef236d65d94aa78151a759c26d6a72.HborYaoAhrEDKnkD)"
            show-password
            clearable
            @input="onApiKeyChange"
          >
            <template #prepend>API Key</template>
          </el-input>
          <div class="input-actions">
            <el-button @click="saveApiKey" type="primary" :loading="saving">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button @click="testConnection" :loading="testing">
              <el-icon><Connection /></el-icon>
              测试连接
            </el-button>
          </div>
        </div>
      </div>

      <!-- 模型信息 -->
      <div class="config-section">
        <h4>🤖 支持的模型</h4>
        <div class="models-grid">
          <div 
            v-for="model in supportedModels" 
            :key="model.id"
            class="model-card"
          >
            <div class="model-header">
              <h5>{{ model.name }}</h5>
              <el-tag size="small" type="success">{{ model.pricing }}</el-tag>
            </div>
            <p class="model-description">{{ model.description }}</p>
            <div class="model-meta">
              <span class="meta-item">
                <el-icon><Document /></el-icon>
                {{ model.maxTokens }} tokens
              </span>
              <span v-if="model.supportsFunctions" class="meta-item">
                <el-icon><Tools /></el-icon>
                支持函数调用
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- API 信息 -->
      <div class="config-section">
        <h4>📋 API 信息</h4>
        <div class="api-info">
          <div class="info-item">
            <span class="info-label">API 地址:</span>
            <code>{{ apiStatus.baseURL }}</code>
          </div>
          <div class="info-item">
            <span class="info-label">官方文档:</span>
            <el-link 
              href="https://docs.bigmodel.cn/cn/guide/develop/http/introduction" 
              target="_blank"
              type="primary"
            >
              https://docs.bigmodel.cn/cn/guide/develop/http/introduction
            </el-link>
          </div>
          <div class="info-item">
            <span class="info-label">配置状态:</span>
            <span :class="['status-text', statusType]">{{ statusText }}</span>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="config-section">
        <h4>📖 使用说明</h4>
        <div class="usage-guide">
          <ol>
            <li>访问 <el-link href="https://open.bigmodel.cn" target="_blank" type="primary">智谱开放平台</el-link> 注册账号</li>
            <li>在控制台创建 API Key</li>
            <li>将 API Key 粘贴到上方输入框中</li>
            <li>点击"测试连接"验证配置</li>
            <li>在聊天界面选择 GLM-4.5 或 GLM-4.5-Air 模型</li>
          </ol>
          
          <el-alert
            title="注意事项"
            type="info"
            :closable="false"
            show-icon
          >
            <ul class="alert-list">
              <li>智谱 API 按 token 使用量计费</li>
              <li>GLM-4.5-Air 速度更快，成本更低</li>
              <li>GLM-4.5 功能更全面，质量更高</li>
              <li>API Key 会安全存储在本地浏览器中</li>
            </ul>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Connection, Document, Tools } from '@element-plus/icons-vue'
import zhipuApi from '@/services/zhipuApi'

// 响应式数据
const apiKey = ref('')
const saving = ref(false)
const testing = ref(false)

// 支持的模型
const supportedModels = ref([
  {
    id: 'glm-4.5-air',
    name: 'GLM-4.5-Air',
    description: '轻量级高效模型，响应速度快，适合日常对话',
    maxTokens: 8192,
    supportsFunctions: true,
    pricing: '官方 API'
  },
  {
    id: 'glm-4.5',
    name: 'GLM-4.5',
    description: '标准版本模型，功能全面，质量更高',
    maxTokens: 8192,
    supportsFunctions: true,
    pricing: '官方 API'
  }
])

// 计算属性
const apiStatus = computed(() => zhipuApi.getStatus())

const statusType = computed(() => {
  if (!apiKey.value) return 'info'
  return apiStatus.value.hasApiKey ? 'success' : 'warning'
})

const statusText = computed(() => {
  if (!apiKey.value) return '未配置'
  return apiStatus.value.hasApiKey ? '已配置' : '配置中'
})

// 方法
const onApiKeyChange = () => {
  if (apiKey.value) {
    zhipuApi.setApiKey(apiKey.value)
  }
}

const saveApiKey = async () => {
  if (!apiKey.value.trim()) {
    ElMessage.warning('请输入 API Key')
    return
  }

  saving.value = true
  try {
    // 设置 API Key
    zhipuApi.setApiKey(apiKey.value.trim())
    
    // 保存到本地存储
    localStorage.setItem('zhipu_api_key', apiKey.value.trim())
    
    ElMessage.success('智谱 API Key 保存成功')
  } catch (error) {
    console.error('保存 API Key 失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  if (!apiKey.value.trim()) {
    ElMessage.warning('请先输入 API Key')
    return
  }

  testing.value = true
  try {
    // 设置 API Key
    zhipuApi.setApiKey(apiKey.value.trim())
    
    // 测试连接
    const result = await zhipuApi.testConnection()
    
    if (result.success) {
      ElMessage.success('智谱 API 连接测试成功！')
    } else {
      ElMessage.error('连接测试失败: ' + result.message)
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('连接测试失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const loadSavedApiKey = () => {
  const saved = localStorage.getItem('zhipu_api_key')
  if (saved) {
    apiKey.value = saved
    zhipuApi.setApiKey(saved)
  }
}

// 生命周期
onMounted(() => {
  loadSavedApiKey()
})
</script>

<style lang="scss" scoped>
.zhipu-api-config {
  max-width: 800px;
  margin: 0 auto;
}

.config-card {
  margin-bottom: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  .header-info {
    h3 {
      margin: 0 0 0.5rem 0;
      color: #2c3e50;
      font-size: 1.25rem;
    }
    
    .description {
      margin: 0;
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

.config-section {
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }
}

.api-key-input {
  .input-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
  }
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.model-card {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  
  .model-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    
    h5 {
      margin: 0;
      color: #2c3e50;
    }
  }
  
  .model-description {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .model-meta {
    display: flex;
    gap: 1rem;
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      color: #6c757d;
      font-size: 0.8rem;
    }
  }
}

.api-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    
    .info-label {
      min-width: 80px;
      font-weight: 500;
      color: #495057;
    }
    
    code {
      background: #f8f9fa;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-family: monospace;
      color: #e83e8c;
    }
    
    .status-text {
      font-weight: 500;
      
      &.success {
        color: #28a745;
      }
      
      &.warning {
        color: #ffc107;
      }
      
      &.info {
        color: #17a2b8;
      }
    }
  }
}

.usage-guide {
  ol {
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
      color: #495057;
    }
  }
  
  .alert-list {
    margin: 0;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.25rem;
      color: #495057;
    }
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .input-actions {
    flex-direction: column;
  }
  
  .models-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
