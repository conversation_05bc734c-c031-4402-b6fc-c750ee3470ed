import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { showLoginRequiredMessage } from '@/utils/message'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 布局组件
const Layout = () => import('@/components/common/Layout.vue')
const SimpleLayout = () => import('@/components/common/SimpleLayout.vue')

// 页面组件 - 懒加载
const Home = () => import('@/views/HomeView.vue')
const Chat = () => import('@/views/chat/ChatViewAdvanced.vue')
const Drawing = () => import('@/views/create/CreateViewAdvanced.vue')
const Gallery = () => import('@/views/gallery/GalleryViewSimple.vue')

const Profile = () => import('@/views/user/ProfileView.vue')
const Settings = () => import('@/views/user/SettingsView.vue')
const Pricing = () => import('@/views/PricingView.vue')
const Login = () => import('@/views/auth/LoginView.vue')
const Register = () => import('@/views/auth/RegisterView.vue')
const Terms = () => import('@/views/legal/TermsView.vue')
const Privacy = () => import('@/views/legal/PrivacyView.vue')
const UsersAdmin = () => import('@/views/admin/UsersView.vue')

const ApiTest = () => import('@/views/test/ApiTest.vue')
const OpenRouterTest = () => import('@/views/test/OpenRouterTest.vue')

const ApiKeyManager = () => import('@/views/admin/ApiKeyManager.vue')
const ApiKeyManagement = () => import('@/views/admin/ApiKeyManagement.vue')
const ApiKeyRotationDemo = () => import('@/views/demo/ApiKeyRotationDemo.vue')

const SystemMonitor = () => import('@/views/admin/SystemMonitor.vue')
const SystemSettings = () => import('@/views/admin/SystemSettings.vue')
const SystemLogs = () => import('@/views/admin/SystemLogs.vue')
const ApiKeyBatchTester = () => import('@/views/admin/ApiKeyBatchTester.vue')
const NotFound = () => import('@/views/error/NotFoundView.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 直接的基础测试路由，不使用布局
    {
      path: '/direct-test',
      name: 'DirectTest',
      component: () => import('@/views/test/ApiTest.vue'),
      meta: {
        title: '直接测试',
        requiresAuth: false,
      },
    },
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: {
        title: '登录',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/simple-login',
      name: 'SimpleLogin',
      component: () => import('@/views/auth/SimpleLoginView.vue'),
      meta: {
        title: '简单登录',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/test-login',
      name: 'TestLogin',
      component: () => import('@/views/auth/TestLoginView.vue'),
      meta: {
        title: '测试登录',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/register',
      name: 'Register',
      component: Register,
      meta: {
        title: '注册',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/terms',
      name: 'Terms',
      component: Terms,
      meta: {
        title: '用户协议',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/privacy',
      name: 'Privacy',
      component: Privacy,
      meta: {
        title: '隐私政策',
        requiresAuth: false,
        hideInMenu: true,
      },
    },
    {
      path: '/',
      component: Layout,
      redirect: '/home',
      children: [
        {
          path: 'home',
          name: 'Home',
          component: Home,
          meta: {
            title: '首页',
            icon: 'House',
            requiresAuth: false,
          },
        },
        {
          path: 'chat',
          name: 'Chat',
          component: Chat,
          meta: {
            title: 'AI聊天',
            icon: 'ChatDotRound',
            requiresAuth: true,
          },
        },
        {
          path: 'drawing',
          name: 'Drawing',
          component: Drawing,
          meta: {
            title: 'AI绘画',
            icon: 'Brush',
            requiresAuth: true,
          },
        },
        {
          path: 'gallery',
          name: 'Gallery',
          component: Gallery,
          meta: {
            title: '作品展示',
            icon: 'Picture',
            requiresAuth: false,
          },
        },
        {
          path: 'pricing',
          name: 'Pricing',
          component: Pricing,
          meta: {
            title: '定价方案',
            icon: 'Money',
            requiresAuth: false,
          },
        },





        {
          path: 'profile',
          name: 'Profile',
          component: Profile,
          meta: {
            title: '个人中心',
            icon: 'User',
            requiresAuth: true,
            hideInMenu: true,
          },
        },
        {
          path: 'settings',
          name: 'Settings',
          component: Settings,
          meta: {
            title: '设置',
            icon: 'Setting',
            requiresAuth: true,
            hideInMenu: true,
          },
        },
        {
          path: 'admin/users',
          name: 'UsersAdmin',
          component: UsersAdmin,
          meta: {
            title: '用户管理',
            icon: 'UserFilled',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },

        {
          path: 'api-test',
          name: 'ApiTest',
          component: ApiTest,
          meta: {
            title: 'API测试',
            icon: 'Connection',
            requiresAuth: false,
          },
        },
        {
          path: 'openrouter-test',
          name: 'OpenRouterTest',
          component: OpenRouterTest,
          meta: {
            title: 'OpenRouter测试',
            icon: 'Link',
            requiresAuth: false,
          },
        },

        {
          path: 'api-key-manager',
          name: 'ApiKeyManager',
          component: ApiKeyManager,
          meta: {
            title: 'API密钥管理',
            icon: 'Key',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'api-key-management',
          name: 'ApiKeyManagement',
          component: () => import('@/views/ApiKeyManagement.vue'),
          meta: {
            title: 'API密钥配置',
            icon: 'Key',
            requiresAuth: false, // 改为普通用户也可访问
          },
        },
        {
          path: 'api-key-rotation-demo',
          name: 'ApiKeyRotationDemo',
          component: ApiKeyRotationDemo,
          meta: {
            title: 'API密钥轮询演示',
            icon: 'Refresh',
            requiresAuth: false,
          },
        },
        {
          path: 'login-state-demo',
          name: 'LoginStateDemo',
          component: () => import('@/views/demo/LoginStateDemo.vue'),
          meta: {
            title: '登录状态管理演示',
            icon: 'Lock',
            requiresAuth: false,
          },
        },

        {
          path: 'system-monitor',
          name: 'SystemMonitor',
          component: SystemMonitor,
          meta: {
            title: '系统监控',
            icon: 'Monitor',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'system-settings',
          name: 'SystemSettings',
          component: SystemSettings,
          meta: {
            title: '系统设置',
            icon: 'Tools',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'system-logs',
          name: 'SystemLogs',
          component: SystemLogs,
          meta: {
            title: '系统日志',
            icon: 'Document',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },
        {
          path: 'api-key-batch-tester',
          name: 'ApiKeyBatchTester',
          component: ApiKeyBatchTester,
          meta: {
            title: 'API密钥批量检测',
            icon: 'Operation',
            requiresAuth: true,
            requiresAdmin: true,
          },
        },



        {
          path: 'api-key-demo',
          name: 'ApiKeyConfigDemo',
          component: () => import('@/views/demo/ApiKeyConfigDemo.vue'),
          meta: {
            title: 'API密钥配置演示',
            icon: 'Key',
            requiresAuth: false,
          },
        },
        {
          path: 'targon-api-demo',
          name: 'TargonApiDemo',
          component: () => import('@/views/demo/TargonApiDemo.vue'),
          meta: {
            title: 'Targon API 演示',
            icon: 'Lightning',
            requiresAuth: false,
          },
        },
        {
          path: 'zhipu-api-demo',
          name: 'ZhipuApiDemo',
          component: () => import('@/views/demo/ZhipuApiDemo.vue'),
          meta: {
            title: '智谱 AI 演示',
            icon: 'ChatDotRound',
            requiresAuth: false,
          },
        },
        {
          path: 'chat-bubble-demo',
          name: 'ChatBubbleDemo',
          component: () => import('@/views/demo/ChatBubbleDemo.vue'),
          meta: {
            title: '智能聊天气泡演示',
            icon: 'ChatDotRound',
            requiresAuth: false,
          },
        },
        {
          path: 'login-persistence-test',
          name: 'LoginPersistenceTest',
          component: () => import('@/views/test/LoginPersistenceTest.vue'),
          meta: {
            title: '登录状态持久化测试',
            icon: 'Lock',
            requiresAuth: false,
          },
        },
        {
          path: 'login-status-test',
          name: 'LoginStatusTest',
          component: () => import('@/views/test/LoginStatusTest.vue'),
          meta: {
            title: '登录状态实时监控',
            icon: 'Monitor',
            requiresAuth: false,
          },
        },
        {
          path: 'login-state-test',
          name: 'LoginStateTest',
          component: () => import('@/views/test/LoginStateTest.vue'),
          meta: {
            title: '登录状态管理测试',
            icon: 'Lock',
            requiresAuth: false,
          },
        },
        {
          path: 'targon-model-test',
          name: 'TargonModelTest',
          component: () => import('@/views/test/TargonModelTest.vue'),
          meta: {
            title: 'Targon 模型测试',
            icon: 'Grid',
            requiresAuth: false,
          },
        },
      ],
    },
    {
      path: '/404',
      name: 'NotFound',
      component: NotFound,
      meta: {
        title: '页面不存在',
        hideInMenu: true,
      },
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404',
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  console.log('路由守卫: 导航到', to.path, '需要认证:', to.meta.requiresAuth)

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - AI创作助手平台` : 'AI创作助手平台'

  // 初始化用户状态（每次路由都检查）
  let userStore = null
  try {
    const { useUserStore } = await import('@/stores')
    userStore = useUserStore()

    // 强制初始化用户状态
    console.log('路由守卫: 强制初始化用户状态...')
    await userStore.initUserState()
    console.log('路由守卫: 用户状态初始化完成')
  } catch (error) {
    console.error('路由守卫: 初始化用户状态失败:', error)
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 安全地检查登录状态
    let isLoggedIn = false
    let userRole = 'user'

    if (userStore) {
      try {
        isLoggedIn = userStore.isLoggedIn || false
        userRole = userStore.userRole || 'user'
        console.log('路由守卫: 登录状态检查', { isLoggedIn, userRole, token: !!userStore.token, userInfo: !!userStore.userInfo })
      } catch (computedError) {
        console.warn('路由守卫: 访问computed属性失败:', computedError)
        isLoggedIn = false
        userRole = 'user'
      }
    } else {
      console.warn('路由守卫: 用户store不可用')
      isLoggedIn = false
      userRole = 'user'
    }

    if (!isLoggedIn) {
      console.log('路由守卫: 用户未登录，重定向到登录页')
      showLoginRequiredMessage()
      next({
        name: 'Login',
        query: { redirect: to.fullPath },
      })
      return
    }

    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin) {
      console.log('路由守卫: 检查管理员权限', userRole)
      if (userRole !== 'admin') {
        console.log('路由守卫: 非管理员用户访问管理页面，拒绝访问')
        ElMessage({
          message: '您没有权限访问此页面',
          type: 'error',
          duration: 4000,
          showClose: true,
          center: true
        })
        next({ name: 'Home' })
        return
      }
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register')) {
    try {
      const { useUserStore } = await import('@/stores')
      const userStore = useUserStore()

      // 安全地访问 computed 属性
      let currentLoginStatus = false
      try {
        currentLoginStatus = userStore.isLoggedIn || false
      } catch (computedError) {
        console.warn('路由守卫: 访问isLoggedIn失败:', computedError)
        currentLoginStatus = false
      }

      console.log('路由守卫: 访问登录页，当前登录状态', currentLoginStatus)
      if (currentLoginStatus) {
        console.log('路由守卫: 已登录用户访问登录页，重定向到首页')
        next({ name: 'Home' })
        return
      }
    } catch (error) {
      console.warn('路由守卫: 检查登录状态失败:', error)
      // 如果检查失败，允许访问登录页
    }
  }

  console.log('路由守卫: 允许导航')
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
