<template>
  <div class="api-key-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><Key /></el-icon>
          API 密钥管理
        </h1>
        <p class="page-description">
          管理您的AI服务提供商API密钥，支持多种主流AI服务
        </p>
      </div>
      
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showQuickSetup = true">
          快速配置
        </el-button>
        <el-button :icon="Refresh" @click="refreshAll">
          刷新状态
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ activeProviders }}</div>
              <div class="stat-label">活跃提供商</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ configuredProviders }}</div>
              <div class="stat-label">已配置</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalModels }}</div>
              <div class="stat-label">可用模型</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ lastTestTime }}</div>
              <div class="stat-label">最后测试</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- API密钥配置组件 -->
    <ApiKeyConfig />

    <!-- 快速配置对话框 -->
    <el-dialog
      v-model="showQuickSetup"
      title="快速配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="quick-setup">
        <el-alert
          title="快速配置向导"
          description="选择一个预设配置快速开始使用AI服务"
          type="info"
          :closable="false"
          show-icon
        />
        
        <div class="preset-configs">
          <div 
            v-for="preset in presetConfigs" 
            :key="preset.id"
            class="preset-card"
            @click="applyPreset(preset)"
          >
            <div class="preset-icon">
              <div class="preset-letter">{{ preset.name.charAt(0) }}</div>
            </div>
            <div class="preset-info">
              <h4>{{ preset.name }}</h4>
              <p>{{ preset.description }}</p>
              <div class="preset-features">
                <el-tag 
                  v-for="feature in preset.features" 
                  :key="feature"
                  size="small"
                  type="info"
                >
                  {{ feature }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showQuickSetup = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 使用说明 -->
    <el-card class="usage-guide">
      <template #header>
        <h3><el-icon><Document /></el-icon> 使用指南</h3>
      </template>
      
      <div class="guide-content">
        <el-steps :active="4" align-center class="guide-steps">
          <el-step title="选择提供商" description="从列表中选择AI服务提供商" />
          <el-step title="配置密钥" description="输入API密钥和相关配置" />
          <el-step title="测试连接" description="验证配置是否正确" />
          <el-step title="开始使用" description="在聊天和绘画中使用AI服务" />
        </el-steps>

        <div class="guide-tips">
          <h4>配置建议：</h4>
          <ul>
            <li><strong>OpenRouter:</strong> 推荐用于访问多种免费模型</li>
            <li><strong>OpenAI:</strong> 官方服务，稳定可靠但需付费</li>
            <li><strong>Anthropic:</strong> Claude模型，适合复杂对话任务</li>
            <li><strong>自定义:</strong> 支持任何OpenAI兼容的API服务</li>
          </ul>

          <el-alert
            title="安全提示"
            type="warning"
            :closable="false"
            show-icon
          >
            <ul class="security-tips">
              <li>API密钥仅存储在本地浏览器中，不会上传到服务器</li>
              <li>请妥善保管您的API密钥，不要分享给他人</li>
              <li>定期检查API使用情况和余额</li>
              <li>如发现异常使用，请及时更换密钥</li>
            </ul>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Key, Plus, Refresh, Check, Setting, Connection, Clock, Document 
} from '@element-plus/icons-vue'
import ApiKeyConfig from '@/components/ApiKeyConfig.vue'

// 响应式数据
const showQuickSetup = ref(false)

// 预设配置
const presetConfigs = ref([
  {
    id: 'openrouter-free',
    name: 'OpenRouter 免费',
    description: '使用OpenRouter的免费模型，无需付费即可体验多种AI模型',
    features: ['免费使用', '多种模型', '无需付费'],
    config: {
      name: 'OpenRouter',
      type: 'openai',
      baseUrl: 'https://openrouter.ai/api/v1',
      apiKey: '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',
      models: ['deepseek/deepseek-chat-v3-0324:free', 'qwen/qwen3-coder:free']
    }
  },
  {
    id: 'openai-official',
    name: 'OpenAI 官方',
    description: '使用OpenAI官方API，需要自己的API密钥',
    features: ['官方服务', '稳定可靠', '需要付费'],
    config: {
      name: 'OpenAI',
      type: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      models: ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo']
    }
  },
  {
    id: 'custom-api',
    name: '自定义API',
    description: '配置您自己的API服务或第三方代理',
    features: ['灵活配置', '支持代理', '自定义模型'],
    config: {
      name: '自定义API',
      type: 'custom',
      baseUrl: 'https://api.example.com/v1'
    }
  }
])

// 模拟数据 - 实际应用中应该从store或API获取
const mockProviders = ref([
  { configured: true, status: 'active', models: ['model1', 'model2'] },
  { configured: true, status: 'inactive', models: ['model3'] },
  { configured: false, status: 'inactive', models: [] }
])

// 计算属性
const activeProviders = computed(() => {
  return mockProviders.value.filter(p => p.status === 'active').length
})

const configuredProviders = computed(() => {
  return mockProviders.value.filter(p => p.configured).length
})

const totalModels = computed(() => {
  return mockProviders.value.reduce((total, p) => total + p.models.length, 0)
})

const lastTestTime = computed(() => {
  return '2分钟前'
})

// 方法
const refreshAll = async () => {
  ElMessage.info('正在刷新所有提供商状态...')
  
  // 模拟刷新
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  ElMessage.success('状态刷新完成')
}

const applyPreset = (preset) => {
  ElMessage.success(`已应用预设配置: ${preset.name}`)
  showQuickSetup.value = false
  
  // 这里应该调用ApiKeyConfig组件的方法来应用预设配置
  // 实际实现中需要通过ref或事件来通信
}

// 生命周期
onMounted(() => {
  // 初始化页面
})

// 设置页面标题
document.title = 'API密钥管理 - AI创意平台'
</script>

<style scoped>
.api-key-management {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
  color: #409EFF;
}

.page-description {
  font-size: 16px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stat-icon.success {
  background: #67c23a;
}

.stat-icon.warning {
  background: #e6a23c;
}

.stat-icon.info {
  background: #909399;
}

.stat-icon.primary {
  background: #409eff;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.quick-setup {
  padding: 0;
}

.preset-configs {
  margin-top: 20px;
  display: grid;
  gap: 16px;
}

.preset-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.preset-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background: #409eff;
  flex-shrink: 0;
}

.preset-letter {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.preset-info {
  flex: 1;
}

.preset-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.preset-info p {
  font-size: 14px;
  color: #606266;
  margin: 0 0 8px 0;
}

.preset-features {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.usage-guide {
  margin-top: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usage-guide :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.usage-guide h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-content {
  padding: 0;
}

.guide-steps {
  margin: 30px 0;
}

.guide-tips h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0 12px 0;
}

.guide-tips ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.guide-tips li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

.guide-tips strong {
  color: #303133;
}

.security-tips {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.security-tips li {
  margin-bottom: 6px;
  color: #606266;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .api-key-management {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .preset-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .preset-icon {
    margin-right: 0;
  }
  
  .guide-steps {
    margin: 20px 0;
  }
  
  .guide-steps :deep(.el-step__description) {
    font-size: 12px;
  }
}
</style>
