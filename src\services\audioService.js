// 音频处理服务 - 基于 Pollinations.AI
import { ElMessage } from 'element-plus'

// 音频服务配置
const AUDIO_CONFIG = {
  textToSpeechUrl: 'https://text.pollinations.ai',
  audioGenerationUrl: 'https://audio.pollinations.ai',
  openaiUrl: 'https://text.pollinations.ai/openai',
  timeout: 60000,
  defaultVoice: 'alloy'
}

// 支持的语音列表
export const SUPPORTED_VOICES = [
  'alloy',
  'echo', 
  'fable',
  'onyx',
  'nova',
  'shimmer'
]

// 音频处理服务类
class AudioService {
  constructor() {
    this.activeRequests = new Map()
  }

  // 文本转语音 (GET 方式)
  async textToSpeech(text, options = {}) {
    try {
      if (!text || typeof text !== 'string' || !text.trim()) {
        throw new Error('请提供有效的文本内容')
      }

      const voice = options.voice || AUDIO_CONFIG.defaultVoice
      const model = options.model || 'openai-audio'
      
      // 构建 URL
      const encodedText = encodeURIComponent(text)
      const url = `${AUDIO_CONFIG.textToSpeechUrl}/${encodedText}?model=${model}&voice=${voice}`

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), AUDIO_CONFIG.timeout)

      try {
        const response = await fetch(url, {
          method: 'GET',
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
        }

        // 检查响应类型
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('audio')) {
          const audioBlob = await response.blob()
          const audioUrl = URL.createObjectURL(audioBlob)

          return {
            success: true,
            audioBlob,
            audioUrl,
            text,
            voice,
            model
          }
        } else {
          throw new Error('响应不是音频格式')
        }
      } catch (error) {
        clearTimeout(timeoutId)
        if (error.name === 'AbortError') {
          throw new Error('请求超时，请稍后重试')
        }
        throw error
      }
    } catch (error) {
      console.error('文本转语音失败:', error)
      return {
        success: false,
        error: error.message,
        text
      }
    }
  }

  // 文本转语音 (POST 方式，OpenAI 兼容)
  async textToSpeechAdvanced(text, options = {}) {
    try {
      const payload = {
        model: options.model || 'tts-1',
        input: text,
        voice: options.voice || AUDIO_CONFIG.defaultVoice,
        response_format: options.format || 'mp3',
        speed: options.speed || 1.0
      }

      const response = await fetch(AUDIO_CONFIG.openaiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const audioBlob = await response.blob()
      const audioUrl = URL.createObjectURL(audioBlob)

      return {
        success: true,
        audioBlob,
        audioUrl,
        text,
        options: payload
      }
    } catch (error) {
      console.error('高级文本转语音失败:', error)
      return {
        success: false,
        error: error.message,
        text
      }
    }
  }

  // 语音转文本 (使用 OpenAI 兼容接口)
  async speechToText(audioFile, options = {}) {
    try {
      if (!audioFile) {
        throw new Error('请提供音频文件')
      }

      // 将音频文件转换为 base64
      const base64Audio = await this.fileToBase64(audioFile)
      const audioFormat = this.getAudioFormat(audioFile.name)

      const messages = [
        {
          role: 'user',
          content: [
            { type: 'text', text: options.prompt || '请转录这段音频:' },
            {
              type: 'input_audio',
              input_audio: {
                data: base64Audio,
                format: audioFormat
              }
            }
          ]
        }
      ]

      const payload = {
        model: options.model || 'whisper-1',
        messages: messages,
        temperature: options.temperature || 0
      }

      const response = await fetch(AUDIO_CONFIG.openaiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      const transcription = result.choices?.[0]?.message?.content || ''

      return {
        success: true,
        transcription,
        audioFile: audioFile.name,
        model: options.model || 'whisper-1'
      }
    } catch (error) {
      console.error('语音转文本失败:', error)
      return {
        success: false,
        error: error.message,
        audioFile: audioFile?.name || 'unknown'
      }
    }
  }

  // 音频生成 (使用 Pollinations 音频 API)
  async generateAudio(prompt, options = {}) {
    try {
      if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
        throw new Error('请提供有效的音频描述')
      }

      const encodedPrompt = encodeURIComponent(prompt)
      const params = new URLSearchParams()
      
      if (options.duration) params.append('duration', options.duration)
      if (options.style) params.append('style', options.style)
      if (options.seed) params.append('seed', options.seed)

      const queryString = params.toString()
      const url = `${AUDIO_CONFIG.audioGenerationUrl}/${encodedPrompt}${queryString ? '?' + queryString : ''}`

      const response = await fetch(url, {
        method: 'GET'
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('audio')) {
        const audioBlob = await response.blob()
        const audioUrl = URL.createObjectURL(audioBlob)

        return {
          success: true,
          audioBlob,
          audioUrl,
          prompt,
          options
        }
      } else {
        throw new Error('响应不是音频格式')
      }
    } catch (error) {
      console.error('音频生成失败:', error)
      return {
        success: false,
        error: error.message,
        prompt
      }
    }
  }

  // 播放音频
  async playAudio(audioUrl) {
    try {
      const audio = new Audio(audioUrl)
      audio.play()
      return { success: true, audio }
    } catch (error) {
      console.error('播放音频失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 下载音频
  async downloadAudio(audioUrl, filename) {
    try {
      const response = await fetch(audioUrl)
      const blob = await response.blob()
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename || `audio_${Date.now()}.mp3`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('下载音频失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 工具函数：文件转 base64
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result.split(',')[1] // 移除 data: 前缀
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  // 获取音频格式
  getAudioFormat(filename) {
    const extension = filename.split('.').pop().toLowerCase()
    const formatMap = {
      'mp3': 'mp3',
      'wav': 'wav',
      'ogg': 'ogg',
      'm4a': 'm4a',
      'flac': 'flac'
    }
    return formatMap[extension] || 'mp3'
  }

  // 获取活跃请求数量
  getActiveRequestCount() {
    return this.activeRequests.size
  }

  // 取消所有活跃请求
  cancelAllRequests() {
    this.activeRequests.clear()
  }
}

// 创建单例实例
const audioService = new AudioService()

// 导出服务实例和便捷方法
export default audioService

export const {
  textToSpeech,
  textToSpeechAdvanced,
  speechToText,
  generateAudio,
  playAudio,
  downloadAudio
} = audioService

// 导出配置
export { AUDIO_CONFIG }
