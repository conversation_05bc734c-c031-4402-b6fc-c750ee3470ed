<template>
  <div class="profile-view">
    <!-- 用户资料头部 -->
    <div class="profile-header">
      <div class="header-background">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
        <div class="floating-particles">
          <div v-for="i in 20" :key="i" class="particle" :style="getParticleStyle(i)"></div>
        </div>
      </div>

      <div class="container">
        <div class="profile-banner">
          <div class="profile-avatar-section">
            <div class="avatar-container">
              <div class="avatar-ring"></div>
              <div class="avatar-glow"></div>
              <img :src="userInfo.avatar || '/default-avatar.svg'" :alt="userInfo.username" class="profile-avatar" />
              <button @click="changeAvatar" class="avatar-edit-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" fill="currentColor"/>
                </svg>
              </button>
              <div class="online-indicator" v-if="userInfo.isOnline"></div>
            </div>

            <div class="profile-basic-info">
              <div class="username-section">
                <h1 class="username">{{ userInfo.username || '未设置用户名' }}</h1>
                <div class="verification-badge" v-if="userInfo.isVerified">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>

              <div class="user-level">
                <div class="level-badge" :class="userInfo.level">
                  <div class="level-icon">{{ getLevelIcon(userInfo.level) }}</div>
                  <span>{{ getLevelName(userInfo.level) }}</span>
                  <div class="level-progress">
                    <div class="progress-bar" :style="{ width: userInfo.levelProgress + '%' }"></div>
                  </div>
                </div>
                <div class="member-status" v-if="userInfo.isPremium">
                  <div class="premium-badge">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                    </svg>
                    <span>会员</span>
                  </div>
                </div>
              </div>

              <p class="user-bio">{{ userInfo.bio || '这个人很懒，什么都没有留下...' }}</p>

              <div class="user-tags" v-if="userInfo.tags && userInfo.tags.length">
                <span v-for="tag in userInfo.tags" :key="tag" class="tag">{{ tag }}</span>
              </div>
              <div class="user-stats">
                <div class="stat-item" @click="showStatDetail('artworks')">
                  <div class="stat-number">{{ userInfo.artworksCount }}</div>
                  <div class="stat-label">作品</div>
                </div>
                <div class="stat-item" @click="showStatDetail('followers')">
                  <div class="stat-number">{{ formatNumber(userInfo.followersCount) }}</div>
                  <div class="stat-label">粉丝</div>
                </div>
                <div class="stat-item" @click="showStatDetail('likes')">
                  <div class="stat-number">{{ formatNumber(userInfo.likesCount) }}</div>
                  <div class="stat-label">获赞</div>
                </div>
                <div class="stat-item" @click="showStatDetail('views')">
                  <div class="stat-number">{{ formatNumber(userInfo.viewsCount) }}</div>
                  <div class="stat-label">浏览</div>
                </div>
              </div>
            </div>
          </div>

          <div class="profile-actions">
            <button @click="editProfile" class="action-btn primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>编辑资料</span>
            </button>

            <button @click="showSettings = true" class="action-btn secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a3 3 0 0 1 0 6 3 3 0 0 1 0-6z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>设置</span>
            </button>

            <button v-if="!userInfo.isPremium" @click="upgradeToPremium" class="action-btn premium">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
              </svg>
              <span>升级会员</span>
            </button>

            <button @click="shareProfile" class="action-btn secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="16,6 12,2 8,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="12" y1="2" x2="12" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>分享</span>
            </button>

            <button @click="followUser" class="action-btn follow" v-if="!isOwnProfile">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="19" y1="8" x2="19" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="22" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>关注</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="profile-content">
      <div class="container">
        <div class="content-layout">
          <!-- 左侧导航 -->
          <div class="profile-sidebar">
            <div class="sidebar-menu">
              <button
                v-for="tab in profileTabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="['menu-item', { active: activeTab === tab.id }]"
              >
                <div class="menu-icon" v-html="tab.icon"></div>
                <span class="menu-label">{{ tab.label }}</span>
                <div class="menu-indicator"></div>
              </button>
            </div>
          </div>

          <!-- 右侧内容 -->
          <div class="profile-main">
            <!-- 我的作品 -->
            <div v-if="activeTab === 'artworks'" class="tab-content">
              <div class="content-header">
                <h2>我的作品</h2>
                <div class="content-actions">
                  <button @click="createNewArtwork" class="create-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                      <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>创建新作品</span>
                  </button>
                </div>
              </div>

              <div class="artworks-grid">
                <div
                  v-for="artwork in userArtworks"
                  :key="artwork.id"
                  class="artwork-card"
                  @click="viewArtwork(artwork.id)"
                >
                  <div class="artwork-image">
                    <img :src="artwork.thumbnail" :alt="artwork.title" />
                    <div class="artwork-overlay">
                      <div class="artwork-stats">
                        <div class="stat-item">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                          </svg>
                          <span>{{ artwork.views }}</span>
                        </div>
                        <div class="stat-item">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="currentColor" stroke-width="2"/>
                          </svg>
                          <span>{{ artwork.likes }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="artwork-info">
                    <h4 class="artwork-title">{{ artwork.title }}</h4>
                    <p class="artwork-date">{{ formatDate(artwork.createdAt) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 会员权益 -->
            <div v-if="activeTab === 'membership'" class="tab-content">
              <div class="membership-section">
                <div class="membership-header">
                  <h2>会员权益</h2>
                  <div class="membership-status" :class="{ premium: userInfo.isPremium }">
                    <div class="status-icon">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" :fill="userInfo.isPremium ? 'currentColor' : 'none'"/>
                      </svg>
                    </div>
                    <span>{{ userInfo.isPremium ? '会员用户' : '普通用户' }}</span>
                  </div>
                </div>

                <div class="membership-benefits">
                  <div class="benefit-card" v-for="benefit in membershipBenefits" :key="benefit.id">
                    <div class="benefit-icon" v-html="benefit.icon"></div>
                    <div class="benefit-content">
                      <h4 class="benefit-title">{{ benefit.title }}</h4>
                      <p class="benefit-description">{{ benefit.description }}</p>
                      <div class="benefit-status" :class="{ available: benefit.available }">
                        {{ benefit.available ? '已开通' : '需要会员' }}
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="!userInfo.isPremium" class="upgrade-section">
                  <div class="upgrade-card">
                    <div class="upgrade-content">
                      <h3>升级到会员，解锁全部功能</h3>
                      <p>享受无限创作、高级功能、优先支持等专属权益</p>
                      <button @click="upgradeToPremium" class="upgrade-btn">
                        <span>立即升级</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                          <polyline points="12,5 19,12 12,19" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 账户设置 -->
            <div v-if="activeTab === 'settings'" class="tab-content">
              <div class="settings-section">
                <h2>账户设置</h2>

                <div class="settings-groups">
                  <div class="settings-group">
                    <h3>基本信息</h3>
                    <div class="setting-item">
                      <label>用户名</label>
                      <input v-model="userInfo.username" type="text" class="setting-input" />
                    </div>
                    <div class="setting-item">
                      <label>邮箱</label>
                      <input v-model="userInfo.email" type="email" class="setting-input" />
                    </div>
                    <div class="setting-item">
                      <label>个人简介</label>
                      <textarea v-model="userInfo.bio" class="setting-textarea" rows="3"></textarea>
                    </div>
                  </div>

                  <div class="settings-group">
                    <h3>安全设置</h3>
                    <div class="setting-item">
                      <label>修改密码</label>
                      <button @click="changePassword" class="setting-btn">修改密码</button>
                    </div>
                    <div class="setting-item">
                      <label>两步验证</label>
                      <button @click="setupTwoFactor" class="setting-btn">设置两步验证</button>
                    </div>
                  </div>

                  <div class="settings-group">
                    <h3>隐私设置</h3>
                    <div class="setting-item">
                      <label>作品可见性</label>
                      <select v-model="userInfo.artworkVisibility" class="setting-select">
                        <option value="public">公开</option>
                        <option value="followers">仅粉丝可见</option>
                        <option value="private">私密</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="settings-actions">
                  <button @click="saveSettings" class="save-btn">保存设置</button>
                  <button @click="resetSettings" class="reset-btn">重置</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const activeTab = ref('artworks')
const showSettings = ref(false)

// 用户信息
const userInfo = reactive({
  username: 'AI创作大师',
  email: '<EMAIL>',
  bio: '专注AI艺术创作，探索无限创意可能。已创作500+精美作品，获得10万+点赞支持。',
  avatar: '/default-avatar.svg',
  level: 'expert', // beginner, intermediate, expert, master
  levelProgress: 75, // 等级进度百分比
  isPremium: false,
  isVerified: true, // 认证用户
  isOnline: true, // 在线状态
  artworksCount: 156,
  followersCount: 2847,
  likesCount: 15632,
  viewsCount: 89456,
  artworkVisibility: 'public',
  tags: ['AI艺术', '数字绘画', '创意设计', '视觉艺术'], // 用户标签
  joinDate: '2023-01-15' // 加入日期
})

// 计算属性
const isOwnProfile = computed(() => true) // 是否为自己的资料页

// 标签页配置
const profileTabs = [
  {
    id: 'artworks',
    label: '我的作品',
    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/><circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/><polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/></svg>'
  },
  {
    id: 'membership',
    label: '会员权益',
    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/></svg>'
  },
  {
    id: 'settings',
    label: '账户设置',
    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a3 3 0 0 1 0 6 3 3 0 0 1 0-6z" stroke="currentColor" stroke-width="2"/></svg>'
  },
  {
    id: 'analytics',
    label: '数据统计',
    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><line x1="18" y1="20" x2="18" y2="10" stroke="currentColor" stroke-width="2"/><line x1="12" y1="20" x2="12" y2="4" stroke="currentColor" stroke-width="2"/><line x1="6" y1="20" x2="6" y2="14" stroke="currentColor" stroke-width="2"/></svg>'
  }
]

// 用户作品数据
const userArtworks = ref([
  {
    id: 1,
    title: '梦幻森林',
    thumbnail: '/artwork1.jpg',
    views: 1234,
    likes: 89,
    createdAt: '2024-01-15'
  },
  {
    id: 2,
    title: '未来城市',
    thumbnail: '/artwork2.jpg',
    views: 2156,
    likes: 156,
    createdAt: '2024-01-10'
  },
  {
    id: 3,
    title: '星空下的少女',
    thumbnail: '/artwork3.jpg',
    views: 3421,
    likes: 234,
    createdAt: '2024-01-05'
  }
])

// 会员权益数据
const membershipBenefits = [
  {
    id: 1,
    title: '无限AI对话',
    description: '不受次数限制，随时与AI助手交流创作灵感',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  },
  {
    id: 2,
    title: '高级AI模型',
    description: '使用最新的GPT-4和Claude-3等顶级AI模型',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  },
  {
    id: 3,
    title: '4K高清绘画',
    description: '生成最高4K分辨率的精美AI艺术作品',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  },
  {
    id: 4,
    title: '优先处理',
    description: '作品生成享受优先队列，更快获得结果',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  },
  {
    id: 5,
    title: '商业使用授权',
    description: '生成的作品可用于商业用途，无版权限制',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  },
  {
    id: 6,
    title: '专属客服',
    description: '享受7x24小时专属客服支持服务',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/><circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/><path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2"/><path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/></svg>',
    available: userInfo.isPremium
  }
]

// 计算属性
const getLevelIcon = (level) => {
  const icons = {
    beginner: '🌱',
    intermediate: '🌿',
    expert: '🌳',
    master: '👑'
  }
  return icons[level] || '🌱'
}

const getLevelName = (level) => {
  const names = {
    beginner: '新手创作者',
    intermediate: '进阶创作者',
    expert: '专业创作者',
    master: '大师级创作者'
  }
  return names[level] || '新手创作者'
}

// 方法
const changeAvatar = () => {
  ElMessageBox.confirm('选择头像更换方式', '更换头像', {
    distinguishCancelAndClose: true,
    confirmButtonText: '上传图片',
    cancelButtonText: '选择默认头像'
  }).then(() => {
    // 上传图片逻辑
    ElMessage.info('上传功能开发中...')
  }).catch(action => {
    if (action === 'cancel') {
      // 选择默认头像
      ElMessage.info('默认头像选择功能开发中...')
    }
  })
}

const editProfile = () => {
  activeTab.value = 'settings'
  ElMessage.info('已切换到设置页面')
}

const upgradeToPremium = () => {
  router.push('/pricing')
}

const createNewArtwork = () => {
  router.push('/drawing')
}

const viewArtwork = (artworkId) => {
  router.push(`/gallery/${artworkId}`)
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const changePassword = () => {
  ElMessageBox.prompt('请输入新密码', '修改密码', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'password',
    inputValidator: (value) => {
      if (!value || value.length < 6) {
        return '密码长度不能少于6位'
      }
      return true
    }
  }).then(({ value }) => {
    // 这里应该调用API修改密码
    ElMessage.success('密码修改成功')
  }).catch(() => {
    ElMessage.info('已取消修改')
  })
}

const setupTwoFactor = () => {
  ElMessage.info('两步验证功能开发中...')
}

const saveSettings = () => {
  // 这里应该调用API保存设置
  ElMessage.success('设置已保存')
}

const resetSettings = () => {
  ElMessageBox.confirm('确定要重置所有设置吗？', '重置设置', {
    type: 'warning'
  }).then(() => {
    // 重置设置逻辑
    ElMessage.success('设置已重置')
  }).catch(() => {
    ElMessage.info('已取消重置')
  })
}

// 新增方法
const getParticleStyle = (index) => {
  const size = Math.random() * 4 + 2
  const delay = Math.random() * 5
  const duration = Math.random() * 10 + 10
  const x = Math.random() * 100
  const y = Math.random() * 100

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${x}%`,
    top: `${y}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`
  }
}

const showStatDetail = (type) => {
  ElMessage.info(`查看${type}详情功能开发中...`)
}

const shareProfile = () => {
  if (navigator.share) {
    navigator.share({
      title: `${userInfo.username}的个人资料`,
      text: userInfo.bio,
      url: window.location.href
    }).catch(() => {
      copyToClipboard()
    })
  } else {
    copyToClipboard()
  }
}

const copyToClipboard = () => {
  navigator.clipboard.writeText(window.location.href).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const followUser = () => {
  ElMessage.success('关注成功')
}

// 格式化数字显示
const formatNumber = (num) => {
  if (typeof num !== 'number') return '0'

  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  } else {
    return num.toString()
  }
}
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.profile-view {
  min-height: 100vh;
  background: $bg-color-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: 768px) {
    padding: 0 $spacing-md;
  }
}

// 用户资料头部
.profile-header {
  position: relative;
  background: linear-gradient(135deg, $bg-color-dark 0%, #1e293b 50%, #334155 100%);
  color: white;
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.6;
      animation: float 8s ease-in-out infinite;

      &.orb-1 {
        width: 300px;
        height: 300px;
        background: $gradient-primary;
        top: -150px;
        left: -150px;
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 200px;
        height: 200px;
        background: $gradient-accent;
        bottom: -100px;
        right: -100px;
        animation-delay: 4s;
      }

      &.orb-3 {
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        top: 50%;
        right: 10%;
        animation-delay: 2s;
      }
    }

    .floating-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;

      .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: particleFloat linear infinite;
      }
    }
  }

  .profile-banner {
    position: relative;
    z-index: 2;
    padding: $spacing-xl 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: $spacing-xl;

    @media (max-width: 1024px) {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: $spacing-lg;
    }

    .profile-avatar-section {
      display: flex;
      align-items: flex-end;
      gap: $spacing-xl;

      @media (max-width: 1024px) {
        flex-direction: column;
        align-items: center;
        gap: $spacing-lg;
      }

      .avatar-container {
        position: relative;

        .avatar-ring {
          position: absolute;
          top: -8px;
          left: -8px;
          right: -8px;
          bottom: -8px;
          border: 3px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        .avatar-glow {
          position: absolute;
          top: -12px;
          left: -12px;
          right: -12px;
          bottom: -12px;
          background: $gradient-primary;
          border-radius: 50%;
          opacity: 0.3;
          filter: blur(20px);
          animation: glow 3s ease-in-out infinite alternate;
        }

        .profile-avatar {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          border: 4px solid white;
          box-shadow: $box-shadow-xl;
          object-fit: cover;
          position: relative;
          z-index: 2;
        }

        .online-indicator {
          position: absolute;
          bottom: 12px;
          right: 12px;
          width: 20px;
          height: 20px;
          background: #10b981;
          border: 3px solid white;
          border-radius: 50%;
          z-index: 3;
          animation: onlinePulse 2s infinite;
        }

        .avatar-edit-btn {
          position: absolute;
          bottom: 8px;
          right: 8px;
          width: 36px;
          height: 36px;
          background: $gradient-primary;
          border: none;
          border-radius: 50%;
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: $box-shadow-md;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.1);
            box-shadow: $box-shadow-lg;
          }
        }
      }

      .profile-basic-info {
        .username-section {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-sm;

          .username {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            line-height: 1.2;
          }

          .verification-badge {
            color: #3b82f6;
            display: flex;
            align-items: center;
            animation: verifiedBadge 2s ease-in-out infinite;
          }
        }

        .user-level {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          margin-bottom: $spacing-md;

          .level-badge {
            display: flex;
            align-items: center;
            gap: $spacing-xs;
            padding: $spacing-xs $spacing-md;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            position: relative;
            overflow: hidden;

            .level-progress {
              position: absolute;
              bottom: 0;
              left: 0;
              height: 3px;
              background: rgba(255, 255, 255, 0.2);
              width: 100%;

              .progress-bar {
                height: 100%;
                background: $gradient-accent;
                transition: width 0.3s ease;
              }
            }

            &.expert {
              background: $gradient-success;
            }

            &.master {
              background: $gradient-premium;
            }
          }

          .member-status {
            .premium-badge {
              display: flex;
              align-items: center;
              gap: $spacing-xs;
              padding: $spacing-xs $spacing-md;
              background: $gradient-premium;
              border-radius: 20px;
              font-size: 14px;
              font-weight: 600;
            }
          }
        }

        .user-bio {
          font-size: 1.1rem;
          color: rgba(255, 255, 255, 0.8);
          margin: 0 0 $spacing-md 0;
          line-height: 1.6;
          max-width: 500px;
        }

        .user-tags {
          display: flex;
          flex-wrap: wrap;
          gap: $spacing-xs;
          margin-bottom: $spacing-lg;

          .tag {
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: translateY(-1px);
            }
          }
        }

        .user-stats {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: $spacing-lg;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: $spacing-md;
          }

          .stat-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: $spacing-sm;
            border-radius: 12px;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              transform: translateY(-2px);
            }

            .stat-number {
              display: block;
              font-size: 1.8rem;
              font-weight: 800;
              background: $gradient-accent;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              line-height: 1;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.7);
              font-weight: 500;
            }
          }
        }
      }
    }

    .profile-actions {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;

      @media (max-width: 1024px) {
        flex-direction: row;
        justify-content: center;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        width: 100%;
      }

      .action-btn {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        padding: $spacing-md $spacing-lg;
        border: none;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        white-space: nowrap;

        &.primary {
          background: $gradient-primary;
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-lg;
          }
        }

        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        }

        &.premium {
          background: $gradient-premium;
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-lg;
          }
        }

        &.follow {
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-lg;
            background: linear-gradient(135deg, #059669, #047857);
          }
        }
      }
    }
  }
}

// 主要内容区域样式
.profile-content {
  background: $bg-color;
  min-height: 60vh;

  .content-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: $spacing-xl;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  .profile-sidebar {
    .sidebar-menu {
      background: $bg-color-card;
      border-radius: 16px;
      padding: $spacing-md;
      box-shadow: $box-shadow-sm;

      .menu-item {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        padding: $spacing-md $spacing-lg;
        border: none;
        background: transparent;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-align: left;
        color: $text-color-secondary;
        font-size: 14px;
        font-weight: 500;
        position: relative;

        .menu-icon {
          color: $text-color-muted;
          transition: color 0.3s ease;
        }

        .menu-label {
          flex: 1;
        }

        .menu-indicator {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) scaleX(0);
          width: 3px;
          height: 20px;
          background: $gradient-primary;
          border-radius: 2px;
          transition: transform 0.3s ease;
        }

        &:hover {
          background: $bg-color-secondary;
          color: $text-color-primary;

          .menu-icon {
            color: $primary-color;
          }
        }

        &.active {
          background: rgba(99, 102, 241, 0.1);
          color: $primary-color;

          .menu-icon {
            color: $primary-color;
          }

          .menu-indicator {
            transform: translateY(-50%) scaleX(1);
          }
        }
      }
    }
  }

  .profile-main {
    .tab-content {
      background: $bg-color-card;
      border-radius: 16px;
      padding: $spacing-xl;
      box-shadow: $box-shadow-sm;

      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-xl;

        h2 {
          font-size: 1.5rem;
          font-weight: 700;
          color: $text-color-primary;
          margin: 0;
        }

        .content-actions {
          .create-btn {
            display: flex;
            align-items: center;
            gap: $spacing-sm;
            padding: $spacing-sm $spacing-lg;
            background: $gradient-primary;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: $box-shadow-lg;
            }
          }
        }
      }

      .artworks-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: $spacing-lg;

        .artwork-card {
          background: $bg-color;
          border-radius: 12px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 1px solid $border-color-light;

          &:hover {
            transform: translateY(-4px);
            box-shadow: $box-shadow-lg;
          }

          .artwork-image {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }

            .artwork-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);
              display: flex;
              align-items: flex-end;
              padding: $spacing-md;
              opacity: 0;
              transition: opacity 0.3s ease;

              .artwork-stats {
                display: flex;
                gap: $spacing-md;

                .stat-item {
                  display: flex;
                  align-items: center;
                  gap: $spacing-xs;
                  color: white;
                  font-size: 12px;
                  font-weight: 500;
                }
              }
            }

            &:hover {
              img {
                transform: scale(1.05);
              }

              .artwork-overlay {
                opacity: 1;
              }
            }
          }

          .artwork-info {
            padding: $spacing-md;

            .artwork-title {
              font-size: 14px;
              font-weight: 600;
              color: $text-color-primary;
              margin: 0 0 $spacing-xs 0;
              line-height: 1.4;
            }

            .artwork-date {
              font-size: 12px;
              color: $text-color-muted;
              margin: 0;
            }
          }
        }
      }
    }
  }
}

// 会员权益样式
.membership-section {
  .membership-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 1.5rem;
      font-weight: 700;
      color: $text-color-primary;
      margin: 0;
    }

    .membership-status {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      padding: $spacing-sm $spacing-lg;
      background: $bg-color-secondary;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      color: $text-color-secondary;

      &.premium {
        background: $gradient-premium;
        color: white;
      }

      .status-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .membership-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-lg;
    margin-bottom: $spacing-xl;

    .benefit-card {
      display: flex;
      gap: $spacing-md;
      padding: $spacing-lg;
      background: $bg-color;
      border: 1px solid $border-color-light;
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $box-shadow-md;
      }

      .benefit-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: $bg-color-secondary;
        border-radius: 12px;
        color: $primary-color;
      }

      .benefit-content {
        flex: 1;

        .benefit-title {
          font-size: 16px;
          font-weight: 600;
          color: $text-color-primary;
          margin: 0 0 $spacing-xs 0;
        }

        .benefit-description {
          font-size: 14px;
          color: $text-color-secondary;
          margin: 0 0 $spacing-sm 0;
          line-height: 1.5;
        }

        .benefit-status {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
          background: $bg-color-secondary;
          color: $text-color-muted;

          &.available {
            background: rgba(16, 185, 129, 0.1);
            color: $success-color;
          }
        }
      }
    }
  }

  .upgrade-section {
    .upgrade-card {
      background: $gradient-primary;
      border-radius: 16px;
      padding: $spacing-xl;
      text-align: center;
      color: white;

      .upgrade-content {
        h3 {
          font-size: 1.25rem;
          font-weight: 700;
          margin: 0 0 $spacing-sm 0;
        }

        p {
          font-size: 16px;
          margin: 0 0 $spacing-lg 0;
          opacity: 0.9;
        }

        .upgrade-btn {
          display: inline-flex;
          align-items: center;
          gap: $spacing-sm;
          padding: $spacing-md $spacing-xl;
          background: white;
          color: $primary-color;
          border: none;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-lg;
          }
        }
      }
    }
  }
}

// 设置页面样式
.settings-section {
  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-color-primary;
    margin: 0 0 $spacing-xl 0;
  }

  .settings-groups {
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;

    .settings-group {
      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: $text-color-primary;
        margin: 0 0 $spacing-lg 0;
      }

      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: $spacing-lg 0;
        border-bottom: 1px solid $border-color-light;

        &:last-child {
          border-bottom: none;
        }

        label {
          font-size: 14px;
          font-weight: 500;
          color: $text-color-primary;
        }

        .setting-input,
        .setting-select {
          padding: $spacing-sm $spacing-md;
          border: 1px solid $border-color-light;
          border-radius: 8px;
          font-size: 14px;
          color: $text-color-primary;
          background: $bg-color;
          min-width: 200px;

          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
          }
        }

        .setting-textarea {
          padding: $spacing-sm $spacing-md;
          border: 1px solid $border-color-light;
          border-radius: 8px;
          font-size: 14px;
          color: $text-color-primary;
          background: $bg-color;
          min-width: 300px;
          resize: vertical;

          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
          }
        }

        .setting-btn {
          padding: $spacing-sm $spacing-lg;
          background: $gradient-primary;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: $box-shadow-md;
          }
        }
      }
    }
  }

  .settings-actions {
    display: flex;
    gap: $spacing-md;
    justify-content: flex-end;
    margin-top: $spacing-xl;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-color-light;

    .save-btn {
      padding: $spacing-md $spacing-xl;
      background: $gradient-primary;
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $box-shadow-lg;
      }
    }

    .reset-btn {
      padding: $spacing-md $spacing-xl;
      background: transparent;
      color: $text-color-secondary;
      border: 1px solid $border-color-light;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: $bg-color-secondary;
        color: $text-color-primary;
      }
    }
  }
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes glow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes onlinePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes verifiedBadge {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
