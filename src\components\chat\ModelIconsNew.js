// AI 模型图标库 - 基于 LobeHub 官方图标
// 导入SVG图标文件
import claudeIcon from '@/assets/icons/claude-color.svg?raw'
import deepseekIcon from '@/assets/icons/deepseek-color.svg?raw'
import fluxIcon from '@/assets/icons/flux.svg?raw'
import geminiIcon from '@/assets/icons/gemini-color.svg?raw'
import gemmaIcon from '@/assets/icons/gemma-color.svg?raw'
import googleIcon from '@/assets/icons/google-color.svg?raw'
import grokIcon from '@/assets/icons/grok.svg?raw'
import metaIcon from '@/assets/icons/meta-color.svg?raw'
import midjourneyIcon from '@/assets/icons/midjourney.svg?raw'
import mistralIcon from '@/assets/icons/mistral-color.svg?raw'
import moonshotIcon from '@/assets/icons/moonshot.svg?raw'
import openaiIcon from '@/assets/icons/openai.svg?raw'
import openrouterIcon from '@/assets/icons/openrouter.svg?raw'
import qwenIcon from '@/assets/icons/qwen-color.svg?raw'
import stabilityIcon from '@/assets/icons/stability-color.svg?raw'

export const MODEL_ICONS = {
  // OpenAI (ChatGPT) - 使用导入的SVG
  'openai': openaiIcon,
  'gpt': openaiIcon,
  'gpt-3.5': openaiIcon,
  'gpt-4': openaiIcon,
  'gpt-4o': openaiIcon,
  'o1': openaiIcon,
  'o3': openaiIcon,
  
  // Google 系列
  'google': googleIcon,
  'gemini': geminiIcon,
  'gemini-2.5': geminiIcon,
  'gemini-pro': geminiIcon,
  'gemini-flash': geminiIcon,
  'gemma': gemmaIcon,
  
  // Anthropic Claude
  'claude': claudeIcon,
  'anthropic': claudeIcon,
  
  // Meta/Facebook
  'llama': metaIcon,
  'meta': metaIcon,
  'llama-4': metaIcon,
  
  // Mistral
  'mistral': mistralIcon,

  // DeepSeek
  'deepseek': deepseekIcon,
  'deepseek-v3': deepseekIcon,
  'deepseek-reasoning': deepseekIcon,
  'deepseek-reasoning-large': deepseekIcon,
  'deepseek-r1': deepseekIcon,
  'deepseek r1': deepseekIcon,
  'deepseek v3': deepseekIcon,

  // Qwen/Alibaba
  'qwen': qwenIcon,
  'qwen2.5': qwenIcon,
  'qwen-turbo': qwenIcon,
  
  // Grok/X
  'grok': grokIcon,
  'grok-3': grokIcon,
  
  // Moonshot
  'moonshot': moonshotIcon,
  'kimi': moonshotIcon,
  
  // Stability AI
  'stability': stabilityIcon,
  'stable-diffusion': stabilityIcon,
  
  // Flux
  'flux': fluxIcon,
  'flux-pro': fluxIcon,
  'flux-dev': fluxIcon,
  
  // Midjourney
  'midjourney': midjourneyIcon,
  'mj': midjourneyIcon,
  
  // OpenRouter
  'openrouter': openrouterIcon,

  // Rtist - 艺术创作AI图标（画笔风格）
  'rtist': `<svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <defs>
      <linearGradient id="rtistGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#FF8E8E;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="12" cy="12" r="10" fill="url(#rtistGrad)"/>
    <path d="M8 16l2-6 2 2 2-6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    <circle cx="12" cy="8" r="1.5" fill="white"/>
    <path d="M6 18c2-1 4-1 6 0s4 1 6 0" stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none"/>
  </svg>`,

  // Evil - 暗黑风格AI图标（神秘眼睛）
  'evil': `<svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <defs>
      <linearGradient id="evilGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#A78BFA;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="12" cy="12" r="10" fill="url(#evilGrad)"/>
    <ellipse cx="12" cy="12" rx="8" ry="4" fill="white" opacity="0.9"/>
    <ellipse cx="12" cy="12" rx="6" ry="3" fill="#8B5CF6"/>
    <circle cx="12" cy="12" r="2" fill="white"/>
    <circle cx="12" cy="12" r="1" fill="#8B5CF6"/>
    <path d="M6 8c2 1 4 1 6 0s4-1 6 0" stroke="white" stroke-width="1" stroke-linecap="round" fill="none" opacity="0.7"/>
  </svg>`,

  // Hormoz - 现代科技风格图标（芯片风格）
  'hormoz': `<svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <defs>
      <linearGradient id="hormozGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#34D399;stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect x="4" y="4" width="16" height="16" rx="3" fill="url(#hormozGrad)"/>
    <rect x="6" y="6" width="12" height="12" rx="2" fill="white" opacity="0.2"/>
    <circle cx="9" cy="9" r="1.5" fill="white"/>
    <circle cx="15" cy="9" r="1.5" fill="white"/>
    <circle cx="9" cy="15" r="1.5" fill="white"/>
    <circle cx="15" cy="15" r="1.5" fill="white"/>
    <path d="M9 12h6M12 9v6" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
    <rect x="2" y="10" width="2" height="4" fill="#10B981"/>
    <rect x="20" y="10" width="2" height="4" fill="#10B981"/>
    <rect x="10" y="2" width="4" height="2" fill="#10B981"/>
    <rect x="10" y="20" width="4" height="2" fill="#10B981"/>
  </svg>`,

  // 默认图标 - 精美的AI大脑图标
  'default': `<svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <defs>
      <linearGradient id="defaultGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="12" cy="12" r="10" fill="url(#defaultGrad)"/>
    <path d="M8 10c0-2 1-3 2-3s2 1 2 3c0 1 1 2 2 2s2-1 2-2c0-2 1-3 2-3s2 1 2 3" stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none"/>
    <path d="M8 14c0 2 1 3 2 3s2-1 2-3c0-1 1-2 2-2s2 1 2 2c0 2 1 3 2 3s2-1 2-3" stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none"/>
    <circle cx="9" cy="10" r="1" fill="white"/>
    <circle cx="15" cy="10" r="1" fill="white"/>
    <circle cx="9" cy="14" r="1" fill="white"/>
    <circle cx="15" cy="14" r="1" fill="white"/>
    <circle cx="12" cy="12" r="1.5" fill="white"/>
  </svg>`
}

// 模型颜色映射
export const MODEL_COLORS = {
  // OpenAI 系列
  'openai': { bg: '#000000', text: '#ffffff' },
  'gpt': { bg: '#000000', text: '#ffffff' },
  
  // Google 系列
  'google': { bg: '#4285F4', text: '#ffffff' },
  'gemini': { bg: '#4285F4', text: '#ffffff' },
  
  // Anthropic
  'claude': { bg: '#D97757', text: '#ffffff' },
  'anthropic': { bg: '#D97757', text: '#ffffff' },
  
  // Meta
  'llama': { bg: '#1D65C1', text: '#ffffff' },
  'meta': { bg: '#1D65C1', text: '#ffffff' },
  
  // Mistral
  'mistral': { bg: '#FF7000', text: '#ffffff' },

  // DeepSeek 系列
  'deepseek': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek-v3': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek-reasoning': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek-reasoning-large': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek-r1': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek r1': { bg: '#4D6BFE', text: '#ffffff' },
  'deepseek v3': { bg: '#4D6BFE', text: '#ffffff' },

  // Qwen 系列
  'qwen': { bg: '#10B981', text: '#ffffff' },
  'qwen2.5': { bg: '#10B981', text: '#ffffff' },
  
  // Grok (X/Twitter) 系列
  'grok': { bg: '#000000', text: '#ffffff' },
  'grok-3': { bg: '#000000', text: '#ffffff' },

  // Moonshot
  'moonshot': { bg: '#1a1a1a', text: '#ffffff' },
  'kimi': { bg: '#1a1a1a', text: '#ffffff' },
  
  // Stability AI
  'stability': { bg: '#6366F1', text: '#ffffff' },
  
  // Flux
  'flux': { bg: '#000000', text: '#ffffff' },
  
  // Midjourney
  'midjourney': { bg: '#000000', text: '#ffffff' },
  
  // OpenRouter
  'openrouter': { bg: '#000000', text: '#ffffff' },

  // Rtist - 艺术创作AI（珊瑚红渐变）
  'rtist': { bg: 'linear-gradient(135deg, #FF6B6B, #FF8E8E)', text: '#ffffff' },

  // Evil - 暗黑风格AI（紫色渐变）
  'evil': { bg: 'linear-gradient(135deg, #8B5CF6, #A78BFA)', text: '#ffffff' },

  // Hormoz - 现代科技AI（翠绿渐变）
  'hormoz': { bg: 'linear-gradient(135deg, #10B981, #34D399)', text: '#ffffff' },

  // 默认颜色（靛蓝渐变）
  'default': { bg: 'linear-gradient(135deg, #6366F1, #8B5CF6)', text: '#ffffff' }
}

// 获取模型图标
export function getModelIcon(modelName) {
  const normalizedName = modelName.toLowerCase()
  
  // 精确匹配
  if (MODEL_ICONS[normalizedName]) {
    return MODEL_ICONS[normalizedName]
  }
  
  // 部分匹配
  for (const [key, icon] of Object.entries(MODEL_ICONS)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return icon
    }
  }
  
  return MODEL_ICONS.default
}

// 获取模型颜色
export function getModelColor(modelName) {
  const normalizedName = modelName.toLowerCase()

  // 查找匹配的颜色
  for (const [key, color] of Object.entries(MODEL_COLORS)) {
    if (normalizedName.includes(key)) {
      // 强制返回透明背景，保持文字颜色
      return {
        bg: 'transparent',
        text: color.text
      }
    }
  }

  // 默认也返回透明背景
  return {
    bg: 'transparent',
    text: MODEL_COLORS.default.text
  }
}
