<template>
  <div class="chat-sidebar" :class="{ collapsed: collapsed }">
    <div class="sidebar-header">
      <button
        @click="$emit('create-conversation')"
        class="new-chat-btn"
        :class="{ collapsed: collapsed }"
      >
        <div class="btn-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <span v-if="!collapsed" class="btn-text">新建对话</span>
      </button>

      <!-- 模型选择器 -->
      <div class="model-selector" v-if="!collapsed">
        <label class="selector-label">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
          </svg>
          AI模型
        </label>
        <div class="select-wrapper">
          <select :value="selectedModel" @change="$emit('update:selectedModel', $event.target.value)" class="model-select">
            <option value="gemini-2.5-flash">Gemini 2.5 Flash (推荐)</option>
            <option value="gemini-2.5-flash-lite">Gemini 2.5 Flash Lite (极速)</option>
            <option value="gemini-2.5-pro">Gemini 2.5 Pro (最强)</option>
            <option value="openai">OpenAI GPT-4o Mini</option>
            <option value="llama">Llama 3.3 70B</option>
            <option value="mistral">Mistral Small 3</option>
          </select>
          <div class="select-arrow">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>

      <button
        @click="$emit('toggle-sidebar')"
        class="toggle-btn"
        :title="collapsed ? '展开侧边栏' : '收起侧边栏'"
      >
        <svg v-if="!collapsed" width="18" height="18" viewBox="0 0 24 24" fill="none">
          <path d="M11 19l-7-7 7-7m8 14l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <svg v-else width="18" height="18" viewBox="0 0 24 24" fill="none">
          <path d="M13 5l7 7-7 7M5 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <ConversationList 
      :conversations="conversations"
      :current-conversation-id="currentConversationId"
      :collapsed="collapsed"
      @switch-conversation="$emit('switch-conversation', $event)"
      @conversation-command="$emit('conversation-command', $event)"
    />
  </div>
</template>

<script setup>
import ConversationList from './ConversationList.vue'

defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  selectedModel: {
    type: String,
    default: 'gpt-3.5-turbo'
  },
  conversations: {
    type: Array,
    default: () => []
  },
  currentConversationId: {
    type: String,
    default: null
  }
})

defineEmits([
  'create-conversation',
  'toggle-sidebar',
  'update:selectedModel',
  'switch-conversation',
  'conversation-command'
])
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.chat-sidebar {
  width: 320px;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 0;

  &.collapsed {
    width: 70px;
    
    .sidebar-header {
      .new-chat-btn {
        width: 48px;
        height: 48px;
        justify-content: center;
        border-radius: 12px;
        
        &.collapsed {
          .btn-text {
            display: none;
          }
        }
      }

      .model-selector {
        display: none;
      }

      .toggle-btn {
        margin: 0 auto;
      }
    }
  }

  .sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .new-chat-btn {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.875rem 1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

      .btn-icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .btn-text {
        font-weight: 600;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .model-selector {
      .selector-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 12px;
        font-weight: 600;
        color: rgba(148, 163, 184, 0.8);
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        svg {
          color: rgba(99, 102, 241, 0.8);
        }
      }

      .select-wrapper {
        position: relative;

        .model-select {
          width: 100%;
          padding: 0.75rem 2.5rem 0.75rem 1rem;
          background: rgba(15, 23, 42, 0.6);
          border: 1px solid rgba(148, 163, 184, 0.2);
          border-radius: 8px;
          color: white;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          appearance: none;

          &:hover {
            background: rgba(15, 23, 42, 0.8);
            border-color: rgba(99, 102, 241, 0.5);
          }

          &:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
          }

          option {
            background: #1e293b;
            color: white;
          }
        }

        .select-arrow {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          pointer-events: none;
          color: rgba(148, 163, 184, 0.6);
        }
      }
    }

    .toggle-btn {
      align-self: flex-end;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(71, 85, 105, 0.3);
      border: 1px solid rgba(71, 85, 105, 0.4);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: rgba(226, 232, 240, 0.8);

      &:hover {
        background: rgba(71, 85, 105, 0.5);
        color: #8b5cf6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
      }
    }
  }
}

// 移动端响应式优化
@media (max-width: 768px) {
  .chat-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    width: 85vw !important; // 使用视口宽度
    max-width: 320px !important; // 最大宽度限制
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.15);

    &:not(.collapsed) {
      transform: translateX(0);
    }

    &.collapsed {
      transform: translateX(-100%);
      width: 85vw !important; // 移动端不使用折叠宽度
      max-width: 320px !important;
    }

    .sidebar-header {
      padding: 1rem 0.75rem; // 减少头部内边距
      gap: 0.75rem; // 减少间距

      .new-chat-btn {
        padding: 0.75rem 0.875rem; // 减少按钮内边距
        font-size: 14px;

        &:hover {
          transform: none; // 移动端移除悬停效果
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
      }

      .model-selector {
        .selector-label {
          font-size: 11px; // 稍小标签文字
          margin-bottom: 0.375rem;
        }

        .select-wrapper {
          select {
            font-size: 14px; // 调整选择器字体
            padding: 0.5rem 0.75rem;
          }
        }
      }

      .toggle-btn {
        width: 36px; // 确保触摸目标足够大
        height: 36px;

        &:hover {
          transform: none; // 移动端移除悬停效果
          background: rgba(71, 85, 105, 0.3);
        }
      }
    }

    .sidebar-content {
      padding: 0 0.75rem; // 减少内容区域内边距
    }
  }
}

// 超小屏幕优化 (480px以下)
@media (max-width: 480px) {
  .chat-sidebar {
    width: 90vw !important; // 超小屏幕占90%宽度
    max-width: 280px !important;

    .sidebar-header {
      padding: 0.75rem 0.5rem; // 更紧凑的内边距
      gap: 0.5rem;

      .new-chat-btn {
        padding: 0.625rem 0.75rem; // 更紧凑的按钮
        font-size: 13px;
      }

      .model-selector {
        .selector-label {
          font-size: 10px;
        }

        .select-wrapper {
          select {
            font-size: 13px;
            padding: 0.375rem 0.625rem;
          }
        }
      }

      .toggle-btn {
        width: 32px; // 超小屏幕稍小按钮
        height: 32px;
      }
    }

    .sidebar-content {
      padding: 0 0.5rem; // 更紧凑的内容区域
    }
  }
}

// 侧边栏遮罩层（移动端）
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }
}
</style>
