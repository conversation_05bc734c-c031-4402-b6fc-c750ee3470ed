<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">OpenRouter API 简单测试</h1>
      
      <!-- 基本信息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">API状态</h2>
        <div class="space-y-2">
          <p><strong>API端点:</strong> https://openrouter.ai/api/v1</p>
          <p><strong>支持模型数量:</strong> {{ modelCount }}</p>
          <p><strong>页面状态:</strong> <span class="text-green-600">正常加载</span></p>
        </div>
      </div>

      <!-- 模型列表 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">免费模型列表</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="(model, index) in displayModels" :key="index" class="border border-gray-200 rounded-lg p-3">
            <div class="font-medium text-sm text-gray-900">{{ getModelName(model) }}</div>
            <div class="text-xs text-gray-500">{{ getProviderName(model) }}</div>
          </div>
        </div>
      </div>

      <!-- API密钥状态 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">API密钥状态</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ apiConfig.totalKeys }}</div>
            <div class="text-sm text-gray-600">总密钥数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ apiConfig.activeKeys }}</div>
            <div class="text-sm text-gray-600">可用密钥</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600">{{ apiConfig.keyRotationEnabled ? '启用' : '禁用' }}</div>
            <div class="text-sm text-gray-600">密钥轮询</div>
          </div>
        </div>
        <div class="mt-4 flex space-x-4">
          <button
            @click="refreshApiConfig"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            刷新状态
          </button>
          <button
            @click="testKeyRotation"
            class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
          >
            测试密钥轮询
          </button>
        </div>
      </div>

      <!-- 简单测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">快速测试</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
            <select v-model="selectedModel" class="w-full p-3 border border-gray-300 rounded-lg">
              <option value="">请选择模型</option>
              <option v-for="(model, index) in displayModels" :key="index" :value="model">
                {{ getModelName(model) }} ({{ getProviderName(model) }})
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">测试消息</label>
            <input 
              v-model="testMessage" 
              type="text" 
              placeholder="输入测试消息..."
              class="w-full p-3 border border-gray-300 rounded-lg"
            />
          </div>
          
          <button 
            @click="runTest"
            :disabled="!selectedModel || !testMessage.trim() || loading"
            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '测试中...' : '开始测试' }}
          </button>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResult" class="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 class="font-semibold mb-2">测试结果</h3>
          <div class="text-sm space-y-1">
            <p><strong>状态:</strong> <span :class="testResult.success ? 'text-green-600' : 'text-red-600'">
              {{ testResult.success ? '成功' : '失败' }}
            </span></p>
            <p v-if="testResult.message"><strong>消息:</strong> {{ testResult.message }}</p>
            <p v-if="testResult.error"><strong>错误:</strong> {{ testResult.error }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getApiConfig, rotateApiKey } from '@/services/openrouterApi.js'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedModel = ref('')
const testMessage = ref('Hello, how are you?')
const loading = ref(false)
const testResult = ref(null)
const apiConfig = ref({
  totalKeys: 0,
  activeKeys: 0,
  keyRotationEnabled: false
})

// 模型列表 - 直接定义避免导入问题
const models = ref([
  'deepseek/deepseek-chat-v3-0324:free',
  'deepseek/deepseek-r1:free',
  'qwen/qwen3-coder:free',
  'meta-llama/llama-3.3-70b-instruct:free',
  'google/gemini-2.0-flash-exp:free',
  'mistralai/mistral-nemo:free',
  'moonshotai/kimi-k2:free',
  'qwen/qwen-2.5-72b-instruct:free',
  'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
  'microsoft/mai-ds-r1:free'
])

// 计算属性
const modelCount = computed(() => models.value.length)
const displayModels = computed(() => models.value.slice(0, 12)) // 只显示前12个

// 方法
const getModelName = (model) => {
  const parts = model.split('/')
  return parts[1]?.replace(':free', '') || model
}

const getProviderName = (model) => {
  const parts = model.split('/')
  return parts[0] || 'Unknown'
}

// 刷新API配置
const refreshApiConfig = () => {
  try {
    apiConfig.value = getApiConfig()
    ElMessage.success('API配置已刷新')
  } catch (error) {
    ElMessage.error('获取API配置失败: ' + error.message)
  }
}

// 测试密钥轮询
const testKeyRotation = () => {
  try {
    rotateApiKey()
    refreshApiConfig()
    ElMessage.success('密钥轮询测试成功，已切换到下一个密钥')
  } catch (error) {
    ElMessage.error('密钥轮询测试失败: ' + error.message)
  }
}

const runTest = async () => {
  if (!selectedModel.value || !testMessage.value.trim()) return

  loading.value = true
  testResult.value = null

  try {
    // 模拟API测试
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟成功结果
    testResult.value = {
      success: true,
      message: `模型 ${getModelName(selectedModel.value)} 测试成功！API连接正常。`
    }
  } catch (error) {
    testResult.value = {
      success: false,
      error: error.message || '测试失败'
    }
  } finally {
    loading.value = false
  }
}

// 组件挂载
onMounted(() => {
  console.log('OpenRouter测试页面加载成功')
  console.log('支持的模型数量:', modelCount.value)

  // 加载API配置
  refreshApiConfig()
})
</script>

<style scoped>
/* 简单样式 */
</style>
