<template>
  <div style="padding: 2rem; text-align: center; font-family: Arial, sans-serif;">
    <h1 style="color: #2c3e50;">🎉 页面正常工作！</h1>
    <p style="color: #7f8c8d; font-size: 1.2rem;">
      如果您能看到这个页面，说明应用已经成功启动。
    </p>
    <div style="margin: 2rem 0; padding: 1rem; background: #ecf0f1; border-radius: 8px;">
      <p><strong>当前时间:</strong> {{ currentTime }}</p>
      <p><strong>页面路径:</strong> {{ $route.path }}</p>
      <p><strong>环境模式:</strong> {{ isDev ? '开发模式' : '生产模式' }}</p>
    </div>
    <button 
      @click="showAlert" 
      style="
        background: #3498db; 
        color: white; 
        border: none; 
        padding: 0.75rem 1.5rem; 
        border-radius: 4px; 
        cursor: pointer;
        font-size: 1rem;
      "
    >
      测试交互
    </button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const currentTime = ref('')

const isDev = computed(() => import.meta.env.DEV)

const showAlert = () => {
  alert('交互测试成功！页面功能正常。')
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

let timeInterval = null

onMounted(() => {
  console.log('MinimalTest 组件已挂载')
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  console.log('MinimalTest 组件已卸载')
})
</script>
