<template>
  <div class="settings-view">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="语言设置">
              <el-select v-model="basicSettings.language" placeholder="请选择语言">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="主题模式">
              <el-radio-group v-model="basicSettings.theme">
                <el-radio value="light">浅色模式</el-radio>
                <el-radio value="dark">深色模式</el-radio>
                <el-radio value="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="自动保存">
              <el-switch v-model="basicSettings.autoSave" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <el-form :model="notificationSettings" label-width="120px">
            <el-form-item label="桌面通知">
              <el-switch v-model="notificationSettings.desktop" />
            </el-form-item>
            
            <el-form-item label="邮件通知">
              <el-switch v-model="notificationSettings.email" />
            </el-form-item>
            
            <el-form-item label="声音提醒">
              <el-switch v-model="notificationSettings.sound" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 隐私设置 -->
        <el-tab-pane label="隐私设置" name="privacy">
          <el-form :model="privacySettings" label-width="120px">
            <el-form-item label="作品公开">
              <el-switch v-model="privacySettings.publicWorks" />
            </el-form-item>
            
            <el-form-item label="允许评论">
              <el-switch v-model="privacySettings.allowComments" />
            </el-form-item>
            
            <el-form-item label="显示在线状态">
              <el-switch v-model="privacySettings.showOnlineStatus" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <div class="settings-actions">
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
        <el-button @click="resetSettings">重置设置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const activeTab = ref('basic')

// 基本设置
const basicSettings = reactive({
  language: 'zh-CN',
  theme: 'light',
  autoSave: true
})

// 通知设置
const notificationSettings = reactive({
  desktop: true,
  email: false,
  sound: true
})

// 隐私设置
const privacySettings = reactive({
  publicWorks: true,
  allowComments: true,
  showOnlineStatus: true
})

// 保存设置
const saveSettings = () => {
  ElMessage.success('设置保存成功')
}

// 重置设置
const resetSettings = () => {
  ElMessage.info('设置已重置')
}
</script>

<style scoped>
.settings-view {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.settings-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.settings-tabs {
  margin-top: 20px;
}

.settings-actions {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.settings-actions .el-button {
  margin: 0 10px;
}

@media (max-width: 768px) {
  .settings-view {
    padding: 10px;
  }
  
  .settings-actions {
    text-align: center;
  }
  
  .settings-actions .el-button {
    width: 120px;
    margin: 5px;
  }
}
</style>
