<template>
  <div class="mobile-layout-test">
    <div class="test-header">
      <h1>移动端布局测试</h1>
      <p>当前屏幕宽度: {{ screenWidth }}px</p>
      <p>设备类型: {{ deviceType }}</p>
    </div>

    <div class="test-sections">
      <!-- 侧边栏测试 -->
      <section class="test-section">
        <h2>侧边栏测试</h2>
        <button @click="toggleSidebar" class="test-btn">
          {{ sidebarVisible ? '隐藏' : '显示' }}侧边栏
        </button>
        
        <!-- 模拟侧边栏 -->
        <div 
          class="test-sidebar chat-sidebar"
          :class="{ visible: sidebarVisible }"
        >
          <div class="sidebar-content">
            <h3>侧边栏内容</h3>
            <p>这是一个测试侧边栏</p>
            <ul>
              <li>菜单项 1</li>
              <li>菜单项 2</li>
              <li>菜单项 3</li>
            </ul>
          </div>
        </div>

        <!-- 遮罩层 -->
        <div 
          class="sidebar-overlay"
          :class="{ active: sidebarVisible }"
          @click="toggleSidebar"
        ></div>
      </section>

      <!-- 响应式测试 -->
      <section class="test-section">
        <h2>响应式测试</h2>
        <div class="responsive-grid">
          <div class="grid-item">项目 1</div>
          <div class="grid-item">项目 2</div>
          <div class="grid-item">项目 3</div>
          <div class="grid-item">项目 4</div>
        </div>
      </section>

      <!-- 移动端输入测试 -->
      <section class="test-section mobile-input-optimized">
        <h2>移动端输入测试</h2>
        <div class="chat-input-container">
          <div class="input-field">
            <input 
              type="text" 
              placeholder="测试输入框..."
              v-model="testInput"
            />
          </div>
          <div class="input-buttons">
            <button>发送</button>
            <button>语音</button>
          </div>
        </div>
      </section>

      <!-- 消息列表测试 -->
      <section class="test-section mobile-messages-optimized">
        <h2>消息列表测试</h2>
        <div class="message-list">
          <div class="message-item" v-for="i in 3" :key="i">
            <div class="message-avatar">AI</div>
            <div class="message-content">
              <div class="message-text">
                这是第 {{ i }} 条测试消息，用于验证移动端布局效果。
              </div>
              <div class="message-actions">
                <button>复制</button>
                <button>重新生成</button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const screenWidth = ref(window.innerWidth)
const sidebarVisible = ref(false)
const testInput = ref('')

const deviceType = computed(() => {
  if (screenWidth.value <= 480) return '超小屏幕 (手机)'
  if (screenWidth.value <= 768) return '小屏幕 (平板)'
  if (screenWidth.value <= 1024) return '中等屏幕 (小笔记本)'
  return '大屏幕 (桌面)'
})

const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})
</script>

<style scoped>
.mobile-layout-test {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.test-section {
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.test-btn {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 1rem;
}

.test-btn:hover {
  background: #0056b3;
}

.test-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 280px;
  background: white;
  border-right: 1px solid #e0e0e0;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.test-sidebar.visible {
  transform: translateX(0);
}

.sidebar-content {
  padding: 1rem;
}

.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.grid-item {
  padding: 1rem;
  background: #f0f0f0;
  border-radius: 4px;
  text-align: center;
}

.chat-input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.input-field {
  flex: 1;
}

.input-field input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.input-buttons {
  display: flex;
  gap: 0.5rem;
}

.input-buttons button {
  padding: 0.5rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.message-avatar {
  width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-text {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.message-actions {
  display: flex;
  gap: 0.5rem;
}

.message-actions button {
  padding: 0.25rem 0.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-layout-test {
    padding: 0.5rem;
  }
  
  .test-section {
    padding: 1rem;
  }
  
  .responsive-grid {
    grid-template-columns: 1fr;
  }
  
  .test-sidebar {
    width: 85vw;
    max-width: 320px;
  }
}

@media (max-width: 480px) {
  .test-sidebar {
    width: 90vw;
    max-width: 300px;
  }
  
  .chat-input-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-buttons {
    justify-content: center;
  }
}
</style>
