<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
    <div class="flex h-screen">
      <!-- 左侧控制面板 -->
      <div class="w-96 bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl">
        <!-- 面板头部 -->
        <div class="p-6 border-b border-gray-200/50">
          <div class="flex items-center space-x-4 mb-4">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl blur-lg opacity-30 animate-pulse-gentle"></div>
              <div class="relative w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                AI 创作工坊
              </h3>
              <p class="text-sm text-gray-600">将您的想象力转化为艺术作品</p>
            </div>
          </div>

          <!-- 快捷操作按钮 -->
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <button
                @click="showTemplates = true"
                class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 hover:scale-105"
                title="模板库"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="7" height="9" stroke="currentColor" stroke-width="2"/>
                  <rect x="14" y="3" width="7" height="5" stroke="currentColor" stroke-width="2"/>
                  <rect x="14" y="12" width="7" height="9" stroke="currentColor" stroke-width="2"/>
                  <rect x="3" y="16" width="7" height="5" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>

              <button
                @click="showHistory = true"
                class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 hover:scale-105"
                title="历史记录"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>

              <button
                @click="randomPrompt"
                class="relative p-2 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
                title="随机灵感"
              >
                <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg blur opacity-50 animate-pulse"></div>
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" class="relative z-10">
                  <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>

            <!-- 用户配额显示 -->
            <div class="flex items-center space-x-2 px-3 py-1 bg-green-50 rounded-full border border-green-200">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-xs font-medium text-green-700">今日剩余</span>
                <span class="text-xs font-bold text-green-700">{{ remainingQuota }}/{{ dailyQuota }}</span>
            </div>
          </div>
        </div>

        <!-- 面板内容 -->
        <div class="flex-1 overflow-y-auto p-6 space-y-6">
          <!-- 智能提示词输入 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <div class="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded flex items-center justify-center">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" class="text-white">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <span>描述您想要的图像</span>
                <div v-if="isProFeature" class="px-2 py-0.5 text-xs font-bold text-purple-600 bg-purple-100 rounded-full">PRO</div>
              </label>
              <span class="text-xs text-gray-500">详细的描述能获得更好的效果</span>
            </div>

            <div class="space-y-3">
              <!-- 智能工具栏 -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div class="flex space-x-2">
                  <button
                    @click="showPromptHelper = !showPromptHelper"
                    :class="[
                      'flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200',
                      showPromptHelper
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-white text-gray-600 border border-gray-200 hover:bg-gray-50'
                    ]"
                    title="AI提示词助手"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>AI助手</span>
                  </button>

                  <button
                    @click="translatePrompt"
                    class="flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium bg-white text-gray-600 border border-gray-200 hover:bg-gray-50 transition-all duration-200"
                    title="智能翻译"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <path d="M5 8l6 6M4 14l6-6 2-3M2 5h12M7 2h1l8 14 1 1M17 8l2 2-2 2M19 6h2a2 2 0 0 1 2 2v6.5" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>翻译</span>
                  </button>

                  <button
                    @click="enhancePrompt"
                    class="relative flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-md hover:shadow-lg"
                    title="AI增强"
                  >
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-md blur opacity-50 animate-pulse"></div>
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" class="relative z-10">
                      <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span class="relative z-10">增强</span>
                  </button>
                </div>

                <div class="w-px h-4 bg-gray-300"></div>

                <div class="flex space-x-2">
                  <button
                    @click="pasteFromClipboard"
                    class="flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium bg-white text-gray-600 border border-gray-200 hover:bg-gray-50 transition-all duration-200"
                    title="粘贴"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2"/>
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>

                  <button
                    @click="clearPrompt"
                    class="flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium bg-white text-gray-600 border border-gray-200 hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-all duration-200"
                    title="清空"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <path d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 智能输入框 -->
              <div class="relative">
                <textarea
                  ref="promptInput"
                  v-model="prompt"
                  placeholder="例如：一只可爱的小猫坐在花园里，阳光明媚，水彩画风格，高质量，细节丰富..."
                  maxlength="1000"
                  rows="4"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none transition-all duration-200 text-sm leading-relaxed placeholder-gray-400"
                  @input="handlePromptInput"
                  @keydown="handleKeyDown"
                ></textarea>

                <!-- 实时建议 -->
                <div
                  v-if="showSuggestions && suggestions.length > 0"
                  class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto"
                >
                  <div class="flex items-center justify-between p-3 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-700">智能建议</span>
                    <button
                      @click="showSuggestions = false"
                      class="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    >
                      ×
                    </button>
                  </div>
                  <div class="p-2 space-y-1">
                    <button
                      v-for="(suggestion, index) in suggestions"
                      :key="index"
                      @click="applySuggestion(suggestion)"
                      class="w-full flex items-center space-x-2 p-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors duration-200"
                    >
                      <span class="text-yellow-500">💡</span>
                      <span>{{ suggestion }}</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 输入状态栏 -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-4 text-xs text-gray-500">
                  <div class="flex items-center space-x-1">
                    <span>字符</span>
                    <span class="font-medium text-gray-700">{{ prompt.length }}/1000</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <span>词汇</span>
                    <span class="font-medium text-gray-700">{{ wordCount }}</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <span>质量</span>
                    <div class="flex items-center space-x-2">
                      <div class="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          class="h-full transition-all duration-300 rounded-full"
                          :class="{
                            'bg-red-400 w-1/4': promptQuality === 'poor',
                            'bg-yellow-400 w-2/4': promptQuality === 'fair',
                            'bg-green-400 w-3/4': promptQuality === 'good',
                            'bg-blue-400 w-full': promptQuality === 'excellent'
                          }"
                        ></div>
                      </div>
                      <span
                        class="text-xs font-medium"
                        :class="{
                          'text-red-600': promptQuality === 'poor',
                          'text-yellow-600': promptQuality === 'fair',
                          'text-green-600': promptQuality === 'good',
                          'text-blue-600': promptQuality === 'excellent'
                        }"
                      >
                        {{ promptQualityText }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 快速标签 -->
                <div v-if="quickTags.length > 0" class="flex flex-wrap items-center gap-2">
                  <span class="text-xs text-gray-500">快速添加：</span>
                  <button
                    v-for="tag in quickTags"
                    :key="tag"
                    @click="addQuickTag(tag)"
                    class="px-2 py-1 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-full hover:bg-purple-100 hover:border-purple-300 transition-all duration-200"
                  >
                    {{ tag }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 提示词助手 -->
            <div v-if="showPromptHelper" class="bg-white border border-gray-200 rounded-lg shadow-sm">
              <div class="flex border-b border-gray-200">
                <button
                  v-for="tab in helperTabs"
                  :key="tab.id"
                  @click="activeHelperTab = tab.id"
                  :class="[
                    'flex-1 px-4 py-3 text-sm font-medium transition-all duration-200',
                    activeHelperTab === tab.id
                      ? 'text-purple-600 border-b-2 border-purple-600 bg-purple-50'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  ]"
                >
                  {{ tab.label }}
                </button>
              </div>
              <div class="p-4">
                <div v-if="activeHelperTab === 'subjects'" class="grid grid-cols-2 gap-2">
                  <button
                    v-for="subject in promptSubjects"
                    :key="subject"
                    @click="insertPromptText(subject)"
                    class="px-3 py-2 text-sm text-left text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:border-gray-300 transition-all duration-200"
                  >
                    {{ subject }}
                  </button>
                </div>
                <div v-if="activeHelperTab === 'styles'" class="grid grid-cols-2 gap-2">
                  <button
                    v-for="style in promptStyles"
                    :key="style"
                    @click="insertPromptText(style)"
                    class="px-3 py-2 text-sm text-left text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:border-gray-300 transition-all duration-200"
                  >
                    {{ style }}
                  </button>
                </div>
                <div v-if="activeHelperTab === 'quality'" class="grid grid-cols-2 gap-2">
                  <button
                    v-for="quality in qualityTerms"
                    :key="quality"
                    @click="insertPromptText(quality)"
                      class="px-3 py-2 text-sm text-left text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:border-gray-300 transition-all duration-200"
                    >
                      {{ quality }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 负面提示词 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <div class="w-5 h-5 bg-gradient-to-r from-red-500 to-orange-500 rounded flex items-center justify-center">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" class="text-white">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                    <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                    <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <span>负面提示词（可选）</span>
              </label>
              <span class="text-xs text-gray-500">描述您不想要的元素</span>
            </div>
            <div class="space-y-2">
              <textarea
                v-model="negativePrompt"
                placeholder="例如：模糊、低质量、变形、噪点、水印"
                maxlength="200"
                rows="2"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none transition-all duration-200 text-sm leading-relaxed placeholder-gray-400"
              ></textarea>
              <div class="flex justify-end">
                <span class="text-xs text-gray-500">{{ negativePrompt.length }}/200</span>
              </div>
            </div>
          </div>

          <!-- 快速风格选择 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <div class="w-5 h-5 bg-gradient-to-r from-green-500 to-teal-500 rounded flex items-center justify-center">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" class="text-white">
                    <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                    <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                    <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                    <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <span>绘画风格</span>
              </label>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <button
                v-for="style in styleOptions"
                :key="style.value"
                @click="drawingStore.settings.style = style.value"
                :class="[
                  'relative p-3 rounded-lg border-2 transition-all duration-200 group',
                  drawingStore.settings.style === style.value
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                ]"
              >
                <div
                  class="w-full h-12 rounded-md mb-2 transition-transform duration-200 group-hover:scale-105"
                  :style="{ background: style.gradient }"
                ></div>
                <span
                  class="text-xs font-medium"
                  :class="drawingStore.settings.style === style.value ? 'text-purple-700' : 'text-gray-700'"
                >
                  {{ style.label }}
                </span>
                <div
                  v-if="drawingStore.settings.style === style.value"
                  class="absolute top-2 right-2 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" class="text-white">
                    <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </button>
            </div>
          </div>

          <!-- 图像生成模型选择 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <div class="w-5 h-5 bg-gradient-to-r from-green-500 to-blue-500 rounded flex items-center justify-center">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" class="text-white">
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <span>图像生成模型</span>
              </label>
              <span class="text-xs text-gray-500">选择适合的生成模型</span>
            </div>

            <ImageModelSelector
              v-model="selectedImageModel"
              @model-changed="handleImageModelChange"
            />
          </div>

          <!-- 图像设置 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700">尺寸</label>
              <select
                v-model="drawingStore.settings.size"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm bg-white"
              >
                <option
                  v-for="size in sizeOptions"
                  :key="size.value"
                  :value="size.value"
                >
                  {{ size.label }}
                </option>
              </select>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-700">质量</label>
              <select
                v-model="drawingStore.settings.quality"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm bg-white"
              >
                <option value="standard">标准</option>
                <option value="high">高质量</option>
              </select>
            </div>
          </div>

          <!-- 高级设置 -->
          <div class="space-y-4">
            <button
              @click="showAdvanced = !showAdvanced"
              class="flex items-center justify-between w-full p-3 text-left bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-all duration-200"
            >
              <div class="flex items-center space-x-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-gray-600">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span class="text-sm font-medium text-gray-700">高级设置</span>
              </div>
              <svg
                :class="['w-4 h-4 text-gray-600 transition-transform duration-200', { 'rotate-180': showAdvanced }]"
                viewBox="0 0 24 24"
                fill="none"
              >
                <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>

            <div v-if="showAdvanced" class="space-y-4 p-4 bg-white border border-gray-200 rounded-lg">
              <div class="space-y-3">
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700">生成步数</label>
                    <span class="text-sm font-bold text-purple-600">{{ drawingStore.settings.steps }}</span>
                  </div>
                  <input
                    type="range"
                    v-model="drawingStore.settings.steps"
                    min="10"
                    max="50"
                    step="5"
                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                </div>

                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700">引导强度</label>
                    <span class="text-sm font-bold text-purple-600">{{ drawingStore.settings.guidance }}</span>
                  </div>
                  <input
                    type="range"
                    v-model="drawingStore.settings.guidance"
                    min="1"
                    max="20"
                    step="0.5"
                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 生成按钮 -->
          <div class="space-y-4">
            <button
              @click="generateImageWithPollinations"
              :disabled="!prompt.trim() || drawingStore.loading"
              class="w-full flex items-center justify-center space-x-3 px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
            >
              <svg
                v-if="drawingStore.loading"
                width="20" height="20" viewBox="0 0 24 24" fill="none"
                class="animate-spin"
              >
                <path d="M21 12a9 9 0 1 1-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ drawingStore.loading ? '创作中...' : '开始创作' }}</span>
            </button>

            <!-- 进度条 -->
            <div v-if="drawingStore.loading" class="space-y-3">
              <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  class="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-300 ease-out"
                  :style="{ width: drawingStore.progress + '%' }"
                ></div>
              </div>
              <p class="text-sm text-center text-gray-600">
                正在生成您的艺术作品... {{ drawingStore.progress }}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧结果展示 -->
      <div class="flex-1 bg-white/60 backdrop-blur-sm border-l border-gray-200/50">
        <!-- 当前生成结果 -->
        <div class="h-full flex flex-col">
          <div v-if="drawingStore.currentDrawing" class="flex-1 flex flex-col">
            <div class="flex items-center justify-between p-6 border-b border-gray-200/50">
              <h3 class="text-lg font-semibold text-gray-900">创作结果</h3>
              <div class="flex items-center space-x-2">
                <button
                  @click="downloadImage(drawingStore.currentDrawing)"
                  :disabled="!drawingStore.currentDrawing.imageUrl"
                  class="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="下载图片"
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
                <button
                  @click="shareDrawing(drawingStore.currentDrawing.id)"
                  :disabled="!drawingStore.currentDrawing.imageUrl"
                  class="action-btn"
                  title="分享作品"
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <circle cx="18" cy="5" r="3" stroke="currentColor" stroke-width="2"/>
                    <circle cx="6" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <circle cx="18" cy="19" r="3" stroke="currentColor" stroke-width="2"/>
                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="currentColor" stroke-width="2"/>
                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <button
                  @click="toggleFavorite(drawingStore.currentDrawing.id)"
                  :disabled="!drawingStore.currentDrawing.imageUrl"
                  :class="['action-btn', { favorite: drawingStore.currentDrawing.isFavorite }]"
                  title="收藏作品"
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" stroke="currentColor" stroke-width="2" :fill="drawingStore.currentDrawing.isFavorite ? 'currentColor' : 'none'"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="image-container">
              <div
                v-if="drawingStore.currentDrawing.status === 'processing'"
                class="loading-placeholder"
              >
                <div class="loading-animation">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" class="loading-icon">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <p>AI正在创作中...</p>
              </div>

              <div
                v-else-if="drawingStore.currentDrawing.status === 'failed'"
                class="error-placeholder"
              >
                <div class="error-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                    <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                    <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <p>生成失败，请重试</p>
                <button @click="regenerateImage" class="retry-btn">重新生成</button>
              </div>

              <div
                v-else-if="drawingStore.currentDrawing.imageUrl"
                class="result-image"
              >
                <img
                  :src="drawingStore.currentDrawing.imageUrl"
                  :alt="drawingStore.currentDrawing.prompt"
                  @click="previewImage(drawingStore.currentDrawing.imageUrl)"
                />
                <div class="image-overlay">
                  <button @click="previewImage(drawingStore.currentDrawing.imageUrl)" class="preview-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 提示词显示 -->
            <div v-if="drawingStore.currentDrawing.prompt" class="prompt-display">
              <div class="prompt-section">
                <h4>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  提示词
                </h4>
                <p>{{ drawingStore.currentDrawing.prompt }}</p>
              </div>
              <div v-if="drawingStore.currentDrawing.negativePrompt" class="negative-section">
                <h4>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                    <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  负面提示词
                </h4>
                <p>{{ drawingStore.currentDrawing.negativePrompt }}</p>
              </div>
            </div>
          </div>

          <div v-else class="empty-state">
            <div class="empty-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3>开始您的创作</h3>
            <p>输入描述并点击"开始创作"，AI将为您生成独特的艺术作品</p>
          </div>
        </div>

        <!-- 历史记录 -->
        <div class="history-section">
          <div class="section-header">
            <h3>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
              </svg>
              创作历史
            </h3>
            <button @click="clearHistory" class="clear-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" stroke-width="2"/>
              </svg>
              清空
            </button>
          </div>

          <div v-if="drawingStore.hasDrawings" class="history-grid">
            <div
              v-for="drawing in drawingStore.drawings.slice(0, 12)"
              :key="drawing.id"
              class="history-item"
              @click="selectDrawing(drawing)"
            >
              <div class="history-image">
                <img
                  v-if="drawing.thumbnailUrl"
                  :src="drawing.thumbnailUrl"
                  :alt="drawing.prompt"
                />
                <div v-else class="image-placeholder">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                    <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>

                <!-- 状态指示器 -->
                <div v-if="drawing.status === 'processing'" class="status-indicator processing">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="loading">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div v-else-if="drawing.status === 'failed'" class="status-indicator failed">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                    <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>

                <!-- 悬停操作 -->
                <div class="item-overlay">
                  <button @click.stop="selectDrawing(drawing)" class="overlay-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                  <button @click.stop="downloadImage(drawing)" class="overlay-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="history-info">
                <p class="history-prompt">{{ drawing.prompt.slice(0, 25) }}...</p>
                <span class="history-time">{{ formatTime(drawing.createdAt) }}</span>
              </div>
            </div>
          </div>

          <div v-else class="empty-history">
            <div class="empty-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <p>暂无创作历史</p>
            <span>开始您的第一次AI创作吧！</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图像预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="图像预览"
      width="80%"
      center
    >
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图像" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatRelativeTime } from '@/utils/common'
import { APP_CONFIG } from '@/config'
import ImageModelSelector from '@/components/image/ImageModelSelector.vue'
import imageGenerationService from '@/services/imageGenerationService.js'

// 使用响应式引用来安全地初始化 store
const drawingStore = ref(null)

// 响应式数据
const prompt = ref('')
const negativePrompt = ref('')
const showAdvanced = ref(false)
const showPreview = ref(false)
const previewImageUrl = ref('')
const selectedImageModel = ref('pollinations-default')

// 新增功能状态
const showPromptHelper = ref(false)
const showTemplates = ref(false)
const showHistory = ref(false)
const activeHelperTab = ref('subjects')
const suggestedTags = ref([])
const promptInput = ref(null)

// 智能功能状态
const showSuggestions = ref(false)
const suggestions = ref([])
const quickTags = ref(['高质量', '细节丰富', '4K', '专业摄影', '艺术杰作'])
const isProFeature = ref(false)

// 用户配额
const remainingQuota = ref(47)
const dailyQuota = ref(50)

// 配置选项
const styleOptions = [
  { value: 'realistic', label: '写实风格', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { value: 'anime', label: '动漫风格', gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
  { value: 'oil_painting', label: '油画风格', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
  { value: 'watercolor', label: '水彩风格', gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' },
  { value: 'sketch', label: '素描风格', gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
  { value: 'digital_art', label: '数字艺术', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' }
]

const sizeOptions = [
  { value: '512x512', label: '正方形 (512×512)' },
  { value: '768x512', label: '横向 (768×512)' },
  { value: '512x768', label: '纵向 (512×768)' },
  { value: '1024x1024', label: '高清正方形 (1024×1024)' }
]

// 提示词助手数据
const helperTabs = [
  { id: 'subjects', label: '主题' },
  { id: 'styles', label: '风格' },
  { id: 'quality', label: '质量' }
]

const promptSubjects = [
  '美丽的女孩', '帅气的男孩', '可爱的动物', '神秘的森林', '未来城市',
  '古代建筑', '梦幻花园', '星空夜景', '海底世界', '魔法城堡',
  '机械战士', '精灵公主', '龙族传说', '太空探索', '时光隧道'
]

const promptStyles = [
  '水彩画风格', '油画质感', '素描线条', '动漫风格', '写实主义',
  '印象派', '抽象艺术', '超现实主义', '赛博朋克', '蒸汽朋克',
  '日式浮世绘', '中国水墨画', '欧洲古典', '现代简约', '梦幻唯美'
]

const qualityTerms = [
  '高质量', '超高清', '4K分辨率', '细节丰富', '专业摄影',
  '电影级画质', '艺术杰作', '获奖作品', '完美构图', '光影效果',
  '色彩鲜艳', '对比强烈', '景深效果', '柔和光线', '戏剧性照明'
]

// 计算属性
const wordCount = computed(() => {
  return prompt.value.trim().split(/\s+/).filter(word => word.length > 0).length
})

const promptQuality = computed(() => {
  const length = prompt.value.length
  const words = wordCount.value

  if (length < 20 || words < 5) return 'poor'
  if (length < 50 || words < 10) return 'fair'
  if (length < 150 || words < 25) return 'good'
  return 'excellent'
})

const promptQualityText = computed(() => {
  const quality = promptQuality.value
  const qualityMap = {
    poor: '较差',
    fair: '一般',
    good: '良好',
    excellent: '优秀'
  }
  return qualityMap[quality] || '未知'
})

const quotaPercentage = computed(() => {
  return (remainingQuota.value / dailyQuota.value) * 100
})

// 方法
const generateImageWithPollinations = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入图像描述')
    return
  }

  if (remainingQuota.value <= 0) {
    ElMessage.warning('今日生成配额已用完')
    return
  }

  try {
    // 显示加载状态
    drawingStore.setLoading(true)
    ElMessage.info('正在生成图像，请稍候...')

    // 解析尺寸设置
    const [width, height] = drawingStore.settings.size.split('x').map(Number)

    // 构建生成选项
    const options = {
      width,
      height,
      enhance: drawingStore.settings.quality === 'high',
      safe: true,
      model: selectedImageModel.value
    }

    // 调用图像生成服务
    const result = await imageGenerationService.generateImage(prompt.value, options)

    if (result.success) {
      // 创建绘画记录
      const drawing = {
        id: Date.now().toString(),
        prompt: prompt.value,
        negativePrompt: negativePrompt.value,
        imageUrl: result.imageUrl,
        directUrl: result.directUrl,
        seed: result.seed,
        model: selectedImageModel.value,
        settings: { ...drawingStore.settings },
        createdAt: new Date().toISOString(),
        isFavorite: false
      }

      // 添加到历史记录
      drawingStore.addDrawing(drawing)
      drawingStore.setCurrentDrawing(drawing)

      // 减少配额
      remainingQuota.value--

      ElMessage.success('图像生成成功！')
    } else {
      throw new Error(result.error || '图像生成失败')
    }
  } catch (error) {
    console.error('图像生成错误:', error)
    ElMessage.error(error.message || '图像生成失败，请稍后重试')
  } finally {
    drawingStore.setLoading(false)
  }
}

// 处理图像模型变化
const handleImageModelChange = (model) => {
  ElMessage.success(`已切换到 ${model.name}`)
}

const regenerateImage = async () => {
  if (drawingStore.currentDrawing) {
    await drawingStore.regenerateImage(drawingStore.currentDrawing.id)
  }
}

const selectDrawing = (drawing) => {
  drawingStore.setCurrentDrawing(drawing)
  prompt.value = drawing.prompt
  negativePrompt.value = drawing.negativePrompt || ''
}

const downloadImage = (drawing) => {
  drawingStore.downloadImage(drawing)
}

const shareDrawing = async (drawingId) => {
  await drawingStore.shareDrawing(drawingId, true)
}

const toggleFavorite = (drawingId) => {
  drawingStore.toggleFavorite(drawingId)
}

const previewImage = (imageUrl) => {
  previewImageUrl.value = imageUrl
  showPreview.value = true
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有历史记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    drawingStore.clearDrawingHistory()
  } catch (error) {
    // 用户取消
  }
}

// 新增功能方法
const handlePromptInput = () => {
  // 分析提示词并生成建议标签
  const words = prompt.value.toLowerCase().split(/[,，\s]+/).filter(word => word.length > 1)
  const tags = []

  // 根据关键词推荐相关标签
  words.forEach(word => {
    if (word.includes('女') || word.includes('girl')) {
      tags.push('美丽', '优雅', '迷人')
    }
    if (word.includes('男') || word.includes('boy')) {
      tags.push('帅气', '英俊', '强壮')
    }
    if (word.includes('动物') || word.includes('cat') || word.includes('dog')) {
      tags.push('可爱', '毛茸茸', '活泼')
    }
    if (word.includes('风景') || word.includes('landscape')) {
      tags.push('壮观', '宁静', '自然')
    }
  })

  suggestedTags.value = [...new Set(tags)].slice(0, 5)
}

const addTag = (tag) => {
  if (!prompt.value.includes(tag)) {
    prompt.value += (prompt.value ? ', ' : '') + tag
    handlePromptInput()
  }
}

const insertPromptText = (text) => {
  const textarea = promptInput.value
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const currentValue = prompt.value

    const newValue = currentValue.substring(0, start) +
                    (start > 0 && currentValue[start - 1] !== ' ' && currentValue[start - 1] !== ',' ? ', ' : '') +
                    text +
                    (end < currentValue.length && currentValue[end] !== ' ' && currentValue[end] !== ',' ? ', ' : '') +
                    currentValue.substring(end)

    prompt.value = newValue

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus()
      const newPosition = start + text.length + (start > 0 ? 2 : 0)
      textarea.setSelectionRange(newPosition, newPosition)
    }, 0)
  }
}

const translatePrompt = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请先输入提示词')
    return
  }

  try {
    ElMessage.info('智能翻译中...')
    // 模拟翻译过程
    setTimeout(() => {
      // 简单的中英文检测和转换示例
      const hasChineseChars = /[\u4e00-\u9fa5]/.test(prompt.value)
      if (hasChineseChars) {
        // 模拟中译英
        ElMessage.success('翻译完成')
      } else {
        // 模拟英译中
        ElMessage.success('翻译完成')
      }
    }, 1000)
  } catch (error) {
    ElMessage.error('翻译失败')
  }
}

// 新增智能功能方法
const enhancePrompt = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请先输入基础描述')
    return
  }

  try {
    ElMessage.info('AI正在增强您的提示词...')

    // 模拟AI增强过程
    setTimeout(() => {
      const enhancements = [
        ', 高质量, 细节丰富',
        ', 专业摄影, 完美构图',
        ', 4K分辨率, 超高清',
        ', 艺术杰作, 获奖作品',
        ', 电影级画质, 戏剧性照明'
      ]

      const randomEnhancement = enhancements[Math.floor(Math.random() * enhancements.length)]
      if (!prompt.value.includes(randomEnhancement.substring(2))) {
        prompt.value += randomEnhancement
        handlePromptInput()
        ElMessage.success('提示词已增强')
      } else {
        ElMessage.info('提示词已经很完善了')
      }
    }, 1500)
  } catch (error) {
    ElMessage.error('增强失败')
  }
}

const pasteFromClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText()
    if (text.trim()) {
      prompt.value = text.trim()
      handlePromptInput()
      ElMessage.success('已粘贴剪贴板内容')
    } else {
      ElMessage.warning('剪贴板为空')
    }
  } catch (error) {
    ElMessage.error('无法访问剪贴板')
  }
}

const clearPrompt = () => {
  prompt.value = ''
  suggestedTags.value = []
  showSuggestions.value = false
  ElMessage.info('已清空提示词')
}

const handleKeyDown = (event) => {
  // Ctrl+Enter 快速生成
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    generateImage()
  }

  // Tab 键自动补全建议
  if (event.key === 'Tab' && suggestions.value.length > 0) {
    event.preventDefault()
    applySuggestion(suggestions.value[0])
  }
}

const applySuggestion = (suggestion) => {
  if (!prompt.value.includes(suggestion)) {
    prompt.value += (prompt.value ? ', ' : '') + suggestion
    handlePromptInput()
  }
  showSuggestions.value = false
}

const addQuickTag = (tag) => {
  if (!prompt.value.includes(tag)) {
    prompt.value += (prompt.value ? ', ' : '') + tag
    handlePromptInput()
    ElMessage.success(`已添加标签: ${tag}`)
  } else {
    ElMessage.info('标签已存在')
  }
}

const randomPrompt = () => {
  const subjects = promptSubjects[Math.floor(Math.random() * promptSubjects.length)]
  const styles = promptStyles[Math.floor(Math.random() * promptStyles.length)]
  const quality = qualityTerms[Math.floor(Math.random() * qualityTerms.length)]

  prompt.value = `${subjects}, ${styles}, ${quality}`
  handlePromptInput()
  ElMessage.success('已生成随机提示词')
}

const formatTime = (timestamp) => {
  return formatRelativeTime(timestamp)
}

// 生命周期
onMounted(() => {
  // 加载绘画历史
  drawingStore.loadDrawingHistory()
})
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  background: #e5e7eb;
  border-radius: 4px;
}

.slider::-moz-range-track {
  background: #e5e7eb;
  border-radius: 4px;
}

/* 移动端适配样式 */
@media (max-width: 768px) {
  .min-h-screen {
    padding: 0;
  }

  .flex.h-screen {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  /* 侧边栏移动端适配 */
  .w-80 {
    width: 100%;
    max-width: none;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  /* 主内容区域 */
  .flex-1 {
    width: 100%;
    padding: 1rem;
  }

  /* 网格布局调整 */
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .grid.grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 图片网格 */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* 输入区域 */
  .space-y-4 > * {
    margin-bottom: 1rem;
  }

  /* 按钮组 */
  .flex.space-x-2 {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* 文本区域 */
  .min-h-\\[120px\\] {
    min-height: 100px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 选择器 */
  .w-full.px-3.py-2 {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 图片容器 */
  .aspect-square {
    aspect-ratio: 1;
  }

  /* 模态框 */
  .fixed.inset-0 {
    padding: 1rem;
  }

  .max-w-4xl {
    max-width: 100%;
  }

  /* 工具栏 */
  .absolute.top-4.right-4 {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 1rem;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  /* 更小屏幕优化 */
  .p-6 {
    padding: 1rem;
  }

  .p-4 {
    padding: 0.75rem;
  }

  /* 按钮更紧凑 */
  .px-6.py-3 {
    padding: 0.5rem 1rem;
  }

  .px-4.py-2 {
    padding: 0.375rem 0.75rem;
  }

  /* 文字大小调整 */
  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.125rem;
  }

  /* 间距调整 */
  .space-y-6 > * {
    margin-bottom: 1rem;
  }

  .space-y-4 > * {
    margin-bottom: 0.75rem;
  }

  /* 图片网格单列 */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }

  /* 设置网格单列 */
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
  }

  /* 滑块容器 */
  .space-y-2 {
    margin-bottom: 1rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover\\:bg-gray-100:hover {
    background-color: #f3f4f6;
  }

  .hover\\:bg-purple-700:hover {
    background-color: #7c3aed;
  }

  .hover\\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
}

/* iOS Safari 适配 */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }

  .h-screen {
    height: -webkit-fill-available;
  }
}

/* 滚动条优化 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }
}
</style>