# Targon API 集成文档

## 概述

本项目已成功集成 Targon API，提供多种主流AI模型的统一接口访问。Targon API 兼容 OpenAI API 格式，支持多种先进的AI模型。

## 支持的模型

**🔄 动态模型加载**

系统会自动从 Targon API 获取最新的可用模型列表，无需手动维护。支持的模型包括但不限于：

### DeepSeek 系列
- DeepSeek-V3 系列 - 强大的推理能力
- DeepSeek-R1 系列 - 推理专用模型

### Moonshot 系列
- Kimi 系列 - Moonshot AI 的指令模型

### Qwen 系列
- Qwen3 系列 - 通义千问3代码和指令模型

### 其他模型
- 系统会自动发现并显示所有可用模型
- 模型列表实时更新，确保使用最新版本

## API 配置

### 基本信息
- **API 地址**: `https://api.targon.com/v1`
- **兼容性**: OpenAI API 格式
- **认证方式**: Bearer Token

### 配置步骤

1. **获取 API 密钥**
   - 访问 Targon 官网注册账号
   - 获取您的 API 密钥

2. **在系统中配置**
   - 访问 `/api-key-demo` 页面
   - 选择 "Targon" 提供商
   - 输入您的 API 密钥
   - 测试连接确保配置正确

## 使用方式

### 1. 通过配置界面
访问 `http://localhost:3000/api-key-demo` 查看完整的配置演示。

### 2. 通过专用演示页面
访问 `http://localhost:3000/targon-api-demo` 体验 Targon API 的完整功能。

### 3. 编程方式使用

```javascript
import targonApi from '@/services/targonApi'

// 配置 API
targonApi.setApiKey('your-api-key-here')
targonApi.setBaseURL('https://api.targon.com/v1')

// 测试连接
const testResult = await targonApi.testConnection()
console.log(testResult)

// 发送聊天请求
const chatResult = await targonApi.chat({
  model: 'deepseek-ai/DeepSeek-V3',
  messages: [
    { role: 'user', content: '你好，请介绍一下自己' }
  ],
  temperature: 0.7,
  max_tokens: 2048
})

console.log(chatResult.data.choices[0].message.content)
```

## API 服务类功能

### TargonApiService 类

位于 `src/services/targonApi.js`，提供以下功能：

#### 基本配置
- `setApiKey(apiKey)` - 设置API密钥
- `setBaseURL(baseURL)` - 设置基础URL
- `createClient()` - 创建HTTP客户端

#### 连接测试
- `testConnection()` - 测试API连接状态
- `getModels()` - 获取可用模型列表

#### 聊天功能
- `chat(params)` - 发送聊天请求
- `streamChat(params, onMessage, onError, onComplete)` - 流式聊天

#### 工具方法
- `getErrorMessage(error)` - 获取友好的错误信息
- `getPresetModels()` - 获取预设模型配置

## 错误处理

系统提供完善的错误处理机制：

- **401**: API密钥无效或已过期
- **403**: 访问被拒绝，请检查API密钥权限
- **429**: 请求频率过高，请稍后重试
- **500**: 服务器内部错误
- **502/503/504**: 服务暂时不可用

## 功能特性

### 1. 🔄 动态模型加载
- 自动从 API 获取最新模型列表
- 无需手动维护模型配置
- 实时更新可用模型
- 智能模型分类和显示

### 2. 🔌 统一接口
兼容 OpenAI API 格式，易于集成和使用。

### 3. 🧪 连接测试
提供一键测试功能，确保配置正确。

### 4. 📡 流式输出
支持流式聊天，提供更好的用户体验。

### 5. ⚠️ 错误处理
完善的错误处理和用户友好的错误提示。

### 6. 🎯 智能选择
- 输入API密钥后自动获取模型
- 自动选择第一个可用模型
- 支持模型搜索和筛选

## 页面路由

- `/api-key-demo` - API密钥配置演示页面
- `/targon-api-demo` - Targon API 专用演示页面
- `/api-key-management` - 完整的API密钥管理页面

## 注意事项

1. **API密钥安全**: API密钥仅存储在本地浏览器中，不会上传到服务器
2. **网络连接**: 确保网络可以访问 `https://api.targon.com`
3. **模型选择**: 不同模型有不同的特性和限制，请根据需求选择
4. **请求频率**: 注意API的请求频率限制，避免触发429错误

## 更新日志

### v1.1.0 (2025-01-01)
- ✅ **动态模型加载** - 自动从API获取最新模型列表
- ✅ **智能模型选择** - 输入密钥后自动获取并选择模型
- ✅ **实时模型更新** - 支持刷新和实时更新模型列表
- ✅ **模型搜索筛选** - 支持模型名称搜索和筛选
- ✅ **优化用户体验** - 更好的加载状态和错误提示

### v1.0.0 (2025-01-01)
- ✅ 集成 Targon API 支持
- ✅ 添加多种 DeepSeek、Moonshot、Qwen 模型
- ✅ 创建专用的 TargonApiService 服务类
- ✅ 添加 Targon API 演示页面
- ✅ 完善错误处理和用户体验
- ✅ 添加连接测试和模型列表获取功能

## 技术支持

如果在使用过程中遇到问题，请：

1. 检查 API 密钥是否正确
2. 确认网络连接正常
3. 查看浏览器控制台的错误信息
4. 参考本文档的错误处理部分

---

**开发团队**: AI创意平台开发组  
**更新时间**: 2025-01-01  
**版本**: v1.0.0
