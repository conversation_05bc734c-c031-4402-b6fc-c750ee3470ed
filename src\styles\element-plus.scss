// Element Plus 主题定制 - 使用现代@use语法
@use './variables.scss' as *;

// 现代化Element Plus主题定制
:root {
  // 主色彩系统
  --el-color-primary: #6366f1;
  --el-color-primary-light-3: #8b5cf6;
  --el-color-primary-light-5: #a78bfa;
  --el-color-primary-light-7: #c4b5fd;
  --el-color-primary-light-9: #e0e7ff;
  --el-color-primary-dark-2: #4f46e5;

  --el-color-success: #10b981;
  --el-color-warning: #f59e0b;
  --el-color-danger: #ef4444;
  --el-color-info: #3b82f6;

  // 文字颜色
  --el-text-color-primary: #0f172a;
  --el-text-color-regular: #475569;
  --el-text-color-secondary: #64748b;
  --el-text-color-placeholder: #cbd5e1;

  // 边框颜色
  --el-border-color: #e2e8f0;
  --el-border-color-light: #f1f5f9;
  --el-border-color-lighter: #f8fafc;
  --el-border-color-extra-light: #fefefe;

  // 背景颜色
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f8fafc;
  --el-bg-color-overlay: rgba(0, 0, 0, 0.5);

  // 圆角系统
  --el-border-radius-base: 8px;
  --el-border-radius-small: 4px;
  --el-border-radius-round: 24px;
  --el-border-radius-circle: 50%;

  // 阴影系统
  --el-box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --el-box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --el-box-shadow-dark: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// 确保Element Plus组件有正确的文字颜色和背景
.el-message-box {
  background-color: #ffffff !important;
  color: #303133 !important;

  .el-message-box__title {
    color: #303133 !important;
  }

  .el-message-box__content {
    color: #606266 !important;
  }

  .el-message-box__message {
    color: #606266 !important;
  }
}

.el-dialog {
  background-color: #ffffff !important;
  color: #303133 !important;

  .el-dialog__title {
    color: #303133 !important;
  }

  .el-dialog__body {
    color: #606266 !important;
  }
}

.el-message {
  color: #ffffff !important;

  .el-message__content {
    color: #ffffff !important;
  }
}

.el-notification {
  background-color: #ffffff !important;
  border: 1px solid #ebeef5 !important;

  .el-notification__title {
    color: #303133 !important;
  }

  .el-notification__content {
    color: #606266 !important;
  }
}

// 自定义组件样式
.el-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-aside {
  background-color: #ffffff;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.el-main {
  background-color: #f2f3f5;
  padding: 24px;
}

// 菜单样式定制
.el-menu {
  border-right: none;

  .el-menu-item {
    &:hover {
      background-color: #ecf5ff;
      color: #667eea;
    }

    &.is-active {
      background-color: #e1f3d8;
      color: #667eea;
      border-right: 3px solid #667eea;
    }
  }

  .el-sub-menu__title {
    &:hover {
      background-color: #ecf5ff;
      color: #667eea;
    }
  }
}

// 现代化按钮样式定制
.el-button {
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);

    &::before {
      left: 100%;
    }
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }
  }

  &.el-button--large {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 16px;
  }

  &.el-button--small {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 8px;
  }
}

// 卡片样式定制
.el-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    background-color: #fafafa;
    border-bottom: 1px solid #f2f6fc;
  }
}

// 对话框样式定制 - 移除可能导致问题的自定义样式
.el-dialog {
  border-radius: 4px;
  background-color: #ffffff !important;

  .el-dialog__header {
    background-color: #ffffff !important;
    color: #303133 !important;
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      color: #303133 !important;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #909399 !important;

        &:hover {
          color: #667eea !important;
        }
      }
    }
  }

  .el-dialog__body {
    background-color: #ffffff !important;
    color: #606266 !important;
    padding: 24px;
  }

  .el-dialog__footer {
    background-color: #ffffff !important;
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
  }
}

// 表格样式定制
.el-table {
  .el-table__header {
    background-color: #fafafa;

    th {
      background-color: #fafafa;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #ecf5ff;
    }
  }
}

// 分页样式定制
.el-pagination {
  .el-pager li {
    &.is-active {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    &:hover {
      color: #667eea;
    }
  }
}

// 输入框样式定制
.el-input {
  .el-input__wrapper {
    border-radius: 4px;

    &:hover {
      box-shadow: 0 0 0 1px #c6d1f0;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #667eea;
    }
  }
}

// 消息提示样式定制
.el-message {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #ffffff !important;

  &.el-message--success {
    background-color: #67c23a !important;
    border: none;
  }

  &.el-message--warning {
    background-color: #e6a23c !important;
    border: none;
  }

  &.el-message--error {
    background-color: #f56c6c !important;
    border: none;
  }

  &.el-message--info {
    background-color: #909399 !important;
    border: none;
  }
}

// 加载样式定制
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);

  .el-loading-spinner {
    .el-loading-text {
      color: #667eea;
    }
  }
}

// 标签页样式定制
.el-tabs {
  .el-tabs__header {
    margin: 0;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__item {
    &.is-active {
      color: #667eea;
      border-bottom-color: #667eea;
    }

    &:hover {
      color: #7c8ceb;
    }
  }
}
