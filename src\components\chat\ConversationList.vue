<template>
  <div class="conversations-list">
    <div v-if="conversations.length === 0" class="empty-conversations">
      <div class="empty-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <p>暂无对话记录</p>
    </div>
    
    <ConversationItem
      v-for="conversation in conversations"
      :key="conversation.id"
      :conversation="conversation"
      :is-active="conversation.id === currentConversationId"
      :collapsed="collapsed"
      @click="$emit('switch-conversation', conversation.id)"
      @command="$emit('conversation-command', $event)"
    />
  </div>
</template>

<script setup>
import ConversationItem from './ConversationItem.vue'

defineProps({
  conversations: {
    type: Array,
    default: () => []
  },
  currentConversationId: {
    type: String,
    default: null
  },
  collapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['switch-conversation', 'conversation-command'])
</script>

<style lang="scss" scoped>
.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(226, 232, 240, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(226, 232, 240, 0.5);
    }
  }

  .empty-conversations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    color: #64748b;

    .empty-icon {
      margin-bottom: 1rem;
      opacity: 0.7;
      color: #667eea;
    }

    p {
      font-size: 0.9rem;
      margin: 0;
      color: #94a3b8;
    }
  }
}
</style>
