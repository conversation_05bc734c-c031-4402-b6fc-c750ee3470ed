// 开发环境配置 - 避免网络依赖
export const DEV_CONFIG = {
  // 开发环境下禁用网络相关功能
  disableNetworkFeatures: import.meta.env.DEV,
  
  // 模拟的API密钥配置
  mockApiKeys: [
    'sk-or-v1-mock-key-1-for-development',
    'sk-or-v1-mock-key-2-for-development',
    'sk-or-v1-mock-key-3-for-development'
  ],
  
  // 模拟的密钥统计
  mockKeyStats: [
    {
      index: 1,
      isActive: true,
      isCurrent: true,
      totalRequests: 15,
      successfulRequests: 14,
      successRate: '93.3',
      failureCount: 1,
      lastUsed: new Date(),
      lastSuccess: new Date(),
      lastFailure: null,
      responseTime: 250,
      keyPreview: 'sk-or-v1-mock-key-1...'
    },
    {
      index: 2,
      isActive: true,
      isCurrent: false,
      totalRequests: 8,
      successfulRequests: 8,
      successRate: '100.0',
      failureCount: 0,
      lastUsed: new Date(Date.now() - 300000),
      lastSuccess: new Date(Date.now() - 300000),
      lastFailure: null,
      responseTime: 180,
      keyPreview: 'sk-or-v1-mock-key-2...'
    },
    {
      index: 3,
      isActive: true,
      isCurrent: false,
      totalRequests: 3,
      successfulRequests: 2,
      successRate: '66.7',
      failureCount: 1,
      lastUsed: new Date(Date.now() - 600000),
      lastSuccess: new Date(Date.now() - 600000),
      lastFailure: new Date(Date.now() - 300000),
      responseTime: 320,
      keyPreview: 'sk-or-v1-mock-key-3...'
    }
  ],
  
  // 模拟的API配置
  mockApiConfig: {
    totalKeys: 3,
    activeKeys: 3,
    keyRotationEnabled: true,
    healthCheckInterval: 300000,
    failureThreshold: 3,
    maxRetries: 3,
    timeout: 60000,
    baseUrl: 'https://openrouter.ai/api/v1'
  }
}

// 检查是否应该使用模拟数据
export function shouldUseMockData() {
  return DEV_CONFIG.disableNetworkFeatures || import.meta.env.VITE_USE_MOCK_DATA === 'true'
}

// 获取模拟的密钥统计
export function getMockKeyStats() {
  return DEV_CONFIG.mockKeyStats
}

// 获取模拟的API配置
export function getMockApiConfig() {
  return DEV_CONFIG.mockApiConfig
}

// 模拟API密钥管理器
export class MockApiKeyManager {
  constructor() {
    this.keyStats = [...DEV_CONFIG.mockKeyStats]
    this.config = { ...DEV_CONFIG.mockApiConfig }
    this.currentKeyIndex = 0
  }
  
  getCurrentKey() {
    return DEV_CONFIG.mockApiKeys[this.currentKeyIndex] || DEV_CONFIG.mockApiKeys[0]
  }
  
  rotateKey() {
    this.currentKeyIndex = (this.currentKeyIndex + 1) % DEV_CONFIG.mockApiKeys.length
    
    // 更新当前密钥标记
    this.keyStats.forEach((key, index) => {
      key.isCurrent = index === this.currentKeyIndex
    })
    
    console.log(`模拟密钥轮换到 #${this.currentKeyIndex + 1}`)
  }
  
  markCurrentKeyFailed(error = null) {
    const currentKey = this.keyStats[this.currentKeyIndex]
    if (currentKey) {
      currentKey.failureCount++
      currentKey.lastFailure = new Date()
      currentKey.totalRequests++
      currentKey.successRate = ((currentKey.successfulRequests / currentKey.totalRequests) * 100).toFixed(1)
      
      if (error) {
        console.warn(`模拟密钥 #${this.currentKeyIndex + 1} 失败:`, error.message || error)
      }
      
      // 如果失败次数过多，轮换密钥
      if (currentKey.failureCount >= this.config.failureThreshold) {
        this.rotateKey()
      }
    }
  }
  
  markCurrentKeySuccess(responseTime = 200) {
    const currentKey = this.keyStats[this.currentKeyIndex]
    if (currentKey) {
      currentKey.totalRequests++
      currentKey.successfulRequests++
      currentKey.lastUsed = new Date()
      currentKey.lastSuccess = new Date()
      currentKey.responseTime = responseTime
      currentKey.successRate = ((currentKey.successfulRequests / currentKey.totalRequests) * 100).toFixed(1)
      
      // 重置失败计数
      if (currentKey.failureCount > 0) {
        currentKey.failureCount = Math.max(0, currentKey.failureCount - 1)
      }
    }
  }
  
  async performHealthCheck() {
    console.log('执行模拟健康检查...')
    
    // 模拟健康检查延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 随机恢复一些失败的密钥
    this.keyStats.forEach(key => {
      if (!key.isActive && Math.random() > 0.5) {
        key.isActive = true
        key.failureCount = 0
        console.log(`模拟密钥 #${key.index} 健康检查通过，重新激活`)
      }
    })
    
    console.log('模拟健康检查完成')
  }
  
  getKeyStats() {
    return this.keyStats.map(key => ({ ...key }))
  }
  
  getConfig() {
    return { ...this.config }
  }
  
  addKey(apiKey) {
    const newIndex = this.keyStats.length
    this.keyStats.push({
      index: newIndex + 1,
      isActive: true,
      isCurrent: false,
      totalRequests: 0,
      successfulRequests: 0,
      successRate: '0.0',
      failureCount: 0,
      lastUsed: null,
      lastSuccess: null,
      lastFailure: null,
      responseTime: 0,
      keyPreview: apiKey.substring(0, 20) + '...'
    })
    
    this.config.totalKeys++
    this.config.activeKeys++
    
    console.log(`模拟添加密钥 #${newIndex + 1}`)
    return newIndex
  }
  
  removeKey(index) {
    if (index < 0 || index >= this.keyStats.length) {
      throw new Error('无效的密钥索引')
    }
    
    if (this.keyStats.length <= 1) {
      throw new Error('至少需要保留一个API密钥')
    }
    
    this.keyStats.splice(index, 1)
    
    // 重新编号
    this.keyStats.forEach((key, newIndex) => {
      key.index = newIndex + 1
    })
    
    // 调整当前密钥索引
    if (this.currentKeyIndex >= index) {
      this.currentKeyIndex = Math.max(0, this.currentKeyIndex - 1)
    }
    
    this.config.totalKeys--
    this.config.activeKeys--
    
    console.log(`模拟删除密钥 #${index + 1}`)
  }
  
  resetAllKeys() {
    this.keyStats.forEach(key => {
      key.isActive = true
      key.failureCount = 0
    })
    
    this.config.activeKeys = this.config.totalKeys
    console.log('模拟重置所有密钥状态')
  }
}

// 创建模拟实例
let _mockApiKeyManager = null

export function getMockApiKeyManager() {
  if (!_mockApiKeyManager) {
    _mockApiKeyManager = new MockApiKeyManager()
  }
  return _mockApiKeyManager
}

export default DEV_CONFIG
