<!--
  智谱 AI API 演示页面
  展示智谱官方 API 的功能和使用方法
-->
<template>
  <div class="zhipu-api-demo">
    <div class="container">
      <h1 class="page-title">🧠 智谱 AI 官方 API 演示</h1>
      <p class="page-description">
        体验智谱 GLM-4.5 系列模型的强大能力，支持 GLM-4.5 和 GLM-4.5-Air 两个版本
      </p>

      <!-- API 配置区域 -->
      <ZhipuApiConfig />

      <!-- 聊天演示区域 -->
      <el-card class="chat-demo-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>💬 聊天演示</h3>
            <div class="header-actions">
              <el-select v-model="selectedModel" placeholder="选择模型" style="width: 200px">
                <el-option
                  v-for="model in availableModels"
                  :key="model.id"
                  :label="model.name"
                  :value="model.id"
                >
                  <span>{{ model.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ model.description }}
                  </span>
                </el-option>
              </el-select>
              <el-button @click="clearChat" size="small">
                <el-icon><Delete /></el-icon>
                清空对话
              </el-button>
            </div>
          </div>
        </template>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="messagesContainer">
          <div 
            v-for="(message, index) in messages" 
            :key="index"
            class="message-item"
            :class="message.role"
          >
            <div class="message-avatar">
              <el-avatar :size="32">
                {{ message.role === 'user' ? '👤' : '🤖' }}
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">
                  {{ message.role === 'user' ? '用户' : '智谱 AI' }}
                </span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-text" v-html="formatMessage(message.content)"></div>
            </div>
          </div>
          
          <!-- 加载状态 -->
          <div v-if="isLoading" class="message-item assistant loading">
            <div class="message-avatar">
              <el-avatar :size="32">🤖</el-avatar>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">智谱 AI</span>
                <span class="message-time">正在思考...</span>
              </div>
              <div class="message-text">
                <el-icon class="is-loading"><Loading /></el-icon>
                正在生成回复...
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题，体验智谱 AI 的强大能力..."
            @keydown.ctrl.enter="sendMessage"
            :disabled="isLoading"
          />
          <div class="input-actions">
            <div class="input-options">
              <el-checkbox v-model="useStream">流式输出</el-checkbox>
              <el-slider
                v-model="temperature"
                :min="0"
                :max="1"
                :step="0.1"
                show-input
                :show-input-controls="false"
                style="width: 150px"
              />
              <span class="temperature-label">温度: {{ temperature }}</span>
            </div>
            <el-button 
              @click="sendMessage" 
              type="primary" 
              :loading="isLoading"
              :disabled="!inputMessage.trim() || !hasApiKey"
            >
              <el-icon><ChatDotRound /></el-icon>
              发送 (Ctrl+Enter)
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 功能展示区域 -->
      <el-card class="features-card" shadow="hover">
        <template #header>
          <h3>✨ 功能特色</h3>
        </template>

        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <h4>高速响应</h4>
            <p>GLM-4.5-Air 模型优化了推理速度，响应更快</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h4>精准理解</h4>
            <p>强大的中文理解能力，准确把握用户意图</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔧</div>
            <h4>函数调用</h4>
            <p>支持函数调用，可以执行复杂的任务</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">💰</div>
            <h4>成本优化</h4>
            <p>官方 API 直连，成本更低，性价比更高</p>
          </div>
        </div>
      </el-card>

      <!-- 示例问题 -->
      <el-card class="examples-card" shadow="hover">
        <template #header>
          <h3>💡 示例问题</h3>
        </template>

        <div class="examples-grid">
          <div 
            v-for="example in exampleQuestions" 
            :key="example.id"
            class="example-item"
            @click="useExample(example.question)"
          >
            <div class="example-icon">{{ example.icon }}</div>
            <div class="example-content">
              <h5>{{ example.title }}</h5>
              <p>{{ example.question }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Loading, ChatDotRound } from '@element-plus/icons-vue'
import ZhipuApiConfig from '@/components/ZhipuApiConfig.vue'
import zhipuApi from '@/services/zhipuApi'

// 响应式数据
const selectedModel = ref('glm-4.5-air')
const inputMessage = ref('')
const messages = ref([])
const isLoading = ref(false)
const useStream = ref(true)
const temperature = ref(0.7)
const messagesContainer = ref(null)

// 可用模型
const availableModels = ref([
  {
    id: 'glm-4.5-air',
    name: 'GLM-4.5-Air',
    description: '轻量级，速度快'
  },
  {
    id: 'glm-4.5',
    name: 'GLM-4.5',
    description: '标准版，功能全'
  }
])

// 示例问题
const exampleQuestions = ref([
  {
    id: 1,
    icon: '📝',
    title: '文本创作',
    question: '请帮我写一篇关于人工智能发展的短文'
  },
  {
    id: 2,
    icon: '💻',
    title: '代码编程',
    question: '用 Python 写一个计算斐波那契数列的函数'
  },
  {
    id: 3,
    icon: '🤔',
    title: '问题解答',
    question: '请解释一下什么是机器学习，它有哪些应用场景？'
  },
  {
    id: 4,
    icon: '🌟',
    title: '创意思考',
    question: '给我一些提高工作效率的创意建议'
  }
])

// 计算属性
const hasApiKey = computed(() => {
  return zhipuApi.getStatus().hasApiKey
})

// 方法
const sendMessage = async () => {
  if (!inputMessage.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }

  if (!hasApiKey.value) {
    ElMessage.warning('请先配置智谱 API Key')
    return
  }

  const userMessage = {
    role: 'user',
    content: inputMessage.value.trim(),
    timestamp: new Date()
  }

  messages.value.push(userMessage)
  const currentInput = inputMessage.value.trim()
  inputMessage.value = ''
  isLoading.value = true

  try {
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))

    if (useStream.value) {
      // 流式输出
      const assistantMessage = {
        role: 'assistant',
        content: '',
        timestamp: new Date()
      }
      messages.value.push(assistantMessage)

      await zhipuApi.streamChat(
        {
          model: selectedModel.value,
          messages: chatMessages,
          temperature: temperature.value
        },
        // onMessage
        (content) => {
          assistantMessage.content += content
          scrollToBottom()
        },
        // onError
        (error) => {
          console.error('流式聊天失败:', error)
          ElMessage.error('聊天失败: ' + error.message)
          messages.value.pop() // 移除失败的消息
        },
        // onComplete
        () => {
          console.log('流式聊天完成')
        }
      )
    } else {
      // 普通输出
      const result = await zhipuApi.chat({
        model: selectedModel.value,
        messages: chatMessages,
        temperature: temperature.value
      })

      if (result.success) {
        const assistantMessage = {
          role: 'assistant',
          content: result.data.choices[0].message.content,
          timestamp: new Date()
        }
        messages.value.push(assistantMessage)
      } else {
        throw new Error(result.message)
      }
    }

    scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const clearChat = () => {
  messages.value = []
  ElMessage.success('对话已清空')
}

const useExample = (question) => {
  inputMessage.value = question
}

const formatTime = (time) => {
  return time.toLocaleTimeString()
}

const formatMessage = (content) => {
  // 简单的 markdown 格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 生命周期
onMounted(() => {
  // 添加欢迎消息
  messages.value.push({
    role: 'assistant',
    content: '您好！我是智谱 AI，很高兴为您服务。请先配置 API Key，然后就可以开始对话了。',
    timestamp: new Date()
  })
})
</script>

<style lang="scss" scoped>
.zhipu-api-demo {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.chat-demo-card, .features-card, .examples-card {
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    color: #2c3e50;
  }
  
  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
}

.chat-messages {
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 1rem;
}

.message-item {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  
  &.user {
    flex-direction: row-reverse;
    
    .message-content {
      background: #3498db;
      color: white;
      border-radius: 18px 18px 4px 18px;
    }
  }
  
  &.assistant {
    .message-content {
      background: #f8f9fa;
      border-radius: 18px 18px 18px 4px;
    }
  }
  
  &.loading {
    .message-content {
      background: #e9ecef;
    }
  }
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  
  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    opacity: 0.7;
  }
  
  .message-text {
    line-height: 1.5;
    
    code {
      background: rgba(0, 0, 0, 0.1);
      padding: 0.2rem 0.4rem;
      border-radius: 4px;
      font-family: monospace;
    }
  }
}

.chat-input {
  .input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    
    .input-options {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .temperature-label {
        font-size: 0.9rem;
        color: #6c757d;
      }
    }
  }
}

.features-grid, .examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.feature-item, .example-item {
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  
  .feature-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6c757d;
    margin: 0;
  }
}

.example-item {
  background: white;
  border: 1px solid #e9ecef;
  cursor: pointer;
  display: flex;
  text-align: left;
  gap: 1rem;
  
  &:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
  }
  
  .example-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }
  
  .example-content {
    h5 {
      color: #2c3e50;
      margin: 0 0 0.25rem 0;
    }
    
    p {
      color: #6c757d;
      margin: 0;
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .input-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .input-options {
    flex-wrap: wrap;
  }
  
  .features-grid, .examples-grid {
    grid-template-columns: 1fr;
  }
}
</style>
