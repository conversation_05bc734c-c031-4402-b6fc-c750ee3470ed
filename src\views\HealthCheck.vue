<template>
  <div class="health-check">
    <div class="container">
      <h1>🏥 系统健康检查</h1>
      
      <div class="status-overview">
        <div :class="['status-card', overallStatus.type]">
          <div class="status-icon">{{ overallStatus.icon }}</div>
          <div class="status-text">
            <h2>{{ overallStatus.title }}</h2>
            <p>{{ overallStatus.message }}</p>
          </div>
        </div>
      </div>
      
      <div class="checks-grid">
        <div v-for="check in healthChecks" :key="check.name" :class="['check-card', check.status]">
          <div class="check-header">
            <span class="check-icon">{{ check.icon }}</span>
            <h3>{{ check.name }}</h3>
            <span :class="['check-status', check.status]">{{ check.statusText }}</span>
          </div>
          <p class="check-description">{{ check.description }}</p>
          <div v-if="check.details" class="check-details">
            <div v-for="(value, key) in check.details" :key="key" class="detail-item">
              <span class="detail-key">{{ key }}:</span>
              <span class="detail-value">{{ value }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="actions">
        <button @click="runHealthCheck" :disabled="isChecking" class="check-btn">
          {{ isChecking ? '检查中...' : '重新检查' }}
        </button>
        <button @click="exportReport" class="export-btn">导出报告</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 状态
const isChecking = ref(false)
const healthChecks = ref([])

// 使用响应式引用来安全地初始化 stores
const appStore = ref(null)
const userStore = ref(null)
const chatStore = ref(null)
const drawingStore = ref(null)

// 总体状态
const overallStatus = computed(() => {
  const failedChecks = healthChecks.value.filter(check => check.status === 'error')
  const warningChecks = healthChecks.value.filter(check => check.status === 'warning')
  
  if (failedChecks.length > 0) {
    return {
      type: 'error',
      icon: '❌',
      title: '系统异常',
      message: `发现 ${failedChecks.length} 个严重问题`
    }
  } else if (warningChecks.length > 0) {
    return {
      type: 'warning',
      icon: '⚠️',
      title: '系统警告',
      message: `发现 ${warningChecks.length} 个警告`
    }
  } else {
    return {
      type: 'success',
      icon: '✅',
      title: '系统正常',
      message: '所有检查项目都通过了'
    }
  }
})

// 运行健康检查
const runHealthCheck = async () => {
  isChecking.value = true
  healthChecks.value = []
  
  try {
    // 检查浏览器兼容性
    const browserCheck = checkBrowserCompatibility()
    healthChecks.value.push({
      name: '浏览器兼容性',
      icon: '🌐',
      status: browserCheck.compatible ? 'success' : 'error',
      statusText: browserCheck.compatible ? '正常' : '异常',
      description: browserCheck.compatible ? '浏览器支持所有必要功能' : '浏览器缺少必要功能',
      details: browserCheck.checks
    })
    
    // 检查本地存储
    const storageCheck = checkLocalStorage()
    healthChecks.value.push({
      name: '本地存储',
      icon: '💾',
      status: storageCheck.available ? 'success' : 'error',
      statusText: storageCheck.available ? '正常' : '异常',
      description: storageCheck.available ? '本地存储功能正常' : '本地存储不可用',
      details: {
        'localStorage': storageCheck.localStorage ? '可用' : '不可用',
        'sessionStorage': storageCheck.sessionStorage ? '可用' : '不可用'
      }
    })
    
    // 检查网络连接
    const networkCheck = checkNetworkConnection()
    healthChecks.value.push({
      name: '网络连接',
      icon: '🌐',
      status: networkCheck.online ? 'success' : 'warning',
      statusText: networkCheck.online ? '在线' : '离线',
      description: networkCheck.online ? '网络连接正常' : '当前处于离线状态',
      details: {
        '连接状态': networkCheck.online ? '在线' : '离线',
        '连接类型': networkCheck.effectiveType || '未知'
      }
    })
    
    // 检查Stores状态
    const storesCheck = checkStoresStatus()
    healthChecks.value.push({
      name: 'Stores状态',
      icon: '🗃️',
      status: storesCheck.allInitialized ? 'success' : 'warning',
      statusText: storesCheck.allInitialized ? '正常' : '部分异常',
      description: storesCheck.allInitialized ? '所有Store都已正确初始化' : '部分Store初始化异常',
      details: storesCheck.details
    })
    
    // 检查路由状态
    const routerCheck = checkRouterStatus()
    healthChecks.value.push({
      name: '路由系统',
      icon: '🛣️',
      status: routerCheck.working ? 'success' : 'error',
      statusText: routerCheck.working ? '正常' : '异常',
      description: routerCheck.working ? '路由系统工作正常' : '路由系统异常',
      details: {
        '当前路由': routerCheck.currentRoute,
        '路由数量': routerCheck.routeCount
      }
    })
    
    ElMessage.success('健康检查完成')
  } catch (error) {
    console.error('健康检查失败:', error)
    ElMessage.error('健康检查失败')
  } finally {
    isChecking.value = false
  }
}

// 检查浏览器兼容性
const checkBrowserCompatibility = () => {
  const checks = {
    localStorage: typeof Storage !== 'undefined',
    fetch: typeof fetch !== 'undefined',
    Promise: typeof Promise !== 'undefined',
    ES6: (() => {
      try {
        new Function('(a = 0) => a')
        return true
      } catch {
        return false
      }
    })()
  }
  
  const compatible = Object.values(checks).every(Boolean)
  
  return { compatible, checks }
}

// 检查本地存储
const checkLocalStorage = () => {
  let localStorage = false
  let sessionStorage = false
  
  try {
    window.localStorage.setItem('test', 'test')
    window.localStorage.removeItem('test')
    localStorage = true
  } catch {}
  
  try {
    window.sessionStorage.setItem('test', 'test')
    window.sessionStorage.removeItem('test')
    sessionStorage = true
  } catch {}
  
  return {
    available: localStorage && sessionStorage,
    localStorage,
    sessionStorage
  }
}

// 检查网络连接
const checkNetworkConnection = () => {
  return {
    online: navigator.onLine,
    effectiveType: navigator.connection?.effectiveType
  }
}

// 检查Stores状态
const checkStoresStatus = () => {
  const details = {
    'App Store': appStore ? '已初始化' : '未初始化',
    'User Store': userStore ? '已初始化' : '未初始化',
    'Chat Store': chatStore ? '已初始化' : '未初始化',
    'Drawing Store': drawingStore ? '已初始化' : '未初始化'
  }
  
  const allInitialized = Object.values(details).every(status => status === '已初始化')
  
  return { allInitialized, details }
}

// 检查路由状态
const checkRouterStatus = () => {
  try {
    const router = useRouter()
    return {
      working: true,
      currentRoute: router.currentRoute.value.path,
      routeCount: router.getRoutes().length
    }
  } catch {
    return {
      working: false,
      currentRoute: '未知',
      routeCount: 0
    }
  }
}

// 导出报告
const exportReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    overallStatus: overallStatus.value,
    checks: healthChecks.value,
    systemInfo: {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    }
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `health-check-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('报告已导出')
}

onMounted(() => {
  runHealthCheck()
})
</script>

<style lang="scss" scoped>
.health-check {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 2rem;

  .container {
    max-width: 1200px;
    margin: 0 auto;

    h1 {
      text-align: center;
      color: #1f2937;
      margin-bottom: 2rem;
      font-size: 2.5rem;
      font-weight: 700;
    }

    .status-overview {
      margin-bottom: 2rem;

      .status-card {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &.success { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
        &.warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; }
        &.error { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }

        .status-icon {
          font-size: 3rem;
        }

        .status-text h2 {
          margin: 0 0 0.5rem 0;
          font-size: 1.5rem;
        }

        .status-text p {
          margin: 0;
          opacity: 0.9;
        }
      }
    }

    .checks-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .check-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border-left: 4px solid;

        &.success { border-left-color: #10b981; }
        &.warning { border-left-color: #f59e0b; }
        &.error { border-left-color: #ef4444; }

        .check-header {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1rem;

          .check-icon {
            font-size: 1.5rem;
          }

          h3 {
            flex: 1;
            margin: 0;
            color: #1f2937;
          }

          .check-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 500;

            &.success { background: #d1fae5; color: #065f46; }
            &.warning { background: #fef3c7; color: #92400e; }
            &.error { background: #fee2e2; color: #991b1b; }
          }
        }

        .check-description {
          color: #6b7280;
          margin-bottom: 1rem;
        }

        .check-details {
          .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f3f4f6;

            &:last-child {
              border-bottom: none;
            }

            .detail-key {
              font-weight: 500;
              color: #374151;
            }

            .detail-value {
              color: #6b7280;
              font-family: monospace;
            }
          }
        }
      }
    }

    .actions {
      display: flex;
      gap: 1rem;
      justify-content: center;

      button {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &.check-btn {
          background: #3b82f6;
          color: white;

          &:hover:not(:disabled) {
            background: #2563eb;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        &.export-btn {
          background: #10b981;
          color: white;

          &:hover {
            background: #059669;
          }
        }
      }
    }
  }
}
</style>
