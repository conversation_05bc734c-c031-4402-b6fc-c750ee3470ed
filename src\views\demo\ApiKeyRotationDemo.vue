<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">API密钥轮询演示</h1>
        <p class="text-gray-600 mt-2">实时演示API密钥的自动轮询、故障转移和健康检查功能</p>
      </div>

      <!-- 实时状态监控 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 当前状态 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">实时状态</h2>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600">当前密钥</span>
              <span class="font-mono text-sm bg-blue-100 px-2 py-1 rounded">
                #{{ currentKeyIndex + 1 }}
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">总请求数</span>
              <span class="font-semibold">{{ totalRequests }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">成功请求</span>
              <span class="text-green-600 font-semibold">{{ successfulRequests }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">失败请求</span>
              <span class="text-red-600 font-semibold">{{ failedRequests }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">成功率</span>
              <span class="font-semibold">{{ successRate }}%</span>
            </div>
          </div>
        </div>

        <!-- 密钥状态 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">密钥状态</h2>
          <div class="space-y-3">
            <div v-for="(keyStats, index) in keyStatsList" :key="index" class="flex items-center justify-between p-3 rounded-lg" :class="[
              keyStats.isCurrent ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50',
              !keyStats.isActive ? 'opacity-50' : ''
            ]">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 rounded-full" :class="[
                  keyStats.isActive ? 'bg-green-500' : 'bg-red-500'
                ]"></div>
                <span class="font-medium">密钥 #{{ index + 1 }}</span>
                <span v-if="keyStats.isCurrent" class="text-xs bg-blue-500 text-white px-2 py-1 rounded">当前</span>
              </div>
              <div class="text-sm text-gray-600">
                {{ keyStats.successfulRequests }}/{{ keyStats.totalRequests }}
                <span v-if="keyStats.failureCount > 0" class="text-red-500 ml-1">
                  ({{ keyStats.failureCount }} 失败)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 演示控制 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">演示控制</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            @click="sendTestRequest"
            :disabled="isRunning"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            发送测试请求
          </button>
          
          <button
            @click="simulateKeyFailure"
            :disabled="isRunning"
            class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:opacity-50"
          >
            模拟密钥故障
          </button>
          
          <button
            @click="startAutoTest"
            :disabled="isRunning"
            class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
          >
            {{ isRunning ? '运行中...' : '开始自动测试' }}
          </button>
          
          <button
            @click="stopAutoTest"
            :disabled="!isRunning"
            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
          >
            停止测试
          </button>
        </div>
        
        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">自动测试间隔 (秒)</label>
          <input
            v-model.number="autoTestInterval"
            type="number"
            min="1"
            max="60"
            class="w-32 p-2 border border-gray-300 rounded-lg"
          />
        </div>
      </div>

      <!-- 请求日志 -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-xl font-semibold">请求日志</h2>
          <button
            @click="clearLogs"
            class="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清空日志
          </button>
        </div>
        
        <div class="max-h-96 overflow-y-auto">
          <div v-if="requestLogs.length === 0" class="p-6 text-center text-gray-500">
            暂无请求日志
          </div>
          <div v-else class="divide-y divide-gray-200">
            <div v-for="log in requestLogs.slice().reverse()" :key="log.id" class="p-4 hover:bg-gray-50">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 rounded-full" :class="[
                    log.success ? 'bg-green-500' : 'bg-red-500'
                  ]"></div>
                  <span class="font-medium">密钥 #{{ log.keyIndex + 1 }}</span>
                  <span class="text-sm text-gray-500">{{ formatTime(log.timestamp) }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span v-if="log.responseTime" class="text-xs text-gray-500">
                    {{ log.responseTime }}ms
                  </span>
                  <span :class="[
                    'text-xs px-2 py-1 rounded',
                    log.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    {{ log.success ? '成功' : '失败' }}
                  </span>
                </div>
              </div>
              <div v-if="log.message" class="mt-2 text-sm text-gray-600">
                {{ log.message }}
              </div>
              <div v-if="log.error" class="mt-2 text-sm text-red-600">
                错误: {{ log.error }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getApiKeyStats, 
  generateChatCompletion,
  rotateApiKey 
} from '@/services/openrouterApi.js'

// 响应式数据
const keyStatsList = ref([])
const requestLogs = ref([])
const isRunning = ref(false)
const autoTestInterval = ref(5)
const autoTestTimer = ref(null)
const currentKeyIndex = ref(0)

// 计算属性
const totalRequests = computed(() => {
  return keyStatsList.value.reduce((sum, key) => sum + key.totalRequests, 0)
})

const successfulRequests = computed(() => {
  return keyStatsList.value.reduce((sum, key) => sum + key.successfulRequests, 0)
})

const failedRequests = computed(() => {
  return totalRequests.value - successfulRequests.value
})

const successRate = computed(() => {
  if (totalRequests.value === 0) return 0
  return ((successfulRequests.value / totalRequests.value) * 100).toFixed(1)
})

// 刷新密钥状态
const refreshKeyStats = () => {
  try {
    const stats = getApiKeyStats()
    keyStatsList.value = stats
    
    // 找到当前使用的密钥
    const currentKey = stats.find(key => key.isCurrent)
    if (currentKey) {
      currentKeyIndex.value = currentKey.index - 1
    }
  } catch (error) {
    console.error('获取密钥状态失败:', error)
  }
}

// 发送测试请求
const sendTestRequest = async () => {
  const startTime = Date.now()
  const logId = Date.now()
  
  try {
    const messages = [{ role: 'user', content: 'Hello' }]
    
    await generateChatCompletion(messages, {
      model: 'deepseek/deepseek-chat-v3-0324:free',
      max_tokens: 10
    })
    
    const responseTime = Date.now() - startTime
    
    // 记录成功日志
    requestLogs.value.push({
      id: logId,
      keyIndex: currentKeyIndex.value,
      success: true,
      responseTime: responseTime,
      timestamp: new Date(),
      message: '测试请求成功'
    })
    
    ElMessage.success('测试请求成功')
  } catch (error) {
    // 记录失败日志
    requestLogs.value.push({
      id: logId,
      keyIndex: currentKeyIndex.value,
      success: false,
      timestamp: new Date(),
      error: error.message,
      message: '测试请求失败'
    })
    
    ElMessage.error('测试请求失败: ' + error.message)
  }
  
  // 刷新状态
  refreshKeyStats()
}

// 模拟密钥故障
const simulateKeyFailure = () => {
  try {
    // 轮换到下一个密钥
    rotateApiKey()
    
    // 记录轮换日志
    requestLogs.value.push({
      id: Date.now(),
      keyIndex: currentKeyIndex.value,
      success: true,
      timestamp: new Date(),
      message: '手动轮换密钥'
    })
    
    refreshKeyStats()
    ElMessage.success('已轮换到下一个密钥')
  } catch (error) {
    ElMessage.error('密钥轮换失败: ' + error.message)
  }
}

// 开始自动测试
const startAutoTest = () => {
  if (isRunning.value) return
  
  isRunning.value = true
  autoTestTimer.value = setInterval(() => {
    sendTestRequest()
  }, autoTestInterval.value * 1000)
  
  ElMessage.success(`开始自动测试，间隔 ${autoTestInterval.value} 秒`)
}

// 停止自动测试
const stopAutoTest = () => {
  if (autoTestTimer.value) {
    clearInterval(autoTestTimer.value)
    autoTestTimer.value = null
  }
  isRunning.value = false
  ElMessage.success('自动测试已停止')
}

// 清空日志
const clearLogs = () => {
  requestLogs.value = []
  ElMessage.success('日志已清空')
}

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN')
}

// 组件挂载
onMounted(() => {
  refreshKeyStats()
  
  // 定期刷新状态
  const refreshTimer = setInterval(refreshKeyStats, 5000)
  
  // 组件卸载时清理
  onUnmounted(() => {
    clearInterval(refreshTimer)
    stopAutoTest()
  })
})
</script>

<style scoped>
/* 组件样式 */
</style>
