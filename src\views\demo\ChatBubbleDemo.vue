<template>
  <div class="chat-bubble-demo">
    <div class="demo-header">
      <h1>智能聊天气泡演示</h1>
      <p>体验根据内容动态变化的聊天气泡样式</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <h2>🎛️ 控制面板</h2>
      
      <div class="controls">
        <button @click="addSampleMessage('question')" class="btn btn-primary">
          添加问题消息
        </button>
        
        <button @click="addSampleMessage('exclamation')" class="btn btn-warning">
          添加感叹消息
        </button>
        
        <button @click="addSampleMessage('code')" class="btn btn-info">
          添加代码消息
        </button>
        
        <button @click="addSampleMessage('emoji')" class="btn btn-success">
          添加表情消息
        </button>
        
        <button @click="clearMessages" class="btn btn-danger">
          清空消息
        </button>
      </div>
      
      <div class="custom-input">
        <textarea
          v-model="customMessage"
          rows="3"
          placeholder="输入自定义消息内容..."
        ></textarea>
        <div class="input-actions">
          <button @click="addCustomMessage('user')" class="btn btn-primary">
            作为用户发送
          </button>
          <button @click="addCustomMessage('assistant')" class="btn btn-success">
            作为AI回复
          </button>
        </div>
      </div>
    </div>

    <!-- 消息展示区域 -->
    <div class="messages-display">
      <h2>💬 消息展示区域</h2>
      
      <div class="messages-container" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-state">
          <div class="empty-icon">💭</div>
          <h3>还没有消息</h3>
          <p>点击上方按钮添加不同类型的消息，体验动态气泡效果</p>
        </div>
        
        <div
          v-for="message in messages"
          :key="message.id"
          class="message-bubble"
          :class="[
            message.role === 'user' ? 'user-message' : 'ai-message',
            getMessageClasses(message.content)
          ]"
        >
          <div class="message-content">{{ message.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const messages = ref([])
const customMessage = ref('')
const messagesContainer = ref(null)

// 示例消息模板
const sampleMessages = {
  question: [
    "你能帮我解释一下什么是人工智能吗？",
    "如何提高工作效率？有什么好的建议吗？",
    "Python和JavaScript哪个更适合初学者？"
  ],
  exclamation: [
    "太棒了！这个功能真的很实用！",
    "哇！没想到还能这样操作！",
    "真是令人惊喜的结果！"
  ],
  code: [
    "```javascript\nfunction hello() {\n  console.log('Hello World!');\n}\n```",
    "这里有一个简单的 `console.log()` 示例",
    "```python\ndef hello():\n    print('Hello Python!')\n```"
  ],
  emoji: [
    "今天天气真好！☀️ 适合出去走走 🚶‍♂️",
    "恭喜你！🎉 任务完成得很棒！👏",
    "咖啡时间到了 ☕ 来一杯提神醒脑 😊"
  ]
}

// 获取消息的动态类名
const getMessageClasses = (content) => {
  const classes = []
  
  // 检测内容类型
  if (/[？?][\s]*$/.test(content.trim())) {
    classes.push('is-question')
  }
  if (/[！!][\s]*$/.test(content.trim())) {
    classes.push('is-exclamation')
  }
  if (/```[\s\S]*?```|`[^`]+`/.test(content)) {
    classes.push('has-code')
  }
  if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]/u.test(content)) {
    classes.push('has-emoji')
  }
  
  // 检测长度
  if (content.length > 200) {
    classes.push('long-message')
  } else if (content.length < 50) {
    classes.push('short-message')
  }
  
  return classes
}

// 添加示例消息
const addSampleMessage = (type) => {
  const samples = sampleMessages[type]
  const randomSample = samples[Math.floor(Math.random() * samples.length)]
  
  const role = Math.random() > 0.5 ? 'user' : 'assistant'
  
  const message = {
    id: Date.now() + Math.random(),
    role: role,
    content: randomSample,
    timestamp: new Date()
  }
  
  messages.value.push(message)
  scrollToBottom()
}

// 添加自定义消息
const addCustomMessage = (role) => {
  if (!customMessage.value.trim()) {
    return
  }
  
  const message = {
    id: Date.now() + Math.random(),
    role: role,
    content: customMessage.value.trim(),
    timestamp: new Date()
  }
  
  messages.value.push(message)
  customMessage.value = ''
  scrollToBottom()
}

// 清空消息
const clearMessages = () => {
  messages.value = []
}

// 滚动到底部
const scrollToBottom = () => {
  setTimeout(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  }, 100)
}

// 初始化一些示例消息
setTimeout(() => {
  addSampleMessage('question')
  addSampleMessage('emoji')
  addSampleMessage('code')
}, 1000)
</script>

<style scoped>
.chat-bubble-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  font-size: 32px;
  color: #303133;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.control-panel,
.messages-display {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-panel h2,
.messages-display h2 {
  margin: 0 0 20px 0;
  color: #303133;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary { background: #409eff; color: white; }
.btn-warning { background: #e6a23c; color: white; }
.btn-info { background: #909399; color: white; }
.btn-success { background: #67c23a; color: white; }
.btn-danger { background: #f56c6c; color: white; }

.btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.custom-input {
  margin-top: 20px;
}

.custom-input textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.input-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.messages-container {
  max-height: 600px;
  overflow-y: auto;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #606266;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 消息气泡样式 */
.message-bubble {
  margin: 15px 0;
  display: flex;
  animation: slideIn 0.4s ease-out;
}

.message-bubble.user-message {
  justify-content: flex-end;
}

.message-bubble.ai-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  transition: all 0.3s ease;
  position: relative;
}

/* 用户消息样式 */
.user-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 18px 18px 4px 18px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* AI消息样式 */
.ai-message .message-content {
  background: white;
  color: #303133;
  border: 1px solid #e4e7ed;
  border-radius: 4px 18px 18px 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 动态样式 - 问题消息 */
.is-question .message-content {
  border-left: 4px solid #3b82f6;
}

.user-message.is-question .message-content {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.ai-message.is-question .message-content {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-left-color: #3b82f6;
}

/* 动态样式 - 感叹消息 */
.is-exclamation .message-content {
  border-left: 4px solid #f59e0b;
  animation: excitedPulse 1.5s ease-in-out;
}

.user-message.is-exclamation .message-content {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.ai-message.is-exclamation .message-content {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-left-color: #f59e0b;
}

/* 动态样式 - 代码消息 */
.has-code .message-content {
  font-family: 'Fira Code', 'JetBrains Mono', monospace;
  font-size: 13px;
}

.user-message.has-code .message-content {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.ai-message.has-code .message-content {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #475569;
}

/* 动态样式 - 表情消息 */
.has-emoji .message-content {
  animation: emojiGlow 3s ease-in-out infinite;
}

.user-message.has-emoji .message-content {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
}

.ai-message.has-emoji .message-content {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* 长度样式 */
.short-message .message-content {
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 14px;
}

.long-message .message-content {
  max-width: 85%;
  padding: 16px 20px;
  border-radius: 20px;
  line-height: 1.7;
}

/* 悬停效果 */
.message-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes excitedPulse {
  0% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1.02); }
  75% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes emojiGlow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px rgba(236, 72, 153, 0.3);
  }
}

@media (max-width: 768px) {
  .controls {
    justify-content: center;
  }

  .input-actions {
    justify-content: center;
  }

  .message-content {
    max-width: 85%;
  }
}
</style>
