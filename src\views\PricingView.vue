<template>
  <div class="pricing-page">
    <!-- 定价英雄区域 -->
    <section class="pricing-hero">
      <div class="container">
        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-text">💎 选择您的创作计划</span>
          </div>
          
          <h1 class="hero-title">
            <span class="title-line">释放AI创作的</span>
            <span class="title-line gradient-text">无限潜能</span>
          </h1>
          
          <p class="hero-subtitle">
            从免费体验到专业创作，选择最适合您的计划<br>
            让AI成为您最强大的创作伙伴
          </p>
          
          <!-- 计费周期切换 -->
          <div class="billing-toggle">
            <span :class="{ active: !isYearly }">月付</span>
            <div class="toggle-switch" @click="toggleBilling">
              <div class="toggle-slider" :class="{ yearly: isYearly }"></div>
            </div>
            <span :class="{ active: isYearly }">年付</span>
            <div class="save-badge" v-if="isYearly">省20%</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 定价卡片 -->
    <section class="pricing-plans">
      <div class="container">
        <div class="plans-grid">
          <!-- 免费计划 -->
          <div class="plan-card free">
            <div class="plan-header">
              <h3 class="plan-name">免费体验</h3>
              <div class="plan-price">
                <span class="currency">¥</span>
                <span class="amount">0</span>
                <span class="period">/月</span>
              </div>
              <p class="plan-description">体验AI创作的魅力</p>
            </div>
            
            <div class="plan-features">
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>每日10次AI对话</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>每日3张AI绘画</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>基础模型访问</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>作品展示</span>
              </div>
            </div>
            
            <button class="plan-button" @click="selectPlan('free')">
              开始免费体验
            </button>
          </div>

          <!-- 专业计划 -->
          <div class="plan-card pro popular">
            <div class="popular-badge">最受欢迎</div>
            <div class="plan-header">
              <h3 class="plan-name">专业版</h3>
              <div class="plan-price">
                <span class="currency">¥</span>
                <span class="amount">{{ isYearly ? '39' : '49' }}</span>
                <span class="period">{{ isYearly ? '/月' : '/月' }}</span>
              </div>
              <p class="plan-description">专业创作者的首选</p>
            </div>
            
            <div class="plan-features">
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>无限AI对话</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>每日100张AI绘画</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>高级模型访问</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>优先处理速度</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>高清图像输出</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>商业使用授权</span>
              </div>
            </div>
            
            <button class="plan-button" @click="selectPlan('pro')">
              立即升级专业版
            </button>
          </div>

          <!-- 企业计划 -->
          <div class="plan-card enterprise">
            <div class="plan-header">
              <h3 class="plan-name">企业版</h3>
              <div class="plan-price">
                <span class="currency">¥</span>
                <span class="amount">{{ isYearly ? '199' : '249' }}</span>
                <span class="period">{{ isYearly ? '/月' : '/月' }}</span>
              </div>
              <p class="plan-description">团队协作与定制服务</p>
            </div>
            
            <div class="plan-features">
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>无限制使用</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>团队协作功能</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>API接口访问</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>专属客服支持</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>定制化服务</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✓</div>
                <span>数据安全保障</span>
              </div>
            </div>
            
            <button class="plan-button" @click="selectPlan('enterprise')">
              联系销售团队
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能对比表 -->
    <section class="feature-comparison">
      <div class="container">
        <h2 class="section-title">功能详细对比</h2>
        <div class="comparison-table">
          <div class="table-header">
            <div class="feature-column">功能</div>
            <div class="plan-column">免费版</div>
            <div class="plan-column">专业版</div>
            <div class="plan-column">企业版</div>
          </div>
          
          <div class="table-row" v-for="feature in comparisonFeatures" :key="feature.name">
            <div class="feature-name">{{ feature.name }}</div>
            <div class="feature-value">{{ feature.free }}</div>
            <div class="feature-value">{{ feature.pro }}</div>
            <div class="feature-value">{{ feature.enterprise }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="container">
        <h2 class="section-title">常见问题</h2>
        <div class="faq-grid">
          <div class="faq-item" v-for="faq in faqs" :key="faq.question">
            <h3 class="faq-question">{{ faq.question }}</h3>
            <p class="faq-answer">{{ faq.answer }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="pricing-cta">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">准备开始您的AI创作之旅？</h2>
          <p class="cta-description">
            立即注册，免费体验AI创作的无限可能
          </p>
          <div class="cta-actions">
            <button class="cta-button primary" @click="startTrial">
              免费开始体验
            </button>
            <button class="cta-button secondary" @click="contactSales">
              联系销售咨询
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isYearly = ref(false)

// 切换计费周期
const toggleBilling = () => {
  isYearly.value = !isYearly.value
}

// 选择计划
const selectPlan = (plan) => {
  if (plan === 'free') {
    if (!userStore.isLoggedIn) {
      router.push('/register')
    } else {
      router.push('/chat')
    }
  } else {
    // 跳转到支付页面或显示支付弹窗
    console.log(`选择了 ${plan} 计划`)
  }
}

// 开始试用
const startTrial = () => {
  if (!userStore.isLoggedIn) {
    router.push('/register')
  } else {
    router.push('/chat')
  }
}

// 联系销售
const contactSales = () => {
  // 打开联系销售弹窗或跳转到联系页面
  console.log('联系销售')
}

// 功能对比数据
const comparisonFeatures = ref([
  { name: 'AI对话次数', free: '10次/天', pro: '无限制', enterprise: '无限制' },
  { name: 'AI绘画次数', free: '3张/天', pro: '100张/天', enterprise: '无限制' },
  { name: '模型访问', free: '基础模型', pro: '高级模型', enterprise: '全部模型' },
  { name: '图像分辨率', free: '512x512', pro: '1024x1024', enterprise: '4K高清' },
  { name: '处理速度', free: '标准', pro: '优先处理', enterprise: '极速处理' },
  { name: '商业使用', free: '❌', pro: '✅', enterprise: '✅' },
  { name: '团队协作', free: '❌', pro: '❌', enterprise: '✅' },
  { name: 'API访问', free: '❌', pro: '❌', enterprise: '✅' },
  { name: '客服支持', free: '社区支持', pro: '邮件支持', enterprise: '专属客服' }
])

// FAQ数据
const faqs = ref([
  {
    question: '免费版有什么限制？',
    answer: '免费版每天可以进行10次AI对话和生成3张AI图像，使用基础模型，适合体验和轻度使用。'
  },
  {
    question: '可以随时升级或降级吗？',
    answer: '是的，您可以随时升级到更高级的计划。降级将在当前计费周期结束后生效。'
  },
  {
    question: '支持哪些支付方式？',
    answer: '我们支持支付宝、微信支付、银行卡等多种支付方式，确保您的支付安全便捷。'
  },
  {
    question: '企业版有什么特殊服务？',
    answer: '企业版提供专属客服、定制化服务、API接口、团队管理等功能，适合企业级用户。'
  }
])
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.pricing-page {
  min-height: 100vh;
  background: $bg-color;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: 768px) {
    padding: 0 $spacing-md;
  }
}

// 定价英雄区域
.pricing-hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, $bg-color-dark 0%, #1e293b 50%, #334155 100%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;

    .hero-badge {
      display: inline-block;
      background: rgba(99, 102, 241, 0.1);
      border: 1px solid rgba(99, 102, 241, 0.3);
      color: #a78bfa;
      padding: 12px 24px;
      border-radius: 50px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 32px;
      backdrop-filter: blur(10px);
    }

    .hero-title {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 24px;

      .title-line {
        display: block;
        margin-bottom: 8px;
      }

      .gradient-text {
        background: $gradient-primary;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .hero-subtitle {
      font-size: 1.25rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 48px;
      line-height: 1.6;
    }

    .billing-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-md;
      margin-bottom: 32px;
      position: relative;

      span {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.6);
        transition: color 0.3s ease;

        &.active {
          color: white;
        }
      }

      .toggle-switch {
        width: 60px;
        height: 30px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        position: relative;
        cursor: pointer;
        transition: background 0.3s ease;

        .toggle-slider {
          width: 26px;
          height: 26px;
          background: $gradient-primary;
          border-radius: 50%;
          position: absolute;
          top: 2px;
          left: 2px;
          transition: transform 0.3s ease;

          &.yearly {
            transform: translateX(30px);
          }
        }
      }

      .save-badge {
        position: absolute;
        top: -40px;
        right: -20px;
        background: $gradient-success;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}

// 定价计划
.pricing-plans {
  padding: 80px 0;

  .plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacing-xl;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  .plan-card {
    background: $bg-color-card;
    border-radius: 24px;
    padding: $spacing-xl;
    box-shadow: $box-shadow-base;
    border: 2px solid transparent;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-8px);
      box-shadow: $box-shadow-xl;
    }

    &.popular {
      border-color: $primary-color;
      box-shadow: $box-shadow-lg;

      .popular-badge {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        background: $gradient-primary;
        color: white;
        padding: 8px 24px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .plan-header {
      text-align: center;
      margin-bottom: $spacing-xl;

      .plan-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: $text-color-primary;
        margin-bottom: $spacing-md;
      }

      .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: $spacing-sm;

        .currency {
          font-size: 1.25rem;
          font-weight: 600;
          color: $text-color-secondary;
        }

        .amount {
          font-size: 3rem;
          font-weight: 800;
          color: $primary-color;
          margin: 0 4px;
        }

        .period {
          font-size: 1rem;
          color: $text-color-secondary;
        }
      }

      .plan-description {
        color: $text-color-secondary;
        font-size: 1rem;
      }
    }

    .plan-features {
      margin-bottom: $spacing-xl;

      .feature-item {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        padding: $spacing-sm 0;

        .feature-icon {
          width: 20px;
          height: 20px;
          background: $gradient-success;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          flex-shrink: 0;
        }

        span {
          color: $text-color-regular;
          font-size: 14px;
        }
      }
    }

    .plan-button {
      width: 100%;
      padding: 16px 24px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;

      background: $gradient-primary;
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $box-shadow-lg;
      }
    }
  }
}

// 功能对比表
.feature-comparison {
  padding: 80px 0;
  background: $bg-color-secondary;

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: $text-color-primary;
    margin-bottom: $spacing-xl;
  }

  .comparison-table {
    max-width: 1000px;
    margin: 0 auto;
    background: $bg-color-card;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: $box-shadow-base;

    .table-header {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      background: $bg-color-tertiary;
      font-weight: 700;
      color: $text-color-primary;

      > div {
        padding: $spacing-md $spacing-lg;
        text-align: center;

        &.feature-column {
          text-align: left;
        }
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      border-bottom: 1px solid $border-color-light;

      &:last-child {
        border-bottom: none;
      }

      .feature-name {
        padding: $spacing-md $spacing-lg;
        font-weight: 600;
        color: $text-color-primary;
      }

      .feature-value {
        padding: $spacing-md $spacing-lg;
        text-align: center;
        color: $text-color-regular;
        font-size: 14px;
      }
    }
  }
}

// FAQ区域
.faq-section {
  padding: 80px 0;

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: $text-color-primary;
    margin-bottom: $spacing-xl;
  }

  .faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: $spacing-xl;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  .faq-item {
    background: $bg-color-card;
    padding: $spacing-xl;
    border-radius: 16px;
    box-shadow: $box-shadow-sm;

    .faq-question {
      font-size: 1.25rem;
      font-weight: 700;
      color: $text-color-primary;
      margin-bottom: $spacing-md;
    }

    .faq-answer {
      color: $text-color-regular;
      line-height: 1.6;
    }
  }
}

// CTA区域
.pricing-cta {
  padding: 100px 0;
  background: $gradient-primary;
  text-align: center;
  color: white;

  .cta-content {
    max-width: 600px;
    margin: 0 auto;

    .cta-title {
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: $spacing-md;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .cta-description {
      font-size: 1.25rem;
      margin-bottom: $spacing-xl;
      opacity: 0.9;
      line-height: 1.6;
    }

    .cta-actions {
      display: flex;
      gap: $spacing-md;
      justify-content: center;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
      }

      .cta-button {
        padding: 16px 32px;
        border-radius: 50px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &.primary {
          background: white;
          color: $primary-color;

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-lg;
          }
        }

        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }
}
</style>
