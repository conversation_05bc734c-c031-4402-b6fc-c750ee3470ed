/**
 * Targon 密钥池测试工具
 * 用于验证密钥池功能是否正常工作
 */

import targonApi from '@/services/targonApi'

/**
 * 测试密钥池基本功能
 */
export async function testKeyPoolBasics() {
  console.log('🧪 开始测试 Targon 密钥池基本功能...')
  
  const results = {
    keyPoolStatus: null,
    connectionTest: null,
    keyRotation: null,
    healthManagement: null
  }

  try {
    // 1. 测试密钥池状态获取
    console.log('📊 测试密钥池状态获取...')
    results.keyPoolStatus = targonApi.getKeyPoolStatus()
    console.log('密钥池状态:', results.keyPoolStatus)

    // 2. 测试连接（使用密钥池中的密钥）
    console.log('🔗 测试连接（使用密钥池）...')
    results.connectionTest = await targonApi.testConnection()
    console.log('连接测试结果:', results.connectionTest)

    // 3. 测试密钥轮换
    console.log('🔄 测试密钥轮换...')
    const oldStatus = targonApi.getKeyPoolStatus()
    const newKey = targonApi.forceKeyRotation()
    const newStatus = targonApi.getKeyPoolStatus()
    
    results.keyRotation = {
      success: true,
      oldIndex: oldStatus.currentKeyIndex,
      newIndex: newStatus.currentKeyIndex,
      newKey: newKey.substring(0, 10) + '...'
    }
    console.log('密钥轮换结果:', results.keyRotation)

    // 4. 测试健康状态管理
    console.log('🏥 测试健康状态管理...')
    targonApi.resetAllKeyHealth()
    const healthStatus = targonApi.getKeyPoolStatus()
    
    results.healthManagement = {
      success: true,
      healthyKeysAfterReset: healthStatus.healthyKeys,
      totalKeys: healthStatus.totalKeys
    }
    console.log('健康状态管理结果:', results.healthManagement)

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
    return {
      success: false,
      error: error.message,
      results
    }
  }

  console.log('✅ 密钥池基本功能测试完成')
  return {
    success: true,
    results
  }
}

/**
 * 测试密钥池在实际API调用中的表现
 */
export async function testKeyPoolInAction() {
  console.log('🚀 开始测试密钥池在实际API调用中的表现...')
  
  const results = {
    multipleRequests: [],
    errorHandling: null,
    loadBalancing: null
  }

  try {
    // 1. 测试多次请求（验证负载均衡）
    console.log('⚖️ 测试负载均衡（多次请求）...')
    const requestPromises = []
    
    for (let i = 0; i < 5; i++) {
      requestPromises.push(
        targonApi.testConnection().then(result => ({
          requestIndex: i,
          success: result.success,
          currentKey: result.data?.currentKey || 'unknown'
        }))
      )
    }
    
    results.multipleRequests = await Promise.all(requestPromises)
    console.log('多次请求结果:', results.multipleRequests)

    // 2. 测试错误处理和密钥标记
    console.log('🚨 测试错误处理...')
    
    // 保存当前状态
    const beforeErrorStatus = targonApi.getKeyPoolStatus()
    
    // 模拟一个错误（使用无效的API密钥）
    const originalApiKey = targonApi.apiKey
    targonApi.setApiKey('invalid_key_for_testing')
    
    const errorResult = await targonApi.testConnection()
    
    // 恢复原始设置
    targonApi.setApiKey(originalApiKey)
    
    const afterErrorStatus = targonApi.getKeyPoolStatus()
    
    results.errorHandling = {
      errorDetected: !errorResult.success,
      beforeHealthyKeys: beforeErrorStatus.healthyKeys,
      afterHealthyKeys: afterErrorStatus.healthyKeys,
      errorMessage: errorResult.message
    }
    console.log('错误处理结果:', results.errorHandling)

    // 3. 测试负载均衡效果
    const keyUsageMap = new Map()
    results.multipleRequests.forEach(req => {
      const key = req.currentKey
      keyUsageMap.set(key, (keyUsageMap.get(key) || 0) + 1)
    })
    
    results.loadBalancing = {
      uniqueKeysUsed: keyUsageMap.size,
      keyDistribution: Object.fromEntries(keyUsageMap),
      isBalanced: keyUsageMap.size > 1 // 如果使用了多个不同的密钥，说明负载均衡生效
    }
    console.log('负载均衡结果:', results.loadBalancing)

  } catch (error) {
    console.error('❌ 实际调用测试中发生错误:', error)
    return {
      success: false,
      error: error.message,
      results
    }
  }

  console.log('✅ 密钥池实际调用测试完成')
  return {
    success: true,
    results
  }
}

/**
 * 运行完整的密钥池测试套件
 */
export async function runFullKeyPoolTest() {
  console.log('🎯 开始运行完整的 Targon 密钥池测试套件...')
  
  const testResults = {
    basicTests: null,
    actionTests: null,
    summary: null,
    timestamp: new Date().toISOString()
  }

  // 运行基本功能测试
  testResults.basicTests = await testKeyPoolBasics()
  
  // 运行实际调用测试
  testResults.actionTests = await testKeyPoolInAction()
  
  // 生成测试摘要
  testResults.summary = {
    basicTestsPassed: testResults.basicTests.success,
    actionTestsPassed: testResults.actionTests.success,
    overallSuccess: testResults.basicTests.success && testResults.actionTests.success,
    keyPoolStatus: targonApi.getKeyPoolStatus()
  }

  console.log('📋 测试摘要:', testResults.summary)
  
  if (testResults.summary.overallSuccess) {
    console.log('🎉 所有测试通过！密钥池功能正常工作')
  } else {
    console.log('⚠️ 部分测试失败，请检查详细结果')
  }

  return testResults
}

/**
 * 快速验证密钥池是否可用
 */
export async function quickKeyPoolCheck() {
  console.log('⚡ 快速检查密钥池状态...')
  
  try {
    const status = targonApi.getKeyPoolStatus()
    const hasHealthyKeys = status.healthyKeys > 0
    
    console.log(`📊 密钥池状态: ${status.totalKeys} 个密钥，${status.healthyKeys} 个健康`)
    
    if (hasHealthyKeys) {
      console.log('✅ 密钥池状态正常')
      return { success: true, status }
    } else {
      console.log('⚠️ 没有健康的密钥可用')
      return { success: false, status, message: '没有健康的密钥可用' }
    }
  } catch (error) {
    console.error('❌ 检查密钥池状态失败:', error)
    return { success: false, error: error.message }
  }
}

// 导出便捷的测试函数
export default {
  testBasics: testKeyPoolBasics,
  testInAction: testKeyPoolInAction,
  runFullTest: runFullKeyPoolTest,
  quickCheck: quickKeyPoolCheck
}
