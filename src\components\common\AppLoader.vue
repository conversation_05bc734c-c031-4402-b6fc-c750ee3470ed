<template>
  <div class="app-loader">
    <div class="loader-content">
      <div class="logo-container">
        <div class="logo">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 17l10 5 10-5" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12l10 5 10-5" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div class="logo-pulse"></div>
      </div>
      
      <h2>AI创作助手平台</h2>
      <p>正在初始化应用...</p>
      
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="progress-text">{{ progressText }}</div>
      </div>
      
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 进度状态
const progress = ref(0)
const progressText = ref('正在启动...')

// 模拟加载进度
onMounted(() => {
  const steps = [
    { progress: 20, text: '加载核心模块...' },
    { progress: 40, text: '初始化用户状态...' },
    { progress: 60, text: '加载聊天历史...' },
    { progress: 80, text: '加载绘画历史...' },
    { progress: 100, text: '初始化完成！' }
  ]
  
  let currentStep = 0
  const interval = setInterval(() => {
    if (currentStep < steps.length) {
      progress.value = steps[currentStep].progress
      progressText.value = steps[currentStep].text
      currentStep++
    } else {
      clearInterval(interval)
    }
  }, 300)
})
</script>

<style lang="scss" scoped>
.app-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loader-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;

    .logo-container {
      position: relative;
      display: inline-block;
      margin-bottom: 2rem;

      .logo {
        position: relative;
        z-index: 2;
        animation: logoFloat 3s ease-in-out infinite;
      }

      .logo-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 2s ease-in-out infinite;
      }
    }

    h2 {
      font-size: 1.75rem;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 0.5rem;
    }

    p {
      color: #64748b;
      font-size: 1rem;
      margin-bottom: 2rem;
    }

    .progress-container {
      margin-bottom: 2rem;

      .progress-bar {
        width: 100%;
        height: 4px;
        background: #e2e8f0;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.75rem;

        .progress-fill {
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 2px;
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
      }
    }

    .loading-dots {
      display: flex;
      justify-content: center;
      gap: 0.5rem;

      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        animation: bounce 1.4s ease-in-out infinite both;

        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
      }
    }
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8) translateY(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2) translateY(-8px);
    opacity: 1;
  }
}
</style>
