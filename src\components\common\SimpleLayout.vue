<template>
  <div class="simple-layout">
    <!-- 简单的头部 -->
    <header class="simple-header">
      <div class="header-content">
        <h1>AI创作平台</h1>
        <nav class="simple-nav">
          <router-link to="/basic-test">基础测试</router-link>
          <router-link to="/home">首页</router-link>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="simple-main">
      <router-view />
    </main>

    <!-- 简单的底部 -->
    <footer class="simple-footer">
      <p>&copy; 2025 AI创作平台</p>
    </footer>
  </div>
</template>

<script setup>
// 最简单的布局，不依赖任何 store
console.log('SimpleLayout 组件已加载')
</script>

<style scoped>
.simple-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e <PERSON>I', <PERSON><PERSON>, sans-serif;
}

.simple-header {
  background: #2c3e50;
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.simple-nav {
  display: flex;
  gap: 1rem;
}

.simple-nav a {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.simple-nav a:hover,
.simple-nav a.router-link-active {
  background: rgba(255, 255, 255, 0.1);
}

.simple-main {
  flex: 1;
  background: #f8f9fa;
  padding: 2rem 0;
}

.simple-footer {
  background: #34495e;
  color: white;
  text-align: center;
  padding: 1rem 0;
}

.simple-footer p {
  margin: 0;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .simple-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .simple-main {
    padding: 1rem 0;
  }
}
</style>
