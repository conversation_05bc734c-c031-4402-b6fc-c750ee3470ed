/**
 * Targon API 集成测试工具
 * 用于验证 Targon API 的集成是否正常工作
 */

import targonApi from '@/services/targonApi'
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService'

/**
 * 测试 Targon API 基本功能
 * @param {string} apiKey - API 密钥
 * @returns {Promise<Object>} 测试结果
 */
export async function testTargonBasicFunctions(apiKey) {
  const results = {
    connection: null,
    models: null,
    chat: null,
    stream: null
  }

  try {
    // 设置 API 密钥
    targonApi.setApiKey(apiKey)
    localStorage.setItem('targon_api_key', apiKey)

    // 1. 测试连接
    console.log('🔗 测试 Targon API 连接...')
    results.connection = await targonApi.testConnection()
    console.log('连接测试结果:', results.connection)

    // 2. 测试获取模型列表
    if (results.connection.success) {
      console.log('📋 获取模型列表...')
      results.models = await targonApi.getModels()
      console.log('模型列表结果:', results.models)
    }

    // 3. 测试聊天功能
    if (results.models.success && results.models.data.length > 0) {
      const testModel = results.models.data[0].id
      console.log(`💬 测试聊天功能 (模型: ${testModel})...`)
      
      results.chat = await targonApi.chat({
        model: testModel,
        messages: [
          { role: 'user', content: '你好，请简单介绍一下自己。' }
        ],
        temperature: 0.7,
        max_tokens: 100
      })
      console.log('聊天测试结果:', results.chat)
    }

    // 4. 测试流式聊天
    if (results.chat.success) {
      console.log('🌊 测试流式聊天...')
      let streamContent = ''
      
      try {
        await new Promise((resolve, reject) => {
          targonApi.streamChat(
            {
              model: results.models.data[0].id,
              messages: [
                { role: 'user', content: '请用一句话介绍人工智能。' }
              ],
              temperature: 0.7,
              max_tokens: 50
            },
            (chunk) => {
              if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
                const content = chunk.choices[0].delta.content
                if (content) {
                  streamContent += content
                }
              }
            },
            (error) => {
              reject(error)
            },
            () => {
              resolve()
            }
          )
        })
        
        results.stream = {
          success: true,
          content: streamContent,
          message: '流式聊天测试成功'
        }
      } catch (error) {
        results.stream = {
          success: false,
          message: error.message,
          error: error
        }
      }
      
      console.log('流式聊天测试结果:', results.stream)
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error)
    return {
      success: false,
      message: error.message,
      results
    }
  }

  return {
    success: true,
    message: '所有测试完成',
    results
  }
}

/**
 * 测试聊天服务集成
 * @param {string} model - 模型名称
 * @returns {Promise<Object>} 测试结果
 */
export async function testChatServiceIntegration(model = 'deepseek-ai/DeepSeek-V3') {
  const results = {
    unified: null,
    stream: null
  }

  try {
    // 测试统一聊天接口
    console.log(`🔄 测试统一聊天接口 (模型: ${model})...`)
    results.unified = await generateChatCompletion(
      [{ role: 'user', content: '你好' }],
      { model }
    )
    console.log('统一接口测试结果:', results.unified)

    // 测试统一流式接口
    console.log('🌊 测试统一流式接口...')
    let streamContent = ''
    
    await generateChatCompletionStream(
      [{ role: 'user', content: '请说"测试成功"' }],
      { model },
      (content) => {
        streamContent += content
      }
    )
    
    results.stream = {
      success: true,
      content: streamContent,
      message: '统一流式接口测试成功'
    }
    
    console.log('统一流式接口测试结果:', results.stream)

  } catch (error) {
    console.error('聊天服务集成测试失败:', error)
    return {
      success: false,
      message: error.message,
      results
    }
  }

  return {
    success: true,
    message: '聊天服务集成测试完成',
    results
  }
}

/**
 * 运行完整测试套件
 * @param {string} apiKey - API 密钥
 * @returns {Promise<Object>} 完整测试结果
 */
export async function runFullTestSuite(apiKey) {
  console.log('🚀 开始 Targon API 完整测试套件...')
  
  const testResults = {
    basic: null,
    integration: null,
    timestamp: new Date().toISOString()
  }

  // 基本功能测试
  testResults.basic = await testTargonBasicFunctions(apiKey)
  
  // 集成测试
  if (testResults.basic.success) {
    testResults.integration = await testChatServiceIntegration()
  }

  console.log('✅ 完整测试套件执行完成')
  console.log('测试结果摘要:', {
    basicTests: testResults.basic.success,
    integrationTests: testResults.integration?.success || false,
    timestamp: testResults.timestamp
  })

  return testResults
}

// 导出便捷的测试函数
export default {
  testBasic: testTargonBasicFunctions,
  testIntegration: testChatServiceIntegration,
  runFullSuite: runFullTestSuite
}
