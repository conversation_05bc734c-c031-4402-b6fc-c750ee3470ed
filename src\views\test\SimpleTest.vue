<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">简单测试页面</h1>
      
      <!-- 状态显示 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">页面状态</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">✅</div>
            <div class="text-sm text-gray-600">页面加载成功</div>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ currentTime }}</div>
            <div class="text-sm text-gray-600">当前时间</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ clickCount }}</div>
            <div class="text-sm text-gray-600">点击次数</div>
          </div>
        </div>
      </div>

      <!-- 环境信息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">环境信息</h2>
        <div class="space-y-2 text-sm">
          <div><strong>开发模式:</strong> {{ isDev ? '是' : '否' }}</div>
          <div><strong>生产模式:</strong> {{ isProd ? '是' : '否' }}</div>
          <div><strong>基础URL:</strong> {{ baseUrl }}</div>
          <div><strong>当前路由:</strong> {{ currentRoute }}</div>
          <div><strong>浏览器:</strong> {{ userAgent }}</div>
        </div>
      </div>

      <!-- 功能测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">功能测试</h2>
        <div class="space-y-4">
          <div class="flex space-x-4">
            <button
              @click="incrementCounter"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              增加计数器
            </button>
            
            <button
              @click="resetCounter"
              class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              重置计数器
            </button>
            
            <button
              @click="showMessage"
              class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
            >
              显示消息
            </button>
          </div>
          
          <div>
            <input
              v-model="testInput"
              type="text"
              placeholder="输入测试文本..."
              class="w-full p-3 border border-gray-300 rounded-lg"
            />
          </div>
          
          <div v-if="testInput" class="p-3 bg-gray-100 rounded-lg">
            <strong>输入内容:</strong> {{ testInput }}
            <br>
            <strong>字符长度:</strong> {{ testInput.length }}
            <br>
            <strong>反转文本:</strong> {{ reversedText }}
          </div>
        </div>
      </div>

      <!-- 模拟API测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">模拟API测试</h2>
        <div class="space-y-4">
          <div class="flex space-x-4">
            <button
              @click="simulateApiCall"
              :disabled="loading"
              class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
            >
              {{ loading ? '请求中...' : '模拟API请求' }}
            </button>
            
            <button
              @click="simulateError"
              class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              模拟错误
            </button>
          </div>
          
          <div v-if="apiResult" class="p-4 rounded-lg" :class="[
            apiResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          ]">
            <div class="font-semibold" :class="[
              apiResult.success ? 'text-green-800' : 'text-red-800'
            ]">
              {{ apiResult.success ? '请求成功' : '请求失败' }}
            </div>
            <div class="text-sm mt-1" :class="[
              apiResult.success ? 'text-green-700' : 'text-red-700'
            ]">
              {{ apiResult.message }}
            </div>
            <div class="text-xs text-gray-500 mt-1">
              时间: {{ formatTime(apiResult.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

// 响应式数据
const currentTime = ref('')
const clickCount = ref(0)
const testInput = ref('')
const loading = ref(false)
const apiResult = ref(null)
const route = useRoute()

// 计算属性
const isDev = computed(() => import.meta.env.DEV)
const isProd = computed(() => import.meta.env.PROD)
const baseUrl = computed(() => import.meta.env.BASE_URL)
const currentRoute = computed(() => route.path)
const userAgent = computed(() => navigator.userAgent.split(' ')[0])
const reversedText = computed(() => testInput.value.split('').reverse().join(''))

// 时间更新
let timeInterval = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString('zh-CN')
}

// 方法
const incrementCounter = () => {
  clickCount.value++
}

const resetCounter = () => {
  clickCount.value = 0
}

const showMessage = () => {
  alert('这是一个测试消息！')
}

const simulateApiCall = async () => {
  loading.value = true
  apiResult.value = null
  
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    // 模拟成功响应
    apiResult.value = {
      success: true,
      message: '模拟API请求成功！返回了一些测试数据。',
      timestamp: new Date(),
      data: {
        id: Math.floor(Math.random() * 1000),
        value: Math.random().toFixed(2)
      }
    }
  } catch (error) {
    apiResult.value = {
      success: false,
      message: '模拟API请求失败: ' + error.message,
      timestamp: new Date()
    }
  } finally {
    loading.value = false
  }
}

const simulateError = () => {
  apiResult.value = {
    success: false,
    message: '这是一个模拟的错误消息，用于测试错误处理。',
    timestamp: new Date(),
    error: {
      code: 'MOCK_ERROR',
      details: '模拟错误详情'
    }
  }
}

const formatTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  console.log('简单测试页面已挂载')
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 组件样式 */
</style>
