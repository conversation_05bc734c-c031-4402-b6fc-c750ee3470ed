/* API密钥配置组件样式 */

:root {
  --api-config-primary-color: #409eff;
  --api-config-success-color: #67c23a;
  --api-config-warning-color: #e6a23c;
  --api-config-danger-color: #f56c6c;
  --api-config-info-color: #909399;
  --api-config-border-radius: 8px;
  --api-config-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --api-config-transition: all 0.3s ease;
}

/* 全局样式重置 */
.api-key-config *,
.api-key-management * {
  box-sizing: border-box;
}

/* 卡片样式 */
.api-key-config .el-card,
.api-key-management .el-card {
  border-radius: var(--api-config-border-radius);
  box-shadow: var(--api-config-box-shadow);
  transition: var(--api-config-transition);
}

.api-key-config .el-card:hover,
.api-key-management .el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.api-key-config .el-button,
.api-key-management .el-button {
  border-radius: 6px;
  transition: var(--api-config-transition);
}

/* 输入框样式 */
.api-key-config .el-input,
.api-key-management .el-input {
  border-radius: 6px;
}

.api-key-config .el-input__wrapper,
.api-key-management .el-input__wrapper {
  border-radius: 6px;
  transition: var(--api-config-transition);
}

/* 选择器样式 */
.api-key-config .el-select,
.api-key-management .el-select {
  width: 100%;
}

/* 对话框样式 */
.api-key-config .el-dialog,
.api-key-management .el-dialog {
  border-radius: var(--api-config-border-radius);
}

.api-key-config .el-dialog__header,
.api-key-management .el-dialog__header {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: var(--api-config-border-radius) var(--api-config-border-radius) 0 0;
}

/* 标签样式 */
.api-key-config .el-tag,
.api-key-management .el-tag {
  border-radius: 4px;
}

/* 步骤条样式 */
.api-key-config .el-steps,
.api-key-management .el-steps {
  margin: 20px 0;
}

/* 警告框样式 */
.api-key-config .el-alert,
.api-key-management .el-alert {
  border-radius: 6px;
  margin: 16px 0;
}

/* 提供商卡片特殊样式 */
.provider-card {
  cursor: pointer;
  transition: var(--api-config-transition);
  border: 2px solid #e4e7ed;
}

.provider-card:hover {
  border-color: var(--api-config-primary-color);
  transform: translateY(-2px);
}

.provider-card.active {
  border-color: var(--api-config-primary-color);
  background: rgba(64, 158, 255, 0.05);
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
  transition: var(--api-config-transition);
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  border-radius: var(--api-config-border-radius);
  transition: var(--api-config-transition);
}

.stat-icon:hover {
  transform: scale(1.1);
}

/* 预设配置卡片 */
.preset-card {
  cursor: pointer;
  transition: var(--api-config-transition);
  border: 2px solid #e4e7ed;
}

.preset-card:hover {
  border-color: var(--api-config-primary-color);
  background: rgba(64, 158, 255, 0.05);
  transform: translateY(-2px);
}

/* 功能特性卡片 */
.feature-card {
  transition: var(--api-config-transition);
}

.feature-card:hover {
  transform: translateY(-4px);
}

/* 加载状态 */
.api-key-config .loading,
.api-key-management .loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 错误状态 */
.api-key-config .error,
.api-key-management .error {
  border-color: var(--api-config-danger-color) !important;
  background: rgba(245, 108, 108, 0.05);
}

/* 成功状态 */
.api-key-config .success,
.api-key-management .success {
  border-color: var(--api-config-success-color) !important;
  background: rgba(103, 194, 58, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-key-config,
  .api-key-management {
    padding: 10px;
  }
  
  .api-key-config .el-dialog,
  .api-key-management .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .provider-card,
  .preset-card {
    margin-bottom: 16px;
  }
  
  .stat-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .api-key-config .el-button,
  .api-key-management .el-button {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .provider-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .provider-actions .el-button {
    width: 100%;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --api-config-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .provider-card.active {
    background: rgba(64, 158, 255, 0.1);
  }
  
  .preset-card:hover {
    background: rgba(64, 158, 255, 0.1);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.api-key-config .fade-in,
.api-key-management .fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.api-key-config .pulse,
.api-key-management .pulse {
  animation: pulse 2s infinite;
}

/* 滚动条样式 */
.api-key-config ::-webkit-scrollbar,
.api-key-management ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.api-key-config ::-webkit-scrollbar-track,
.api-key-management ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.api-key-config ::-webkit-scrollbar-thumb,
.api-key-management ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.api-key-config ::-webkit-scrollbar-thumb:hover,
.api-key-management ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印样式 */
@media print {
  .api-key-config,
  .api-key-management {
    box-shadow: none !important;
  }
  
  .api-key-config .el-button,
  .api-key-management .el-button {
    display: none !important;
  }
  
  .provider-actions {
    display: none !important;
  }
}
