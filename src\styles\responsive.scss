// 响应式设计统一样式文件
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 响应式容器
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @include respond-to(md) {
    padding: 0 $spacing-md;
  }

  @include respond-to(sm) {
    padding: 0 $spacing-sm;
  }
}

// 响应式网格
.responsive-grid {
  display: grid;
  gap: $spacing-lg;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

  @include respond-to(md) {
    gap: $spacing-md;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  @include respond-to(sm) {
    gap: $spacing-sm;
    grid-template-columns: 1fr;
  }
}

// 响应式文本
.responsive-text {
  &.heading {
    font-size: 2.5rem;
    line-height: 1.2;

    @include respond-to(md) {
      font-size: 2rem;
    }

    @include respond-to(sm) {
      font-size: 1.75rem;
    }
  }

  &.subheading {
    font-size: 1.5rem;
    line-height: 1.4;

    @include respond-to(md) {
      font-size: 1.25rem;
    }

    @include respond-to(sm) {
      font-size: 1.125rem;
    }
  }

  &.body {
    font-size: 1rem;
    line-height: 1.6;

    @include respond-to(sm) {
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }
}

// 响应式按钮
.responsive-btn {
  padding: $spacing-md $spacing-lg;
  font-size: 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: $primary-color;
  color: white;

  @include respond-to(md) {
    padding: $spacing-sm $spacing-md;
    font-size: 0.9rem;
  }

  @include respond-to(sm) {
    padding: $spacing-sm $spacing-md;
    font-size: 0.875rem;
    min-height: 44px; // iOS推荐的最小触摸目标
    min-width: 44px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    @include respond-to(sm) {
      transform: none; // 移动端减少悬停效果
      box-shadow: none;
    }
  }

  &.large {
    padding: $spacing-lg $spacing-xl;
    font-size: 1.125rem;

    @include respond-to(md) {
      padding: $spacing-md $spacing-lg;
      font-size: 1rem;
    }

    @include respond-to(sm) {
      padding: $spacing-md $spacing-lg;
      font-size: 0.9rem;
    }
  }

  &.small {
    padding: $spacing-sm $spacing-md;
    font-size: 0.875rem;

    @include respond-to(sm) {
      padding: $spacing-xs $spacing-sm;
      font-size: 0.8rem;
    }
  }
}

// 响应式卡片
.responsive-card {
  background: white;
  border-radius: 12px;
  padding: $spacing-xl;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  @include respond-to(md) {
    padding: $spacing-lg;
    border-radius: 10px;
  }

  @include respond-to(sm) {
    padding: $spacing-md;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    @include respond-to(sm) {
      transform: none;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式表单
.responsive-form {
  .form-group {
    margin-bottom: $spacing-lg;

    @include respond-to(sm) {
      margin-bottom: $spacing-md;
    }
  }

  .form-input {
    width: 100%;
    padding: $spacing-md;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;

    @include respond-to(sm) {
      padding: $spacing-sm $spacing-md;
      font-size: 16px; // 防止iOS缩放
      border-width: 1px;
    }

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);

      @include respond-to(sm) {
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .form-textarea {
    min-height: 120px;
    resize: vertical;

    @include respond-to(sm) {
      min-height: 80px;
    }
  }
}

// 响应式导航
.responsive-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;

  @include respond-to(md) {
    padding: $spacing-sm $spacing-md;
  }

  @include respond-to(sm) {
    padding: $spacing-sm;
    flex-wrap: wrap;
  }

  .nav-brand {
    font-size: 1.5rem;
    font-weight: bold;

    @include respond-to(sm) {
      font-size: 1.25rem;
    }
  }

  .nav-menu {
    display: flex;
    gap: $spacing-lg;

    @include respond-to(sm) {
      display: none; // 移动端隐藏，使用汉堡菜单
    }
  }

  .nav-toggle {
    display: none;

    @include respond-to(sm) {
      display: block;
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
    }
  }
}

// 响应式侧边栏
.responsive-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  @include respond-to(md) {
    width: 240px;
  }

  @include respond-to(sm) {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

    &.open {
      transform: translateX(0);
    }
  }
}

// 响应式模态框
.responsive-modal {
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-lg;

    @include respond-to(sm) {
      padding: $spacing-md;
      align-items: flex-start;
      padding-top: 10vh;
    }
  }

  .modal-content {
    background: white;
    border-radius: 12px;
    padding: $spacing-xl;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;

    @include respond-to(sm) {
      padding: $spacing-lg;
      border-radius: 8px;
      max-height: 90vh;
      margin: 0 $spacing-md;
    }
  }
}

// 响应式工具类
.hide-mobile {
  @include respond-to(sm) {
    display: none !important;
  }
}

.show-mobile {
  display: none;

  @include respond-to(sm) {
    display: block !important;
  }
}

.hide-tablet {
  @include respond-to(md) {
    display: none !important;
  }
}

.show-tablet {
  display: none;

  @include respond-to(md) {
    display: block !important;
  }
}

// 触摸优化
.touch-optimized {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;

  .clickable {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 安全区域适配（iPhone X等）
.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
