// Element Plus 组件修复样式
// 确保所有弹出组件都有正确的背景色和文字颜色

// 高对比度提示样式 - 专门用于重要提示如"请先登录"
.high-contrast-message {
  &.el-message--success {
    background: linear-gradient(135deg, #4caf50, #388e3c) !important;
    border: 3px solid #2e7d32 !important;
    box-shadow: 0 8px 32px 0 rgba(76, 175, 80, 0.4) !important;
    animation: pulse-success 2s infinite !important;

    .el-message__content {
      color: #ffffff !important;
      font-weight: 700 !important;
      font-size: 16px !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .el-message__icon {
      color: #ffffff !important;
      font-size: 20px !important;
      animation: bounce 0.6s ease-in-out !important;
    }
  }

  &.el-message--warning {
    background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
    border: 3px solid #e65100 !important;
    box-shadow: 0 8px 32px 0 rgba(255, 107, 53, 0.4) !important;
    animation: pulse-warning 2s infinite !important;

    .el-message__content {
      color: #ffffff !important;
      font-weight: 700 !important;
      font-size: 16px !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .el-message__icon {
      color: #ffffff !important;
      font-size: 20px !important;
      animation: shake 0.5s ease-in-out !important;
    }
  }

  &.el-message--error {
    background: linear-gradient(135deg, #ff1744, #d50000) !important;
    border: 3px solid #b71c1c !important;
    box-shadow: 0 8px 32px 0 rgba(255, 23, 68, 0.4) !important;
    animation: pulse-error 2s infinite !important;

    .el-message__content {
      color: #ffffff !important;
      font-weight: 700 !important;
      font-size: 16px !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .el-message__icon {
      color: #ffffff !important;
      font-size: 20px !important;
      animation: shake 0.5s ease-in-out !important;
    }
  }

  &.el-message--info {
    background: linear-gradient(135deg, #2196f3, #1976d2) !important;
    border: 3px solid #0d47a1 !important;
    box-shadow: 0 8px 32px 0 rgba(33, 150, 243, 0.4) !important;
    animation: pulse-info 2s infinite !important;

    .el-message__content {
      color: #ffffff !important;
      font-weight: 700 !important;
      font-size: 16px !important;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    .el-message__icon {
      color: #ffffff !important;
      font-size: 20px !important;
    }
  }
}

// 动画效果
@keyframes pulse-success {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px 0 rgba(76, 175, 80, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px 0 rgba(76, 175, 80, 0.6);
  }
}

@keyframes pulse-warning {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px 0 rgba(255, 107, 53, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px 0 rgba(255, 107, 53, 0.6);
  }
}

@keyframes pulse-error {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px 0 rgba(255, 23, 68, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px 0 rgba(255, 23, 68, 0.6);
  }
}

@keyframes pulse-info {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px 0 rgba(33, 150, 243, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px 0 rgba(33, 150, 243, 0.6);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-8px); }
  60% { transform: translateY(-4px); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

// 高对比度对话框样式
.high-contrast-dialog {
  .el-message-box__header {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: #ffffff !important;
    border-radius: 16px 16px 0 0 !important;

    .el-message-box__title {
      color: #ffffff !important;
      font-weight: 700 !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    .el-message-box__headerbtn .el-message-box__close {
      color: rgba(255, 255, 255, 0.8) !important;

      &:hover {
        color: #ffffff !important;
      }
    }
  }

  .el-message-box__content {
    .el-message-box__status {
      &.el-message-box-icon--warning {
        color: #ff6b35 !important;
        font-size: 32px !important;
        animation: pulse 1.5s infinite !important;
      }

      &.el-message-box-icon--error {
        color: #ff1744 !important;
        font-size: 32px !important;
        animation: shake 0.5s ease-in-out !important;
      }

      &.el-message-box-icon--success {
        color: #4caf50 !important;
        font-size: 32px !important;
        animation: bounce 0.6s ease-in-out !important;
      }

      &.el-message-box-icon--info {
        color: #2196f3 !important;
        font-size: 32px !important;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

// 消息框修复 - 高对比度优化
.el-message-box {
  background-color: #ffffff !important;
  color: #1a202c !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 16px !important;
  box-shadow: 0 20px 60px 0 rgba(0, 0, 0, 0.15) !important;
  overflow: hidden !important;

  .el-message-box__wrapper {
    background-color: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px) !important;
  }

  .el-message-box__header {
    background-color: #ffffff !important;
    border-bottom: 2px solid #f7fafc !important;
    padding: 24px 24px 16px !important;

    .el-message-box__title {
      color: #1a202c !important;
      font-weight: 700 !important;
      font-size: 18px !important;
    }

    .el-message-box__headerbtn {
      .el-message-box__close {
        color: #718096 !important;
        font-size: 20px !important;
        font-weight: bold !important;

        &:hover {
          color: #2d3748 !important;
          transform: scale(1.1) !important;
        }
      }
    }
  }
  
  .el-message-box__content {
    background-color: #ffffff !important;
    color: #2d3748 !important;
    padding: 16px 24px 24px !important;

    .el-message-box__container {
      .el-message-box__message {
        color: #2d3748 !important;
        font-size: 15px !important;
        line-height: 1.6 !important;
        font-weight: 500 !important;

        p {
          color: #2d3748 !important;
          margin: 0 !important;
        }
      }

      .el-message-box__status {
        font-size: 28px !important;
        margin-right: 16px !important;

        &.el-message-box-icon--success {
          color: #38a169 !important;
        }

        &.el-message-box-icon--warning {
          color: #d69e2e !important;
        }

        &.el-message-box-icon--error {
          color: #e53e3e !important;
        }

        &.el-message-box-icon--info {
          color: #3182ce !important;
        }
      }

      .el-message-box__input {
        margin-top: 16px !important;

        .el-input__wrapper {
          background-color: #ffffff !important;
          border: 2px solid #e2e8f0 !important;
          border-radius: 8px !important;

          &:hover {
            border-color: #cbd5e0 !important;
          }

          &.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
          }

          .el-input__inner {
            color: #2d3748 !important;
            font-weight: 500 !important;

            &::placeholder {
              color: #a0aec0 !important;
            }
          }
        }
      }
    }
  }
  
  .el-message-box__btns {
    background-color: #ffffff !important;
    padding: 16px 24px 24px !important;
    border-top: 2px solid #f7fafc !important;

    .el-button {
      font-weight: 600 !important;
      border-radius: 8px !important;
      padding: 12px 24px !important;
      font-size: 14px !important;

      &.el-button--default {
        background-color: #ffffff !important;
        border: 2px solid #e2e8f0 !important;
        color: #4a5568 !important;

        &:hover {
          background-color: #f7fafc !important;
          border-color: #cbd5e0 !important;
          color: #2d3748 !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border: 2px solid #667eea !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;

        &:hover {
          background: linear-gradient(135deg, #7c8ceb, #8b5fbf) !important;
          border-color: #7c8ceb !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }

      &.el-button--danger {
        background: linear-gradient(135deg, #e53e3e, #c53030) !important;
        border: 2px solid #e53e3e !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3) !important;

        &:hover {
          background: linear-gradient(135deg, #fc8181, #e53e3e) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 6px 16px rgba(229, 62, 62, 0.4) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }
    }
  }
}

// 对话框修复
.el-dialog {
  background-color: #ffffff !important;
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  
  .el-dialog__wrapper {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  .el-dialog__header {
    background-color: #ffffff !important;
    color: #303133 !important;
    border-bottom: 1px solid #ebeef5 !important;
    
    .el-dialog__title {
      color: #303133 !important;
      font-weight: 600 !important;
    }
    
    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #909399 !important;
        
        &:hover {
          color: #667eea !important;
        }
      }
    }
  }
  
  .el-dialog__body {
    background-color: #ffffff !important;
    color: #606266 !important;
    
    * {
      color: inherit !important;
    }
  }
  
  .el-dialog__footer {
    background-color: #ffffff !important;
    border-top: 1px solid #ebeef5 !important;
  }
}

// 消息提示修复 - 高对比度优化
.el-message {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15) !important;
  border: 2px solid transparent !important;
  font-size: 15px !important;
  padding: 16px 20px !important;
  min-width: 300px !important;
  max-width: 500px !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 3000 !important;

  .el-message__content {
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    line-height: 1.4 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  .el-message__icon {
    font-size: 18px !important;
    margin-right: 12px !important;
  }

  &.el-message--success {
    background: linear-gradient(135deg, #52c41a, #389e0d) !important;
    border-color: #389e0d !important;
    box-shadow: 0 4px 20px 0 rgba(82, 196, 26, 0.3) !important;
  }

  &.el-message--warning {
    background: linear-gradient(135deg, #fa8c16, #d46b08) !important;
    border-color: #d46b08 !important;
    box-shadow: 0 4px 20px 0 rgba(250, 140, 22, 0.3) !important;
  }

  &.el-message--error {
    background: linear-gradient(135deg, #ff4d4f, #cf1322) !important;
    border-color: #cf1322 !important;
    box-shadow: 0 4px 20px 0 rgba(255, 77, 79, 0.3) !important;
  }

  &.el-message--info {
    background: linear-gradient(135deg, #1890ff, #096dd9) !important;
    border-color: #096dd9 !important;
    box-shadow: 0 4px 20px 0 rgba(24, 144, 255, 0.3) !important;
  }

  // 关闭按钮优化
  .el-message__closeBtn {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 16px !important;
    font-weight: bold !important;

    &:hover {
      color: #ffffff !important;
      transform: scale(1.1) !important;
    }
  }
}

// 高对比度消息的特殊定位
.high-contrast-message {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 3000 !important;

  // 添加背景遮罩效果
  &::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: -1 !important;
    backdrop-filter: blur(2px) !important;
  }
}

// 通知修复 - 高对比度优化
.el-notification {
  background-color: #ffffff !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.12) !important;
  padding: 20px !important;
  min-width: 350px !important;

  .el-notification__title {
    color: #1a202c !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  .el-notification__content {
    color: #2d3748 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  .el-notification__icon {
    font-size: 24px !important;
    margin-right: 16px !important;
  }

  .el-notification__closeBtn {
    color: #718096 !important;
    font-size: 18px !important;
    font-weight: bold !important;

    &:hover {
      color: #2d3748 !important;
      transform: scale(1.1) !important;
    }
  }

  // 不同类型的通知样式
  &.el-notification--success {
    border-left: 6px solid #38a169 !important;

    .el-notification__icon {
      color: #38a169 !important;
    }
  }

  &.el-notification--warning {
    border-left: 6px solid #d69e2e !important;

    .el-notification__icon {
      color: #d69e2e !important;
    }
  }

  &.el-notification--error {
    border-left: 6px solid #e53e3e !important;

    .el-notification__icon {
      color: #e53e3e !important;
    }
  }

  &.el-notification--info {
    border-left: 6px solid #3182ce !important;

    .el-notification__icon {
      color: #3182ce !important;
    }
  }
}

// 下拉菜单修复
.el-dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  
  .el-dropdown-menu__item {
    color: #606266 !important;
    
    &:hover {
      background-color: #ecf5ff !important;
      color: #667eea !important;
    }
  }
}

// 工具提示修复
.el-tooltip__popper {
  background-color: #303133 !important;
  color: #ffffff !important;
  border: 1px solid #303133 !important;
  
  .el-tooltip__arrow {
    &::before {
      border-top-color: #303133 !important;
    }
  }
}

// 弹出确认框修复
.el-popconfirm {
  background-color: #ffffff !important;
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  
  .el-popconfirm__main {
    color: #606266 !important;
  }
  
  .el-popconfirm__action {
    .el-button {
      &.el-button--small {
        font-size: 12px !important;
      }
    }
  }
}

// 选择器下拉修复
.el-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #ebeef5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  
  .el-select-dropdown__item {
    color: #606266 !important;
    
    &:hover {
      background-color: #f5f7fa !important;
    }
    
    &.is-selected {
      background-color: #ecf5ff !important;
      color: #667eea !important;
      font-weight: 600 !important;
    }
  }
}
