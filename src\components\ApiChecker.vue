<template>
  <div class="api-checker">
    <el-card class="checker-card">
      <template #header>
        <div class="card-header">
          <h3>API 检查工具</h3>
          <p class="subtitle">输入URL和密钥，自动查询所有可用模型</p>
        </div>
      </template>

      <el-form :model="form" label-width="100px" class="checker-form">
        <el-form-item label="API URL">
          <el-input
            v-model="form.url"
            placeholder="请输入API地址，如: https://openrouter.ai/api/v1"
            clearable
          >
            <template #prepend>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="API 密钥">
          <el-input
            v-model="form.apiKey"
            type="password"
            placeholder="请输入API密钥"
            show-password
            clearable
          >
            <template #prepend>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="模型筛选">
          <el-radio-group v-model="form.onlyFree">
            <el-radio :label="true">仅免费模型</el-radio>
            <el-radio :label="false">所有模型</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="testConnection"
            :loading="testing"
            :disabled="!form.url || !form.apiKey"
          >
            <el-icon><Search /></el-icon>
            测试连接
          </el-button>
          
          <el-button 
            type="success" 
            @click="queryModels"
            :loading="querying"
            :disabled="!form.url || !form.apiKey"
          >
            <el-icon><List /></el-icon>
            查询模型
          </el-button>
          
          <el-button 
            @click="resetToDefault"
          >
            <el-icon><Refresh /></el-icon>
            重置默认
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 连接测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-alert
          :title="testResult.success ? '连接成功' : '连接失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
          :closable="false"
        />
        
        <div v-if="testResult.success && testResult.models" class="sample-models">
          <h4>示例模型 (前5个):</h4>
          <el-tag 
            v-for="model in testResult.models" 
            :key="model.id"
            class="model-tag"
            type="info"
          >
            {{ model.name || model.id }}
          </el-tag>
        </div>
      </div>

      <!-- 模型查询结果 -->
      <div v-if="modelResult" class="model-result">
        <div class="result-header">
          <h4>查询结果</h4>
          <div class="result-stats">
            <el-tag type="primary">总计: {{ modelResult.totalCount }}</el-tag>
            <el-tag type="success">免费: {{ modelResult.freeCount }}</el-tag>
          </div>
        </div>

        <div v-if="modelResult.success" class="models-grid">
          <el-card 
            v-for="model in displayModels" 
            :key="model.id"
            class="model-card"
            shadow="hover"
          >
            <div class="model-info">
              <div class="model-header">
                <h5 class="model-name">{{ model.name || model.id }}</h5>
                <el-tag 
                  :type="model.isFree ? 'success' : 'warning'"
                  size="small"
                >
                  {{ model.isFree ? '免费' : '付费' }}
                </el-tag>
              </div>
              
              <p class="model-id">{{ model.id }}</p>
              
              <div class="model-details">
                <span class="provider">{{ model.provider || '未知' }}</span>
                <span class="context">{{ model.context_length || 4096 }} tokens</span>
              </div>
              
              <p v-if="model.description" class="model-description">
                {{ model.description }}
              </p>
            </div>
          </el-card>
        </div>

        <div v-if="modelResult.models.length > displayLimit" class="load-more">
          <el-button @click="loadMore" type="text">
            显示更多 ({{ modelResult.models.length - displayLimit }} 个)
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Key, Search, List, Refresh } from '@element-plus/icons-vue'
import { apiConfigManager } from '@/services/openrouterApi.js'

// 响应式数据
const form = ref({
  url: 'https://openrouter.ai/api/v1',
  apiKey: '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',
  onlyFree: true
})

const testing = ref(false)
const querying = ref(false)
const testResult = ref(null)
const modelResult = ref(null)
const displayLimit = ref(20)

// 计算属性
const displayModels = computed(() => {
  if (!modelResult.value?.models) return []
  return modelResult.value.models.slice(0, displayLimit.value)
})

// 方法
const testConnection = async () => {
  testing.value = true
  testResult.value = null
  
  try {
    // 设置临时配置
    apiConfigManager.setCustomConfig(form.value.url, form.value.apiKey)
    
    // 测试连接
    const result = await apiConfigManager.testCurrentConfig()
    testResult.value = result
    
  } catch (error) {
    testResult.value = {
      success: false,
      message: error.message
    }
  } finally {
    testing.value = false
  }
}

const queryModels = async () => {
  querying.value = true
  modelResult.value = null
  displayLimit.value = 20
  
  try {
    const result = await apiConfigManager.queryAllModels(
      form.value.url, 
      form.value.apiKey, 
      form.value.onlyFree
    )
    
    modelResult.value = result
    
  } catch (error) {
    ElMessage.error(`查询失败: ${error.message}`)
  } finally {
    querying.value = false
  }
}

const resetToDefault = () => {
  form.value.url = 'https://openrouter.ai/api/v1'
  form.value.apiKey = '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'
  form.value.onlyFree = true
  
  apiConfigManager.resetToDefault()
  testResult.value = null
  modelResult.value = null
  
  ElMessage.success('已重置为默认配置')
}

const loadMore = () => {
  displayLimit.value += 20
}

// 生命周期
onMounted(() => {
  // 初始化时设置默认配置
  apiConfigManager.setCustomConfig(form.value.url, form.value.apiKey)
})
</script>

<style scoped>
.api-checker {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.checker-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.checker-form {
  margin-top: 20px;
}

.test-result {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.sample-models {
  margin-top: 16px;
}

.sample-models h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.model-tag {
  margin: 4px 8px 4px 0;
}

.model-result {
  margin-top: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.result-stats .el-tag {
  margin-left: 8px;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.model-card {
  border-radius: 8px;
  transition: transform 0.2s;
}

.model-card:hover {
  transform: translateY(-2px);
}

.model-info {
  padding: 4px;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.model-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  margin-right: 8px;
}

.model-id {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
  font-family: monospace;
  word-break: break-all;
}

.model-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
}

.model-description {
  margin: 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.load-more {
  text-align: center;
  padding: 20px;
}

@media (max-width: 768px) {
  .models-grid {
    grid-template-columns: 1fr;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
