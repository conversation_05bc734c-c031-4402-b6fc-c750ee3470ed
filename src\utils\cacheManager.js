/**
 * 缓存管理工具
 * 用于清理浏览器缓存、Service Worker等
 */

/**
 * 清除所有缓存
 */
export async function clearAllCaches() {
  const results = {
    localStorage: false,
    sessionStorage: false,
    indexedDB: false,
    serviceWorker: false,
    caches: false
  }

  try {
    // 清除 localStorage
    localStorage.clear()
    results.localStorage = true
    console.log('✅ localStorage 已清除')
  } catch (error) {
    console.warn('⚠️ 清除 localStorage 失败:', error)
  }

  try {
    // 清除 sessionStorage
    sessionStorage.clear()
    results.sessionStorage = true
    console.log('✅ sessionStorage 已清除')
  } catch (error) {
    console.warn('⚠️ 清除 sessionStorage 失败:', error)
  }

  try {
    // 清除 IndexedDB
    if ('indexedDB' in window) {
      const databases = await indexedDB.databases()
      await Promise.all(
        databases.map(db => {
          return new Promise((resolve, reject) => {
            const deleteReq = indexedDB.deleteDatabase(db.name)
            deleteReq.onsuccess = () => resolve()
            deleteReq.onerror = () => reject(deleteReq.error)
          })
        })
      )
      results.indexedDB = true
      console.log('✅ IndexedDB 已清除')
    }
  } catch (error) {
    console.warn('⚠️ 清除 IndexedDB 失败:', error)
  }

  try {
    // 清除 Service Worker
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations()
      await Promise.all(
        registrations.map(registration => registration.unregister())
      )
      results.serviceWorker = true
      console.log('✅ Service Worker 已清除')
    }
  } catch (error) {
    console.warn('⚠️ 清除 Service Worker 失败:', error)
  }

  try {
    // 清除 Cache API
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
      results.caches = true
      console.log('✅ Cache API 已清除')
    }
  } catch (error) {
    console.warn('⚠️ 清除 Cache API 失败:', error)
  }

  return results
}

/**
 * 检查缓存状态
 */
export async function checkCacheStatus() {
  const status = {
    localStorage: {
      enabled: 'localStorage' in window,
      itemCount: 0,
      size: 0
    },
    sessionStorage: {
      enabled: 'sessionStorage' in window,
      itemCount: 0,
      size: 0
    },
    indexedDB: {
      enabled: 'indexedDB' in window,
      databases: []
    },
    serviceWorker: {
      enabled: 'serviceWorker' in navigator,
      registrations: []
    },
    caches: {
      enabled: 'caches' in window,
      cacheNames: []
    }
  }

  try {
    // 检查 localStorage
    if (status.localStorage.enabled) {
      status.localStorage.itemCount = localStorage.length
      let totalSize = 0
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        const value = localStorage.getItem(key)
        totalSize += key.length + (value ? value.length : 0)
      }
      status.localStorage.size = totalSize
    }
  } catch (error) {
    console.warn('检查 localStorage 状态失败:', error)
  }

  try {
    // 检查 sessionStorage
    if (status.sessionStorage.enabled) {
      status.sessionStorage.itemCount = sessionStorage.length
      let totalSize = 0
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        const value = sessionStorage.getItem(key)
        totalSize += key.length + (value ? value.length : 0)
      }
      status.sessionStorage.size = totalSize
    }
  } catch (error) {
    console.warn('检查 sessionStorage 状态失败:', error)
  }

  try {
    // 检查 IndexedDB
    if (status.indexedDB.enabled) {
      status.indexedDB.databases = await indexedDB.databases()
    }
  } catch (error) {
    console.warn('检查 IndexedDB 状态失败:', error)
  }

  try {
    // 检查 Service Worker
    if (status.serviceWorker.enabled) {
      status.serviceWorker.registrations = await navigator.serviceWorker.getRegistrations()
    }
  } catch (error) {
    console.warn('检查 Service Worker 状态失败:', error)
  }

  try {
    // 检查 Cache API
    if (status.caches.enabled) {
      status.caches.cacheNames = await caches.keys()
    }
  } catch (error) {
    console.warn('检查 Cache API 状态失败:', error)
  }

  return status
}

/**
 * 强制刷新页面（绕过缓存）
 */
export function forceReload() {
  if ('location' in window) {
    window.location.reload(true)
  }
}

/**
 * 检查是否需要清除缓存
 * 基于版本号或其他条件
 */
export function shouldClearCache() {
  const currentVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'
  const cachedVersion = localStorage.getItem('app_version')
  
  if (!cachedVersion || cachedVersion !== currentVersion) {
    localStorage.setItem('app_version', currentVersion)
    return true
  }
  
  return false
}

/**
 * 开发环境缓存清理
 */
export async function clearDevelopmentCache() {
  if (import.meta.env.DEV) {
    console.log('🧹 开发环境：清理缓存...')
    
    // 清除可能的 Service Worker
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations()
      for (const registration of registrations) {
        await registration.unregister()
        console.log('🧹 已清除 Service Worker 注册')
      }
    }
    
    // 清除 Cache API
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      for (const cacheName of cacheNames) {
        await caches.delete(cacheName)
        console.log('🧹 已清除缓存:', cacheName)
      }
    }
    
    console.log('✅ 开发环境缓存清理完成')
  }
}
