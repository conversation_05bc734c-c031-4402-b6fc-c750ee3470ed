// 统一导出所有store
export { useAppStore } from './app'
export { useUserStore } from './user'
export { useChatStore } from './chat'
export { useDrawingStore } from './drawing'

// 初始化所有store - 这个函数应该在 Vue 应用上下文中调用
export const initStores = async () => {
  console.log('🔧 开始初始化Stores...')

  const results = {
    success: true,
    errors: []
  }

  try {
    // 这里不直接调用 useStore，而是返回一个初始化函数
    // 实际的初始化应该在组件中进行
    console.log('✅ Stores初始化配置完成')
    return results
  } catch (error) {
    console.error('❌ Stores初始化配置失败:', error)
    results.success = false
    results.errors.push(error)
    return results
  }
}

// 重置所有store - 这个函数应该在 Vue 组件上下文中调用
export const resetAllStores = () => {
  console.log('🔄 重置所有Stores...')
  // 实际的重置逻辑应该在组件中实现
  // 这里只是一个占位符函数
}
