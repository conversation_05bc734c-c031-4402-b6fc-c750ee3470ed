<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆角矩形 -->
  <rect width="192" height="192" rx="38" fill="url(#gradient)"/>

  <!-- 背景圆形 -->
  <circle cx="96" cy="96" r="75" fill="white" opacity="0.1"/>

  <!-- AI字母设计 -->
  <g fill="white" font-family="Arial, sans-serif" font-weight="bold">
    <!-- A字母 -->
    <path d="M60 135 L75 80 L90 135 M68 115 L82 115" stroke="white" stroke-width="5" fill="none"/>
    <!-- I字母 -->
    <rect x="108" y="80" width="10" height="55" fill="white"/>
    <rect x="104" y="80" width="18" height="8" fill="white"/>
    <rect x="104" y="127" width="18" height="8" fill="white"/>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="145" cy="47" r="5" fill="rgba(255,255,255,0.8)"/>
  <circle cx="47" cy="47" r="4" fill="rgba(255,255,255,0.6)"/>
  <circle cx="150" cy="145" r="4.5" fill="rgba(255,255,255,0.7)"/>
</svg>
