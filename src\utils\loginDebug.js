// 登录状态调试工具
import { getToken } from '@/utils/auth'
import { APP_CONFIG } from '@/config'

// 检查本地存储中的登录信息
export function checkLocalStorage() {
  console.group('🔍 本地存储登录信息检查')
  
  try {
    // 检查token
    const token = getToken()
    console.log('Token:', token ? `${token.substring(0, 30)}...` : '无')
    
    // 检查用户信息
    const userInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo)
        console.log('用户信息:', {
          username: parsed.username,
          email: parsed.email,
          role: parsed.role,
          id: parsed.id
        })
      } catch (error) {
        console.error('用户信息解析失败:', error)
      }
    } else {
      console.log('用户信息: 无')
    }
    
    // 检查记住我信息
    const rememberMe = localStorage.getItem('ai_creative_remember_me')
    if (rememberMe) {
      try {
        const parsed = JSON.parse(rememberMe)
        console.log('记住我信息:', parsed)
      } catch (error) {
        console.error('记住我信息解析失败:', error)
      }
    } else {
      console.log('记住我信息: 无')
    }
    
    // 检查设置
    const settings = localStorage.getItem(APP_CONFIG.storage.settingsKey)
    console.log('用户设置:', settings ? '存在' : '无')
    
  } catch (error) {
    console.error('检查本地存储失败:', error)
  }
  
  console.groupEnd()
}

// 清除所有登录相关的本地存储
export function clearAllLoginData() {
  console.group('🗑️ 清除所有登录数据')
  
  try {
    // 清除token
    localStorage.removeItem(APP_CONFIG.storage.tokenKey)
    console.log('✅ 已清除token')
    
    // 清除用户信息
    localStorage.removeItem(APP_CONFIG.storage.userKey)
    console.log('✅ 已清除用户信息')
    
    // 清除设置
    localStorage.removeItem(APP_CONFIG.storage.settingsKey)
    console.log('✅ 已清除用户设置')
    
    // 清除记住我信息
    localStorage.removeItem('ai_creative_remember_me')
    console.log('✅ 已清除记住我信息')
    
    console.log('🎉 所有登录数据已清除')
  } catch (error) {
    console.error('清除登录数据失败:', error)
  }
  
  console.groupEnd()
}

// 模拟登录状态
export function simulateLogin(username = 'admin', role = 'admin') {
  console.group('🎭 模拟登录状态')
  
  try {
    // 生成模拟token
    const mockToken = 'mock_token_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    
    // 生成模拟用户信息
    const mockUserInfo = {
      id: Date.now(),
      username: username,
      email: `${username}@example.com`,
      avatar: '/default-avatar.svg',
      role: role,
      nickname: username,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      profile: {
        bio: '',
        location: '',
        website: '',
        birthday: null,
        gender: null
      },
      preferences: {
        theme: 'light',
        language: 'zh-CN',
        notifications: {
          email: true,
          push: true,
          marketing: false
        }
      }
    }
    
    // 保存到本地存储
    localStorage.setItem(APP_CONFIG.storage.tokenKey, mockToken)
    localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(mockUserInfo))
    
    console.log('✅ 模拟登录成功:', {
      username: mockUserInfo.username,
      role: mockUserInfo.role,
      token: mockToken.substring(0, 30) + '...'
    })
    
    console.log('🔄 请刷新页面以应用模拟登录状态')
  } catch (error) {
    console.error('模拟登录失败:', error)
  }
  
  console.groupEnd()
}

// 在开发环境下暴露调试函数到全局
if (import.meta.env.DEV) {
  window.loginDebug = {
    check: checkLocalStorage,
    clear: clearAllLoginData,
    simulate: simulateLogin
  }
  
  console.log('🛠️ 登录调试工具已加载，使用方法:')
  console.log('- window.loginDebug.check() - 检查本地存储')
  console.log('- window.loginDebug.clear() - 清除所有登录数据')
  console.log('- window.loginDebug.simulate() - 模拟登录状态')
}
