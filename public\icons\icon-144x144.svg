<svg width="144" height="144" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆角矩形 -->
  <rect width="144" height="144" rx="28" fill="url(#gradient)"/>

  <!-- 背景圆形 -->
  <circle cx="72" cy="72" r="55" fill="white" opacity="0.1"/>

  <!-- AI字母设计 -->
  <g fill="white" font-family="Arial, sans-serif" font-weight="bold">
    <!-- A字母 -->
    <path d="M45 100 L55 60 L65 100 M50 85 L60 85" stroke="white" stroke-width="4" fill="none"/>
    <!-- I字母 -->
    <rect x="80" y="60" width="8" height="40" fill="white"/>
    <rect x="77" y="60" width="14" height="6" fill="white"/>
    <rect x="77" y="94" width="14" height="6" fill="white"/>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="105" cy="35" r="4" fill="rgba(255,255,255,0.8)"/>
  <circle cx="35" cy="35" r="3" fill="rgba(255,255,255,0.6)"/>
  <circle cx="110" cy="105" r="3.5" fill="rgba(255,255,255,0.7)"/>
</svg>
