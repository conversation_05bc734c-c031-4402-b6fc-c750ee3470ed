# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Creative Platform is a modern Vue 3 web application that integrates multiple AI services for chat, image generation, and content creation. The platform features a comprehensive admin system and supports both desktop and mobile experiences.

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build with production environment
npm run build:prod

# Preview production build
npm run preview

# Lint and fix code
npm run lint

# Format code
npm run format
```

## Architecture

### Tech Stack
- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite 5.x
- **UI Library**: Element Plus
- **State Management**: Pinia
- **Routing**: Vue Router
- **HTTP Client**: Axios
- **Styling**: SCSS with responsive design
- **Code Quality**: ESLint + Prettier

### Key Directories
- `src/components/` - Reusable components organized by feature (common, chat, admin, drawing, etc.)
- `src/views/` - Page components organized by feature area
- `src/services/` - AI service integrations (Pollinations, OpenRouter, image generation, audio)
- `src/stores/` - Pinia state management stores
- `src/config/` - Configuration files including AI model definitions
- `src/utils/` - Utility functions and helpers
- `src/styles/` - Global styles with responsive design patterns

### AI Service Integration
The platform integrates multiple AI services:
- **Pollinations.AI** - Free 25+ models for text, image, and audio (no API key required)
- **OpenRouter** - 100+ premium models (requires API key)
- **GeminiPool** - Latest Gemini models (requires API key)

Service files in `src/services/`:
- `pollinationsApi.js` - Primary free AI service
- `openrouterApi.js` - Premium AI models
- `imageGenerationService.js` - Unified image generation
- `audioService.js` - Text-to-speech and audio processing
- `apiKeyManager.js` - API key management and rotation

### State Management Pattern
Uses Pinia with feature-based stores:
- `app.js` - Global app state and theme management
- `user.js` - User authentication and profile
- `chat.js` - Chat conversations and message history
- `drawing.js` - Image generation and gallery

### Routing Structure
- Main layout routes under `/` using `Layout.vue`
- Authentication routes (login, register) without layout
- Admin routes require authentication and admin role
- Test routes for development and API testing

### Responsive Design
- Mobile-first approach with dedicated mobile components
- Responsive utilities in `src/composables/useResponsive.js`
- Mobile-specific styles in `src/styles/mobile.scss`
- Breakpoints: mobile (< 768px), tablet (768px-1024px), desktop (> 1024px)

## Code Conventions

### Component Structure
- Use Vue 3 Composition API consistently
- Components should be self-contained with props/emits interfaces
- Place component-specific styles in `<style scoped>` blocks
- Use SCSS variables from `src/styles/variables.scss`

### Naming Conventions
- Components: PascalCase (e.g., `ChatHeader.vue`)
- Files: kebab-case (e.g., `chat-header.vue`)
- Stores: camelCase (e.g., `useUserStore`)
- CSS classes: BEM methodology or utility classes

### State Management
- Use Pinia stores for shared state
- Keep component-local state in `ref()` or `reactive()`
- Use computed properties for derived state
- Actions should handle async operations and error handling

## Important Implementation Details

### Authentication Flow
- User state managed in `src/stores/user.js`
- Route guards check `requiresAuth` and `requiresAdmin` meta fields
- Login redirect preserves original destination
- Admin routes require both authentication and admin role

### Error Handling
- Global error handlers in `src/main.js`
- Service-level error handling in API files
- User-facing error messages use Element Plus components
- Console logging for development debugging

### API Configuration
- Base URLs and endpoints in `src/config/apiConfig.js`
- Environment variables prefixed with `VITE_`
- API key management supports multiple providers
- Service abstraction allows easy provider switching

### Mobile Optimization
- Uses responsive breakpoints for layout adaptation
- Touch-friendly interface elements (44px minimum touch targets)
- Mobile-specific components in `/mobile/` directories
- PWA support with service worker registration

## Testing Strategy

The project includes various test pages for development:
- `/test` - Main testing interface
- `/api-test` - Pollinations API testing
- `/openrouter-test` - OpenRouter API testing
- `/mobile-test` - Mobile-specific testing
- `/health` - System health checks

## Development Workflow

1. Start with `npm run dev` for development server
2. Use browser dev tools for responsive testing
3. Test AI integrations via dedicated test pages
4. Run `npm run lint` before commits
5. Use `npm run format` for code formatting
6. Build with `npm run build` to verify production readiness

## Deployment Notes

- Production builds use `npm run build:prod`
- Service Worker enabled for PWA functionality
- Supports deployment to static hosting (Vercel, Netlify, etc.)
- Environment variables configured for production endpoints