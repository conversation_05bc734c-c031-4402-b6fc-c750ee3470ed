<template>
  <div class="api-check-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><Tools /></el-icon>
          API 检查工具
        </h1>
        <p class="page-description">
          支持OpenAI兼容API的连接测试和模型查询，参考 
          <el-link 
            href="https://github.com/october-coder/api-check" 
            target="_blank" 
            type="primary"
          >
            october-coder/api-check
          </el-link>
          项目实现
        </p>
      </div>
    </div>

    <!-- 功能介绍 -->
    <el-row :gutter="20" class="feature-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card" shadow="hover">
          <div class="feature-content">
            <el-icon class="feature-icon" color="#409EFF"><Connection /></el-icon>
            <h3>连接测试</h3>
            <p>快速测试API连接状态和可用性</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card" shadow="hover">
          <div class="feature-content">
            <el-icon class="feature-icon" color="#67C23A"><List /></el-icon>
            <h3>模型查询</h3>
            <p>自动获取所有可用模型列表</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card" shadow="hover">
          <div class="feature-content">
            <el-icon class="feature-icon" color="#E6A23C"><Filter /></el-icon>
            <h3>智能筛选</h3>
            <p>支持免费模型和付费模型筛选</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card" shadow="hover">
          <div class="feature-content">
            <el-icon class="feature-icon" color="#F56C6C"><Setting /></el-icon>
            <h3>配置管理</h3>
            <p>支持自定义URL和密钥配置</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- API检查组件 -->
    <ApiChecker />

    <!-- 使用说明 -->
    <el-card class="usage-card">
      <template #header>
        <h3><el-icon><Document /></el-icon> 使用说明</h3>
      </template>
      
      <el-steps :active="4" align-center class="usage-steps">
        <el-step title="输入API地址" description="填入OpenAI兼容的API端点URL" />
        <el-step title="输入API密钥" description="填入有效的API密钥" />
        <el-step title="选择筛选条件" description="选择查询免费模型或所有模型" />
        <el-step title="测试和查询" description="点击按钮进行连接测试和模型查询" />
      </el-steps>

      <div class="usage-tips">
        <h4>支持的API类型：</h4>
        <ul>
          <li><strong>OpenRouter:</strong> https://openrouter.ai/api/v1</li>
          <li><strong>OpenAI:</strong> https://api.openai.com/v1</li>
          <li><strong>OneAPI:</strong> https://your-domain/v1</li>
          <li><strong>NewAPI:</strong> https://your-domain/v1</li>
          <li><strong>其他OpenAI兼容API</strong></li>
        </ul>

        <h4>默认配置：</h4>
        <ul>
          <li><strong>URL:</strong> https://openrouter.ai/api/v1</li>
          <li><strong>密钥:</strong> 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</li>
          <li><strong>筛选:</strong> 仅免费模型</li>
        </ul>

        <el-alert
          title="注意事项"
          type="info"
          :closable="false"
          show-icon
        >
          <ul class="alert-list">
            <li>请确保API密钥有效且有足够的配额</li>
            <li>某些API可能有速率限制，请适当控制请求频率</li>
            <li>免费模型筛选基于OpenRouter的免费模型列表</li>
            <li>所有操作都在前端进行，不会上传您的密钥到服务器</li>
          </ul>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Tools, Connection, List, Filter, Setting, Document } from '@element-plus/icons-vue'
import ApiChecker from '@/components/ApiChecker.vue'

// 设置页面标题
document.title = 'API检查工具 - AI创意平台'
</script>

<style scoped>
.api-check-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #409EFF;
}

.page-description {
  font-size: 16px;
  color: #606266;
  margin: 0;
  line-height: 1.6;
}

.feature-cards {
  margin-bottom: 30px;
}

.feature-card {
  height: 100%;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-content {
  text-align: center;
  padding: 20px 10px;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.feature-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.feature-content p {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.usage-card {
  margin-top: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.usage-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.usage-card h3 {
  margin: 0;
  font-size: 20px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-steps {
  margin: 30px 0;
}

.usage-tips {
  margin-top: 30px;
}

.usage-tips h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0 12px 0;
}

.usage-tips ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.usage-tips li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

.usage-tips strong {
  color: #303133;
}

.alert-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.alert-list li {
  margin-bottom: 6px;
  color: #606266;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .api-check-page {
    padding: 10px;
  }
  
  .page-title {
    font-size: 24px;
    flex-direction: column;
    gap: 8px;
  }
  
  .title-icon {
    font-size: 28px;
  }
  
  .feature-cards {
    margin-bottom: 20px;
  }
  
  .usage-steps {
    margin: 20px 0;
  }
  
  .usage-steps :deep(.el-step__description) {
    font-size: 12px;
  }
}
</style>
