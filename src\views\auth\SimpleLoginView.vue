<template>
  <div class="simple-login">
    <div class="login-container">
      <h2>简单登录测试</h2>
      <form @submit.prevent="handleSubmit">
        <div class="form-group">
          <label>用户名:</label>
          <input 
            v-model="form.username" 
            type="text" 
            placeholder="请输入用户名"
            required
          />
        </div>
        <div class="form-group">
          <label>密码:</label>
          <input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码"
            required
          />
        </div>
        <button type="submit" :disabled="loading">
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>
      <div v-if="message" class="message" :class="messageType">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()

// 使用响应式引用来安全地初始化 userStore
const userStore = ref(null)

const loading = ref(false)
const message = ref('')
const messageType = ref('')

const form = reactive({
  username: '',
  password: ''
})

const handleSubmit = async () => {
  if (!form.username || !form.password) {
    message.value = '请输入用户名和密码'
    messageType.value = 'error'
    return
  }

  if (!userStore.value) {
    message.value = '用户系统未初始化，请刷新页面重试'
    messageType.value = 'error'
    return
  }

  loading.value = true
  message.value = ''

  try {
    console.log('开始登录:', form.username)

    const result = await userStore.value.login({
      username: form.username,
      password: form.password,
      rememberMe: false
    })

    console.log('登录结果:', result)

    if (result.success) {
      message.value = '登录成功！'
      messageType.value = 'success'

      setTimeout(() => {
        router.push('/home')
      }, 1000)
    } else {
      message.value = result.message || '登录失败'
      messageType.value = 'error'
    }
  } catch (error) {
    console.error('登录错误:', error)
    message.value = '登录失败，请稍后再试'
    messageType.value = 'error'
  } finally {
    loading.value = false
  }
}

// 在组件挂载后初始化 store
onMounted(async () => {
  try {
    const { useUserStore } = await import('@/stores')
    userStore.value = useUserStore()
    console.log('SimpleLogin: userStore 初始化成功')
  } catch (error) {
    console.error('SimpleLogin: userStore 初始化失败:', error)
  }
})
</script>

<style scoped>
.simple-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.login-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  width: 100%;
  padding: 0.75rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 4px;
  text-align: center;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style>
