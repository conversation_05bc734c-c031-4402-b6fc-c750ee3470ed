/**
 * Targon 集成测试工具
 * 验证 Targon API 在聊天界面中的集成是否正常工作
 */

import { getAIModels } from '@/config/aiModels.js'
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService.js'
import targonApi from '@/services/targonApi.js'

/**
 * 测试 Targon 模型是否正确加载到模型列表中
 */
export async function testTargonModelsInList() {
  console.log('🧪 测试 Targon 模型是否正确加载到模型列表中...')
  
  try {
    const models = await getAIModels()
    const targonModels = models.filter(model => model.category === 'targon')
    
    console.log(`📊 总模型数: ${models.length}`)
    console.log(`🎯 Targon 模型数: ${targonModels.length}`)
    
    if (targonModels.length > 0) {
      console.log('✅ Targon 模型已成功加载到模型列表')
      console.log('🔍 Targon 模型列表:')
      targonModels.forEach(model => {
        console.log(`  - ${model.id}: ${model.name} (${model.description})`)
      })
      
      return {
        success: true,
        totalModels: models.length,
        targonModels: targonModels.length,
        models: targonModels
      }
    } else {
      console.log('❌ 未找到 Targon 模型')
      return {
        success: false,
        message: '未找到 Targon 模型',
        totalModels: models.length,
        targonModels: 0
      }
    }
  } catch (error) {
    console.error('❌ 测试模型列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 测试通过统一聊天服务调用 Targon 模型
 */
export async function testTargonChatIntegration() {
  console.log('🧪 测试通过统一聊天服务调用 Targon 模型...')
  
  const testModel = 'deepseek-ai/DeepSeek-V3'
  const testMessages = [
    { role: 'user', content: '你好，请简单介绍一下自己。' }
  ]
  
  try {
    console.log(`🚀 使用模型 ${testModel} 进行聊天测试...`)
    
    const response = await generateChatCompletion(testMessages, {
      model: testModel,
      temperature: 0.7,
      max_tokens: 200
    })
    
    if (response && response.choices && response.choices[0]) {
      const aiReply = response.choices[0].message.content
      console.log('✅ 聊天测试成功')
      console.log('🤖 AI 回复:', aiReply.substring(0, 100) + '...')
      
      return {
        success: true,
        model: testModel,
        response: aiReply,
        message: '聊天集成测试成功'
      }
    } else {
      console.log('❌ 聊天测试失败: 响应格式不正确')
      return {
        success: false,
        message: '响应格式不正确',
        response
      }
    }
  } catch (error) {
    console.error('❌ 聊天集成测试失败:', error)
    return {
      success: false,
      error: error.message,
      model: testModel
    }
  }
}

/**
 * 测试流式聊天集成
 */
export async function testTargonStreamIntegration() {
  console.log('🧪 测试 Targon 流式聊天集成...')
  
  const testModel = 'deepseek-ai/DeepSeek-V3'
  const testMessages = [
    { role: 'user', content: '请用一句话介绍人工智能。' }
  ]
  
  try {
    console.log(`🌊 使用模型 ${testModel} 进行流式聊天测试...`)
    
    let streamContent = ''
    let chunkCount = 0
    
    await generateChatCompletionStream(
      testMessages,
      {
        model: testModel,
        temperature: 0.7,
        max_tokens: 100
      },
      (content) => {
        streamContent += content
        chunkCount++
        process.stdout.write(content) // 实时输出
      }
    )
    
    console.log('\n✅ 流式聊天测试成功')
    console.log(`📊 接收到 ${chunkCount} 个数据块`)
    console.log('📝 完整回复:', streamContent)
    
    return {
      success: true,
      model: testModel,
      content: streamContent,
      chunkCount,
      message: '流式聊天集成测试成功'
    }
  } catch (error) {
    console.error('\n❌ 流式聊天集成测试失败:', error)
    return {
      success: false,
      error: error.message,
      model: testModel
    }
  }
}

/**
 * 测试密钥池状态
 */
export async function testKeyPoolStatus() {
  console.log('🧪 测试密钥池状态...')
  
  try {
    const status = targonApi.getKeyPoolStatus()
    
    console.log('📊 密钥池状态:')
    console.log(`  - 总密钥数: ${status.totalKeys}`)
    console.log(`  - 健康密钥数: ${status.healthyKeys}`)
    console.log(`  - 当前密钥索引: ${status.currentKeyIndex}`)
    console.log(`  - 上次轮换时间: ${status.lastRotation}`)
    
    if (status.healthyKeys > 0) {
      console.log('✅ 密钥池状态正常')
      return {
        success: true,
        status,
        message: '密钥池状态正常'
      }
    } else {
      console.log('⚠️ 没有健康的密钥可用')
      return {
        success: false,
        status,
        message: '没有健康的密钥可用'
      }
    }
  } catch (error) {
    console.error('❌ 获取密钥池状态失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 运行完整的集成测试套件
 */
export async function runFullIntegrationTest() {
  console.log('🚀 开始运行 Targon 完整集成测试套件...\n')
  
  const results = {
    modelsList: null,
    chatIntegration: null,
    streamIntegration: null,
    keyPoolStatus: null,
    summary: null,
    timestamp: new Date().toISOString()
  }
  
  // 1. 测试模型列表
  console.log('=== 1. 模型列表测试 ===')
  results.modelsList = await testTargonModelsInList()
  console.log('')
  
  // 2. 测试密钥池状态
  console.log('=== 2. 密钥池状态测试 ===')
  results.keyPoolStatus = await testKeyPoolStatus()
  console.log('')
  
  // 3. 测试聊天集成
  console.log('=== 3. 聊天集成测试 ===')
  results.chatIntegration = await testTargonChatIntegration()
  console.log('')
  
  // 4. 测试流式聊天集成
  console.log('=== 4. 流式聊天集成测试 ===')
  results.streamIntegration = await testTargonStreamIntegration()
  console.log('')
  
  // 生成测试摘要
  const passedTests = [
    results.modelsList?.success,
    results.keyPoolStatus?.success,
    results.chatIntegration?.success,
    results.streamIntegration?.success
  ].filter(Boolean).length
  
  results.summary = {
    totalTests: 4,
    passedTests,
    failedTests: 4 - passedTests,
    overallSuccess: passedTests === 4,
    successRate: `${Math.round((passedTests / 4) * 100)}%`
  }
  
  console.log('=== 📋 测试摘要 ===')
  console.log(`总测试数: ${results.summary.totalTests}`)
  console.log(`通过测试: ${results.summary.passedTests}`)
  console.log(`失败测试: ${results.summary.failedTests}`)
  console.log(`成功率: ${results.summary.successRate}`)
  
  if (results.summary.overallSuccess) {
    console.log('🎉 所有测试通过！Targon 集成成功！')
  } else {
    console.log('⚠️ 部分测试失败，请检查详细结果')
  }
  
  return results
}

/**
 * 快速验证 Targon 集成状态
 */
export async function quickIntegrationCheck() {
  console.log('⚡ 快速检查 Targon 集成状态...')
  
  try {
    // 检查模型列表
    const models = await getAIModels()
    const targonModels = models.filter(model => model.category === 'targon')
    
    // 检查密钥池
    const keyPoolStatus = targonApi.getKeyPoolStatus()
    
    const isIntegrated = targonModels.length > 0 && keyPoolStatus.healthyKeys > 0
    
    if (isIntegrated) {
      console.log('✅ Targon 集成状态正常')
      console.log(`📊 可用模型: ${targonModels.length} 个`)
      console.log(`🔑 健康密钥: ${keyPoolStatus.healthyKeys} 个`)
      
      return {
        success: true,
        integrated: true,
        targonModels: targonModels.length,
        healthyKeys: keyPoolStatus.healthyKeys
      }
    } else {
      console.log('⚠️ Targon 集成存在问题')
      return {
        success: false,
        integrated: false,
        targonModels: targonModels.length,
        healthyKeys: keyPoolStatus.healthyKeys,
        message: 'Targon 集成存在问题'
      }
    }
  } catch (error) {
    console.error('❌ 检查集成状态失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 导出便捷的测试函数
export default {
  testModels: testTargonModelsInList,
  testChat: testTargonChatIntegration,
  testStream: testTargonStreamIntegration,
  testKeyPool: testKeyPoolStatus,
  runFullTest: runFullIntegrationTest,
  quickCheck: quickIntegrationCheck
}
