/**
 * Targon API 集成测试工具
 * 用于测试 Targon API 的各种功能
 */

import targonApi from '@/services/targonApi'

/**
 * 测试 API 连接
 */
export async function testConnection(config) {
  try {
    console.log('🔗 测试 Targon API 连接...')
    
    // 设置配置
    targonApi.setConfig(config)
    
    // 测试连接
    const response = await targonApi.testConnection()
    
    if (response.success) {
      console.log('✅ Targon API 连接测试成功')
      return {
        success: true,
        message: 'API 连接正常',
        data: response.data
      }
    } else {
      console.log('❌ Targon API 连接测试失败:', response.error)
      return {
        success: false,
        message: response.error || 'API 连接失败',
        error: response.error
      }
    }
  } catch (error) {
    console.error('❌ Targon API 连接测试异常:', error)
    return {
      success: false,
      message: '连接测试异常: ' + error.message,
      error: error.message
    }
  }
}

/**
 * 测试文本生成
 */
export async function testTextGeneration(config, prompt) {
  try {
    console.log('📝 测试 Targon API 文本生成...')
    
    // 设置配置
    targonApi.setConfig(config)
    
    // 测试文本生成
    const response = await targonApi.generateText({
      prompt: prompt || '请写一首关于春天的诗',
      maxTokens: 200,
      temperature: 0.7
    })
    
    if (response.success) {
      console.log('✅ Targon API 文本生成测试成功')
      return {
        success: true,
        message: '文本生成成功',
        data: response.data
      }
    } else {
      console.log('❌ Targon API 文本生成测试失败:', response.error)
      return {
        success: false,
        message: response.error || '文本生成失败',
        error: response.error
      }
    }
  } catch (error) {
    console.error('❌ Targon API 文本生成测试异常:', error)
    return {
      success: false,
      message: '文本生成测试异常: ' + error.message,
      error: error.message
    }
  }
}

/**
 * 测试图像生成
 */
export async function testImageGeneration(config, prompt) {
  try {
    console.log('🎨 测试 Targon API 图像生成...')
    
    // 设置配置
    targonApi.setConfig(config)
    
    // 测试图像生成
    const response = await targonApi.generateImage({
      prompt: prompt || '一只可爱的小猫在花园里玩耍',
      size: '512x512',
      quality: 'standard'
    })
    
    if (response.success) {
      console.log('✅ Targon API 图像生成测试成功')
      return {
        success: true,
        message: '图像生成成功',
        data: response.data
      }
    } else {
      console.log('❌ Targon API 图像生成测试失败:', response.error)
      return {
        success: false,
        message: response.error || '图像生成失败',
        error: response.error
      }
    }
  } catch (error) {
    console.error('❌ Targon API 图像生成测试异常:', error)
    return {
      success: false,
      message: '图像生成测试异常: ' + error.message,
      error: error.message
    }
  }
}

/**
 * 综合集成测试
 */
export async function runIntegrationTest(config) {
  const results = {
    connection: null,
    textGeneration: null,
    imageGeneration: null,
    overall: false
  }
  
  try {
    console.log('🚀 开始 Targon API 综合集成测试...')
    
    // 测试连接
    results.connection = await testConnection(config)
    
    if (results.connection.success) {
      // 测试文本生成
      results.textGeneration = await testTextGeneration(config, '测试文本生成功能')
      
      // 测试图像生成
      results.imageGeneration = await testImageGeneration(config, '测试图像生成功能')
    }
    
    // 判断整体测试结果
    results.overall = results.connection.success && 
                     (results.textGeneration?.success || results.imageGeneration?.success)
    
    console.log('📊 Targon API 综合集成测试完成:', results)
    return results
    
  } catch (error) {
    console.error('❌ Targon API 综合集成测试异常:', error)
    results.overall = false
    return results
  }
}

// 默认导出
export default {
  testConnection,
  testTextGeneration,
  testImageGeneration,
  runIntegrationTest
}
