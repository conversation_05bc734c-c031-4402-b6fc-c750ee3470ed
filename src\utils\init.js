/**
 * 应用初始化工具函数
 */

// 检查浏览器兼容性
export function checkBrowserCompatibility() {
  const checks = {
    localStorage: typeof Storage !== 'undefined',
    sessionStorage: typeof sessionStorage !== 'undefined',
    fetch: typeof fetch !== 'undefined',
    Promise: typeof Promise !== 'undefined',
    ES6: (() => {
      try {
        new Function('(a = 0) => a')
        return true
      } catch {
        return false
      }
    })(),
    WebGL: (() => {
      try {
        const canvas = document.createElement('canvas')
        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      } catch {
        return false
      }
    })()
  }

  const incompatible = Object.entries(checks)
    .filter(([, supported]) => !supported)
    .map(([feature]) => feature)

  return {
    compatible: incompatible.length === 0,
    incompatible,
    checks
  }
}

// 检查必要的环境变量
export function checkEnvironment() {
  const requiredEnvVars = [
    'VITE_APP_TITLE',
    'VITE_API_BASE_URL'
  ]

  const missing = requiredEnvVars.filter(varName => !import.meta.env[varName])

  return {
    valid: missing.length === 0,
    missing,
    env: import.meta.env
  }
}

// 初始化性能监控
export function initPerformanceMonitoring() {
  if (typeof performance !== 'undefined' && performance.mark) {
    performance.mark('app-init-start')
    
    return {
      markEnd: () => {
        performance.mark('app-init-end')
        performance.measure('app-init', 'app-init-start', 'app-init-end')
        
        const measure = performance.getEntriesByName('app-init')[0]
        console.log(`🚀 应用初始化耗时: ${measure.duration.toFixed(2)}ms`)
        
        return measure.duration
      }
    }
  }
  
  return { markEnd: () => 0 }
}

// 检查网络连接
export function checkNetworkStatus() {
  return {
    online: navigator.onLine,
    connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
    effectiveType: navigator.connection?.effectiveType || 'unknown'
  }
}

// 初始化错误收集
export function initErrorCollection() {
  const errors = []
  
  const collectError = (error, source = 'unknown') => {
    const errorInfo = {
      message: error.message || String(error),
      stack: error.stack,
      source,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }
    
    errors.push(errorInfo)
    console.error(`[${source}] 错误收集:`, errorInfo)
    
    // 限制错误数量
    if (errors.length > 100) {
      errors.splice(0, 50)
    }
  }
  
  // 监听全局错误
  window.addEventListener('error', (event) => {
    collectError(event.error || new Error(event.message), 'global')
  })
  
  // 监听未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    collectError(event.reason, 'promise')
  })
  
  return {
    getErrors: () => [...errors],
    clearErrors: () => errors.splice(0),
    collectError
  }
}

// 系统诊断
export function runSystemDiagnostics() {
  console.log('🔍 运行系统诊断...')
  
  const diagnostics = {
    browser: checkBrowserCompatibility(),
    environment: checkEnvironment(),
    network: checkNetworkStatus(),
    memory: {
      used: performance.memory?.usedJSHeapSize || 0,
      total: performance.memory?.totalJSHeapSize || 0,
      limit: performance.memory?.jsHeapSizeLimit || 0
    },
    screen: {
      width: window.screen.width,
      height: window.screen.height,
      colorDepth: window.screen.colorDepth,
      pixelRatio: window.devicePixelRatio
    },
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    timestamp: new Date().toISOString()
  }
  
  console.log('📊 系统诊断结果:', diagnostics)
  
  return diagnostics
}

// 显示兼容性警告
export function showCompatibilityWarning(incompatible) {
  const warningDiv = document.createElement('div')
  warningDiv.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      color: white;
      font-family: Arial, sans-serif;
    ">
      <div style="
        background: #1f2937;
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        text-align: center;
      ">
        <h2 style="color: #ef4444; margin-bottom: 1rem;">浏览器兼容性警告</h2>
        <p style="margin-bottom: 1rem;">您的浏览器不支持以下功能，可能影响应用正常使用：</p>
        <ul style="text-align: left; margin-bottom: 1.5rem;">
          ${incompatible.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        <p style="margin-bottom: 1.5rem;">建议升级到最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。</p>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #3b82f6;
          color: white;
          border: none;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          cursor: pointer;
        ">继续使用</button>
      </div>
    </div>
  `
  document.body.appendChild(warningDiv)
}

// 完整的初始化检查
export async function performInitializationCheck() {
  console.log('🔧 开始初始化检查...')
  
  const performance = initPerformanceMonitoring()
  const errorCollection = initErrorCollection()
  
  try {
    // 运行系统诊断
    const diagnostics = runSystemDiagnostics()
    
    // 检查浏览器兼容性
    if (!diagnostics.browser.compatible) {
      console.warn('⚠️ 浏览器兼容性问题:', diagnostics.browser.incompatible)
      showCompatibilityWarning(diagnostics.browser.incompatible)
    }
    
    // 检查环境变量
    if (!diagnostics.environment.valid) {
      console.warn('⚠️ 环境变量缺失:', diagnostics.environment.missing)
    }
    
    // 检查网络状态
    if (!diagnostics.network.online) {
      console.warn('⚠️ 网络连接异常')
    }
    
    const duration = performance.markEnd()
    
    console.log('✅ 初始化检查完成')
    
    return {
      success: true,
      diagnostics,
      duration,
      errorCollection
    }
  } catch (error) {
    console.error('❌ 初始化检查失败:', error)
    errorCollection.collectError(error, 'init-check')
    
    return {
      success: false,
      error,
      errorCollection
    }
  }
}
