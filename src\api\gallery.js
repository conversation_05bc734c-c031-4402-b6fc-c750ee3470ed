// 作品展示相关API
import { request } from '@/utils/request'

/**
 * 获取作品列表
 */
export function getArtworksApi(params) {
  return request.get('/gallery/artworks', params)
}

/**
 * 获取作品详情
 */
export function getArtworkDetailApi(id) {
  return request.get(`/gallery/artworks/${id}`)
}

/**
 * 发布作品
 */
export function publishArtworkApi(data) {
  return request.post('/gallery/artworks', data)
}

/**
 * 更新作品信息
 */
export function updateArtworkApi(id, data) {
  return request.put(`/gallery/artworks/${id}`, data)
}

/**
 * 删除作品
 */
export function deleteArtworkApi(id) {
  return request.delete(`/gallery/artworks/${id}`)
}

/**
 * 点赞/取消点赞作品
 */
export function likeArtworkApi(id) {
  return request.post(`/gallery/artworks/${id}/like`)
}

/**
 * 收藏/取消收藏作品
 */
export function favoriteArtworkApi(id) {
  return request.post(`/gallery/artworks/${id}/favorite`)
}

/**
 * 分享作品
 */
export function shareArtworkApi(id, data) {
  return request.post(`/gallery/artworks/${id}/share`, data)
}

/**
 * 下载作品
 */
export function downloadArtworkApi(id) {
  return request.download(`/gallery/artworks/${id}/download`)
}

/**
 * 获取作品评论
 */
export function getArtworkCommentsApi(artworkId, params) {
  return request.get(`/gallery/artworks/${artworkId}/comments`, params)
}

/**
 * 添加作品评论
 */
export function addArtworkCommentApi(artworkId, data) {
  return request.post(`/gallery/artworks/${artworkId}/comments`, data)
}

/**
 * 更新评论
 */
export function updateCommentApi(commentId, data) {
  return request.put(`/gallery/comments/${commentId}`, data)
}

/**
 * 删除评论
 */
export function deleteCommentApi(commentId) {
  return request.delete(`/gallery/comments/${commentId}`)
}

/**
 * 点赞/取消点赞评论
 */
export function likeCommentApi(commentId) {
  return request.post(`/gallery/comments/${commentId}/like`)
}

/**
 * 回复评论
 */
export function replyCommentApi(commentId, data) {
  return request.post(`/gallery/comments/${commentId}/reply`, data)
}

/**
 * 获取热门作品
 */
export function getPopularArtworksApi(params) {
  return request.get('/gallery/popular', params)
}

/**
 * 获取最新作品
 */
export function getLatestArtworksApi(params) {
  return request.get('/gallery/latest', params)
}

/**
 * 获取推荐作品
 */
export function getRecommendedArtworksApi(params) {
  return request.get('/gallery/recommended', params)
}

/**
 * 获取关注用户的作品
 */
export function getFollowingArtworksApi(params) {
  return request.get('/gallery/following', params)
}

/**
 * 搜索作品
 */
export function searchArtworksApi(params) {
  return request.get('/gallery/search', params)
}

/**
 * 获取作品分类
 */
export function getArtworkCategoriesApi() {
  return request.get('/gallery/categories')
}

/**
 * 获取作品标签
 */
export function getArtworkTagsApi(params) {
  return request.get('/gallery/tags', params)
}

/**
 * 创建作品集
 */
export function createCollectionApi(data) {
  return request.post('/gallery/collections', data)
}

/**
 * 获取作品集列表
 */
export function getCollectionsApi(params) {
  return request.get('/gallery/collections', params)
}

/**
 * 获取作品集详情
 */
export function getCollectionDetailApi(id) {
  return request.get(`/gallery/collections/${id}`)
}

/**
 * 更新作品集
 */
export function updateCollectionApi(id, data) {
  return request.put(`/gallery/collections/${id}`, data)
}

/**
 * 删除作品集
 */
export function deleteCollectionApi(id) {
  return request.delete(`/gallery/collections/${id}`)
}

/**
 * 添加作品到作品集
 */
export function addToCollectionApi(collectionId, artworkId) {
  return request.post(`/gallery/collections/${collectionId}/artworks/${artworkId}`)
}

/**
 * 从作品集移除作品
 */
export function removeFromCollectionApi(collectionId, artworkId) {
  return request.delete(`/gallery/collections/${collectionId}/artworks/${artworkId}`)
}

/**
 * 关注作品集
 */
export function followCollectionApi(id) {
  return request.post(`/gallery/collections/${id}/follow`)
}

/**
 * 取消关注作品集
 */
export function unfollowCollectionApi(id) {
  return request.delete(`/gallery/collections/${id}/follow`)
}

/**
 * 获取用户作品
 */
export function getUserArtworksApi(userId, params) {
  return request.get(`/gallery/users/${userId}/artworks`, params)
}

/**
 * 获取用户收藏
 */
export function getUserFavoritesApi(userId, params) {
  return request.get(`/gallery/users/${userId}/favorites`, params)
}

/**
 * 获取用户作品集
 */
export function getUserCollectionsApi(userId, params) {
  return request.get(`/gallery/users/${userId}/collections`, params)
}

/**
 * 举报作品
 */
export function reportArtworkApi(data) {
  return request.post('/gallery/report', data)
}

/**
 * 获取作品统计
 */
export function getArtworkStatsApi(artworkId) {
  return request.get(`/gallery/artworks/${artworkId}/stats`)
}

/**
 * 获取画廊统计
 */
export function getGalleryStatsApi() {
  return request.get('/gallery/stats')
}

/**
 * 获取作品浏览历史
 */
export function getViewHistoryApi(params) {
  return request.get('/gallery/view-history', params)
}

/**
 * 记录作品浏览
 */
export function recordViewApi(artworkId) {
  return request.post(`/gallery/artworks/${artworkId}/view`)
}

/**
 * 获取相似作品
 */
export function getSimilarArtworksApi(artworkId, params) {
  return request.get(`/gallery/artworks/${artworkId}/similar`, params)
}

/**
 * 获取作品创作过程
 */
export function getArtworkProcessApi(artworkId) {
  return request.get(`/gallery/artworks/${artworkId}/process`)
}

/**
 * 上传作品文件
 */
export function uploadArtworkFileApi(formData) {
  return request.upload('/gallery/upload', formData)
}

/**
 * 批量上传作品
 */
export function batchUploadArtworksApi(formData) {
  return request.upload('/gallery/batch-upload', formData)
}

/**
 * 获取上传进度
 */
export function getUploadProgressApi(uploadId) {
  return request.get(`/gallery/upload-progress/${uploadId}`)
}

/**
 * 取消上传
 */
export function cancelUploadApi(uploadId) {
  return request.post(`/gallery/cancel-upload/${uploadId}`)
}
