<template>
  <div class="model-selector-demo">
    <div class="demo-header">
      <h1>🤖 模型选择器演示</h1>
      <p>体验全新的可折叠分类标签和增强的模型卡片</p>
    </div>

    <div class="demo-content">
      <div class="demo-section">
        <h2>✨ 新功能展示</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🏷️</div>
            <h3>可折叠分类</h3>
            <p>AI分类标签支持折叠，节省空间，提升体验</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>增强卡片</h3>
            <p>模型卡片采用全新设计，视觉效果更佳</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>默认模型</h3>
            <p>Gemini 2.5 Flash 现为默认推荐模型</p>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h2>🎯 模型选择器</h2>
        <div class="selector-container">
          <ModelSelector
            v-model="selectedModel"
            @model-changed="handleModelChange"
          />
        </div>
      </div>

      <div class="demo-section" v-if="currentModel">
        <h2>📊 当前选择</h2>
        <div class="current-model-info">
          <div class="model-details">
            <h3>{{ currentModel.name }}</h3>
            <p>{{ currentModel.description }}</p>
            <div class="model-meta">
              <span class="meta-item">分类: {{ currentModel.category }}</span>
              <span class="meta-item">状态: {{ currentModel.status }}</span>
              <span class="meta-item" v-if="currentModel.isFree">免费使用</span>
              <span class="meta-item" v-if="currentModel.isPremium">高级模型</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ModelSelector from '@/components/chat/ModelSelector.vue'
import { getAIModels } from '@/config/aiModels.js'

// 响应式数据
const selectedModel = ref('gemini-2.5-flash')
const aiModels = ref([])

// 计算属性
const currentModel = computed(() => {
  return aiModels.value.find(model => model.id === selectedModel.value)
})

// 方法
const handleModelChange = (model) => {
  console.log('模型已切换:', model)
  ElMessage.success(`已切换到 ${model.name}`)
}

// 加载模型数据
const loadModels = async () => {
  try {
    const models = await getAIModels()
    aiModels.value = models
  } catch (error) {
    console.error('加载模型失败:', error)
  }
}

// 初始化
loadModels()
</script>

<style lang="scss" scoped>
.model-selector-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  p {
    font-size: 1.125rem;
    color: #6b7280;
  }
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.demo-section {
  h2 {
    font-size: 1.5rem;
    color: #374151;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }
  
  .feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    line-height: 1.6;
  }
}

.selector-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.current-model-info {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.model-details {
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.6;
  }
}

.model-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.meta-item {
  background: white;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .model-selector-demo {
    padding: 1rem;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .selector-container {
    padding: 1rem;
  }
}
</style>
