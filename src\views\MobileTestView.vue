<template>
  <div class="mobile-test-view">
    <div class="page-header">
      <h1>移动端样式测试</h1>
      <p>测试所有组件在移动端的显示效果</p>
    </div>

    <!-- 按钮测试 -->
    <div class="test-section">
      <h2>按钮测试</h2>
      <div class="button-grid">
        <button class="btn btn-primary">主要按钮</button>
        <button class="btn btn-info">信息按钮</button>
        <button class="btn btn-warning">警告按钮</button>
        <button class="btn btn-danger">危险按钮</button>
        <button class="btn btn-small">小按钮</button>
        <button class="btn btn-large">大按钮</button>
      </div>
    </div>

    <!-- 表单测试 */
    <div class="test-section">
      <h2>表单测试</h2>
      <div class="form-container">
        <div class="form-group">
          <label>文本输入</label>
          <input type="text" placeholder="请输入文本" class="form-input">
        </div>
        <div class="form-group">
          <label>选择器</label>
          <select class="form-select">
            <option>选项1</option>
            <option>选项2</option>
            <option>选项3</option>
          </select>
        </div>
        <div class="form-group">
          <label>文本域</label>
          <textarea placeholder="请输入多行文本" class="form-textarea" rows="3"></textarea>
        </div>
      </div>
    </div>

    <!-- 卡片网格测试 */
    <div class="test-section">
      <h2>卡片网格测试</h2>
      <div class="card-grid">
        <div class="test-card" v-for="i in 6" :key="i">
          <div class="card-header">
            <h3>卡片标题 {{ i }}</h3>
          </div>
          <div class="card-content">
            <p>这是卡片内容，用于测试移动端的显示效果。</p>
          </div>
          <div class="card-actions">
            <button class="btn btn-small btn-primary">操作</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格测试 -->
    <div class="test-section">
      <h2>表格测试</h2>
      <div class="table-container">
        <table class="test-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>名称</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="i in 5" :key="i">
              <td>{{ i }}</td>
              <td>项目 {{ i }}</td>
              <td>
                <span class="status-badge" :class="i % 2 === 0 ? 'success' : 'warning'">
                  {{ i % 2 === 0 ? '正常' : '警告' }}
                </span>
              </td>
              <td>
                <button class="btn btn-small btn-info">查看</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 统计卡片测试 */
    <div class="test-section">
      <h2>统计卡片测试</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-number">1,234</div>
            <div class="stat-label">总数据</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <div class="stat-number">567</div>
            <div class="stat-label">用户数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🔑</div>
          <div class="stat-content">
            <div class="stat-number">89</div>
            <div class="stat-label">API密钥</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-number">95%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 响应式测试信息 -->
    <div class="test-section">
      <h2>响应式信息</h2>
      <div class="responsive-info">
        <div class="info-item">
          <strong>屏幕宽度:</strong> <span id="screen-width">-</span>px
        </div>
        <div class="info-item">
          <strong>屏幕高度:</strong> <span id="screen-height">-</span>px
        </div>
        <div class="info-item">
          <strong>设备像素比:</strong> <span id="device-ratio">-</span>
        </div>
        <div class="info-item">
          <strong>用户代理:</strong> <span id="user-agent">-</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

// 更新屏幕信息
const updateScreenInfo = () => {
  document.getElementById('screen-width').textContent = window.innerWidth
  document.getElementById('screen-height').textContent = window.innerHeight
  document.getElementById('device-ratio').textContent = window.devicePixelRatio
  document.getElementById('user-agent').textContent = navigator.userAgent
}

onMounted(() => {
  updateScreenInfo()
  window.addEventListener('resize', updateScreenInfo)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenInfo)
})
</script>

<style lang="scss" scoped>
.mobile-test-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
  
  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f1f3f4;
  }
}

/* 按钮样式 */
.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  min-height: 44px;
  
  &.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 36px;
  }
  
  &.btn-large {
    padding: 14px 28px;
    font-size: 16px;
    min-height: 52px;
  }
  
  &.btn-primary {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
    
    &:hover {
      background-color: #66b1ff;
    }
  }
  
  &.btn-info {
    background-color: #909399;
    border-color: #909399;
    color: white;
    
    &:hover {
      background-color: #a6a9ad;
    }
  }
  
  &.btn-warning {
    background-color: #e6a23c;
    border-color: #e6a23c;
    color: white;
    
    &:hover {
      background-color: #ebb563;
    }
  }
  
  &.btn-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: white;
    
    &:hover {
      background-color: #f78989;
    }
  }
}

/* 表单样式 */
.form-container {
  display: grid;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  
  label {
    font-weight: 500;
    color: #2c3e50;
  }
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px; /* 防止iOS缩放 */
  min-height: 44px;
  
  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.test-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  
  .card-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e8ed;
    
    h3 {
      margin: 0;
      font-size: 1rem;
      color: #2c3e50;
    }
  }
  
  .card-content {
    padding: 1rem;
    
    p {
      margin: 0;
      color: #7f8c8d;
      line-height: 1.5;
    }
  }
  
  .card-actions {
    padding: 1rem;
    border-top: 1px solid #e1e8ed;
    background: #f8f9fa;
  }
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.test-table {
  width: 100%;
  border-collapse: collapse;
  
  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
  }
  
  td {
    color: #7f8c8d;
  }
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.success {
    background: #e8f5e8;
    color: #388e3c;
  }
  
  &.warning {
    background: #fff3cd;
    color: #f57c00;
  }
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e1e8ed;
  display: flex;
  align-items: center;
  gap: 1rem;
  
  .stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .stat-content {
    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 0.25rem;
    }
    
    .stat-label {
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

/* 响应式信息 */
.responsive-info {
  display: grid;
  gap: 0.5rem;
}

.info-item {
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.9rem;
  
  strong {
    color: #2c3e50;
  }
  
  span {
    color: #7f8c8d;
    word-break: break-all;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .mobile-test-view {
    padding: 1rem;
  }
  
  .button-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .test-table {
    font-size: 0.875rem;
  }
  
  .test-table th,
  .test-table td {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .mobile-test-view {
    padding: 0.5rem;
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
  
  .test-section {
    padding: 1rem;
  }
  
  .test-section h2 {
    font-size: 1.125rem;
  }
}
</style>
