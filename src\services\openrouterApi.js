// OpenRouter AI API 服务 - 基于通用OpenAI兼容API
import { ElMessage } from 'element-plus'
import { BaseOpenAIApi, OpenAICompatibleError } from './BaseOpenAIApi.js'
import { apiKeyManager } from './apiKeyManager.js'
import { shouldUseMockData, getMockApiKeyManager } from '@/config/devConfig.js'

// OpenRouter API配置
const OPENROUTER_CONFIG = {
  baseUrl: import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
  timeout: parseInt(import.meta.env.VITE_OPENROUTER_TIMEOUT) || 60000,
  maxRetries: parseInt(import.meta.env.VITE_OPENROUTER_MAX_RETRIES) || 3,
  retryDelay: parseInt(import.meta.env.VITE_OPENROUTER_RETRY_DELAY) || 1000,
  defaultModel: 'deepseek/deepseek-chat-v3-0324:free',
  // 默认API密钥
  defaultApiKey: '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'
}

// OpenRouter API类 - 继承通用OpenAI兼容API
class OpenRouterApi extends BaseOpenAIApi {
  constructor(config = {}) {
    // 合并默认配置
    const openrouterConfig = {
      ...OPENROUTER_CONFIG,
      apiKey: config.apiKey || OPENROUTER_CONFIG.defaultApiKey,
      headers: {
        'HTTP-Referer': window.location.origin,
        'X-Title': 'AI Creative Platform',
        ...config.headers
      },
      ...config
    }

    super(openrouterConfig)

    // OpenRouter特有的免费模型列表
    this.freeModels = [
      'deepseek/deepseek-chat-v3-0324:free',
      'deepseek/deepseek-r1-0528:free',
      'qwen/qwen3-coder:free',
      'deepseek/deepseek-r1:free',
      'qwen/qwen3-235b-a22b-2507:free',
      'tngtech/deepseek-r1t2-chimera:free',
      'moonshotai/kimi-k2:free',
      'tngtech/deepseek-r1t-chimera:free',
      'google/gemini-2.0-flash-exp:free',
      'qwen/qwq-32b:free',
      'qwen/qwen3-14b:free',
      'mistralai/mistral-nemo:free',
      'deepseek/deepseek-r1-0528-qwen3-8b:free',
      'microsoft/mai-ds-r1:free',
      'qwen/qwen3-235b-a22b:free',
      'mistralai/mistral-small-3.1-24b-instruct:free',
      'qwen/qwen2.5-vl-72b-instruct:free',
      'mistralai/mistral-small-3.2-24b-instruct:free',
      'moonshotai/kimi-dev-72b:free',
      'google/gemma-3-27b-it:free',
      'qwen/qwen3-30b-a3b:free',
      'qwen/qwen-2.5-coder-32b-instruct:free',
      'mistralai/devstral-small-2505:free',
      'mistralai/mistral-7b-instruct:free',
      'agentica-org/deepcoder-14b-preview:free',
      'deepseek/deepseek-r1-distill-llama-70b:free',
      'meta-llama/llama-3.3-70b-instruct:free',
      'qwen/qwen3-8b:free',
      'meta-llama/llama-3.2-11b-vision-instruct:free',
      'cognitivecomputations/dolphin-mistral-24b-venice-edition:free',
      'moonshotai/kimi-vl-a3b-thinking:free',
      'qwen/qwen-2.5-72b-instruct:free',
      'thudm/glm-z1-32b:free',
      'shisa-ai/shisa-v2-llama3.3-70b:free',
      'cognitivecomputations/dolphin3.0-mistral-24b:free',
      'qwen/qwen2.5-vl-32b-instruct:free',
      'tencent/hunyuan-a13b-instruct:free',
      'nousresearch/deephermes-3-llama-3-8b-preview:free',
      'google/gemma-3n-e2b-it:free',
      'thudm/glm-4-32b:free',
      'google/gemma-2-9b-it:free',
      'google/gemma-3-12b-it:free',
      'meta-llama/llama-3.1-405b-instruct:free',
      'cognitivecomputations/dolphin3.0-r1-mistral-24b:free',
      'mistralai/mistral-small-24b-instruct-2501:free',
      'arliai/qwq-32b-arliai-rpr-v1:free',
      'google/gemma-3n-e4b-it:free',
      'google/gemma-3-4b-it:free',
      'rekaai/reka-flash-3:free',
      'sarvamai/sarvam-m:free',
      'qwen/qwen3-4b:free',
      'featherless/qwerky-72b:free',
      'meta-llama/llama-3.2-3b-instruct:free',
      'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
      'deepseek/deepseek-r1-distill-qwen-14b:free'
    ]
  }

  // 获取当前API密钥 - 支持密钥管理器
  getCurrentApiKey() {
    // 如果配置了自定义密钥，使用自定义密钥
    if (this.config.apiKey && this.config.apiKey !== OPENROUTER_CONFIG.defaultApiKey) {
      return this.config.apiKey
    }

    // 否则使用密钥管理器
    try {
      return apiKeyManager.getCurrentKey()
    } catch (error) {
      console.warn('密钥管理器获取失败，使用默认密钥:', error)
      return this.config.apiKey || OPENROUTER_CONFIG.defaultApiKey
    }
  }

  // 获取可用模型列表 - 支持过滤免费模型
  async getAvailableModels(forceRefresh = false, onlyFree = true) {
    try {
      const allModels = await super.getAvailableModels(forceRefresh)

      if (onlyFree) {
        // 过滤出免费模型
        const freeModels = allModels.filter(model =>
          this.freeModels.includes(model.id)
        )

        return freeModels.map(model => ({
          ...model,
          isFree: true,
          provider: this.getModelProvider(model.id)
        }))
      }

      return allModels.map(model => ({
        ...model,
        isFree: this.freeModels.includes(model.id),
        provider: this.getModelProvider(model.id)
      }))
    } catch (error) {
      console.error('获取OpenRouter模型列表失败:', error)
      throw error
    }
  }

  // 获取模型提供商信息
  getModelProvider(modelId) {
    const parts = modelId.split('/')
    const provider = parts[0]

    const providerMap = {
      'deepseek': 'DeepSeek',
      'qwen': 'Alibaba',
      'moonshotai': 'Moonshot AI',
      'google': 'Google',
      'mistralai': 'Mistral AI',
      'microsoft': 'Microsoft',
      'meta-llama': 'Meta',
      'tngtech': 'TNG Technology',
      'agentica-org': 'Agentica',
      'cognitivecomputations': 'Cognitive Computations',
      'thudm': 'Tsinghua University',
      'shisa-ai': 'Shisa AI',
      'tencent': 'Tencent',
      'nousresearch': 'Nous Research',
      'arliai': 'Arli AI',
      'rekaai': 'Reka AI',
      'sarvamai': 'Sarvam AI',
      'featherless': 'Featherless',
      'nvidia': 'NVIDIA'
    }

    return providerMap[provider] || provider
  }

  // 获取模型信息
  getModelInfo(modelId) {
    const parts = modelId.split('/')
    const provider = parts[0]
    const modelName = parts[1]?.replace(':free', '') || modelId

    return {
      name: modelName,
      provider: this.getModelProvider(modelId),
      capabilities: ['text'],
      isFree: this.freeModels.includes(modelId)
    }
  }
}

// 创建OpenRouter API实例
const openrouterApi = new OpenRouterApi()

// 动态配置API的功能
export const apiConfigManager = {
  // 设置自定义URL和API密钥
  setCustomConfig(url, apiKey) {
    openrouterApi.setBaseUrl(url)
    openrouterApi.setApiKey(apiKey)
    console.log(`已设置自定义配置: URL=${url}, Key=${apiKey.substring(0, 10)}...`)
  },

  // 重置为默认配置
  resetToDefault() {
    openrouterApi.setBaseUrl(OPENROUTER_CONFIG.baseUrl)
    openrouterApi.setApiKey(OPENROUTER_CONFIG.defaultApiKey)
    console.log('已重置为默认配置')
  },

  // 获取当前配置
  getCurrentConfig() {
    return {
      baseUrl: openrouterApi.config.baseUrl,
      apiKey: openrouterApi.getCurrentApiKey(),
      defaultModel: openrouterApi.config.defaultModel
    }
  },

  // 测试当前配置
  async testCurrentConfig() {
    try {
      const result = await openrouterApi.testConnection()
      if (result.success) {
        ElMessage.success(`连接成功！找到 ${result.modelCount} 个模型`)
        return result
      } else {
        ElMessage.error(`连接失败: ${result.error}`)
        return result
      }
    } catch (error) {
      ElMessage.error(`测试失败: ${error.message}`)
      return { success: false, error: error.message }
    }
  },

  // 自动查询并获取所有模型
  async queryAllModels(url, apiKey, onlyFree = false) {
    try {
      // 临时设置配置
      const originalConfig = this.getCurrentConfig()
      this.setCustomConfig(url, apiKey)

      // 查询模型
      const models = await openrouterApi.getAvailableModels(true, onlyFree)

      ElMessage.success(`成功查询到 ${models.length} 个${onlyFree ? '免费' : ''}模型`)

      return {
        success: true,
        models,
        totalCount: models.length,
        freeCount: models.filter(m => m.isFree).length
      }
    } catch (error) {
      ElMessage.error(`查询模型失败: ${error.message}`)
      return {
        success: false,
        error: error.message,
        models: []
      }
    }
  }
}

// 导出的API函数 - 使用新的API实例

// 获取可用模型列表
export async function getAvailableModels(onlyFree = true) {
  return await openrouterApi.getAvailableModels(false, onlyFree)
}

// 生成聊天完成
export async function generateChatCompletion(messages, options = {}) {
  return await openrouterApi.generateChatCompletion(messages, options)
}

// 流式生成聊天完成
export async function generateChatCompletionStream(messages, options = {}, onChunk) {
  return await openrouterApi.generateChatCompletionStream(messages, options, onChunk)
}

// 获取模型信息
export function getModelInfo(modelId) {
  return openrouterApi.getModelInfo(modelId)
}

// 错误处理
export function handleOpenRouterError(error) {
  openrouterApi.handleError(error)
}

// API密钥管理相关函数
export function getApiKeyStats() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().getKeyStats()
  }
  return apiKeyManager.getKeyStats()
}

export function getApiConfig() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().getConfig()
  }
  return apiKeyManager.getConfig()
}

export function addApiKey(apiKey) {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().addKey(apiKey)
  }
  return apiKeyManager.addKey(apiKey)
}

export function removeApiKey(index) {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().removeKey(index)
  }
  return apiKeyManager.removeKey(index)
}

export function rotateApiKey() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().rotateKey()
  }
  return apiKeyManager.rotateKey()
}

export function performHealthCheck() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().performHealthCheck()
  }
  return apiKeyManager.performHealthCheck()
}

export function resetAllApiKeys() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().resetAllKeys()
  }
  return apiKeyManager.resetAllKeys()
}

// 增强的管理功能
export function testApiKey(index) {
  if (shouldUseMockData()) {
    // 模拟测试
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: Math.random() > 0.2, // 80% 成功率
          responseTime: Math.floor(Math.random() * 500) + 100,
          message: '测试完成'
        })
      }, 1000)
    })
  }

  // 实际测试逻辑
  return apiKeyManager.testKey(index)
}

export function switchToApiKey(index) {
  if (shouldUseMockData()) {
    const manager = getMockApiKeyManager()
    manager.currentKeyIndex = index
    manager.keyStats.forEach((key, i) => {
      key.isCurrent = i === index
    })
    return
  }

  return apiKeyManager.switchToKey(index)
}

export function getApiKeyDetails(index) {
  if (shouldUseMockData()) {
    const manager = getMockApiKeyManager()
    const keyStats = manager.keyStats[index]
    if (!keyStats) return null

    return {
      ...keyStats,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      lastHealthCheck: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
      totalDataTransferred: Math.floor(Math.random() * 1000000),
      averageTokensPerRequest: Math.floor(Math.random() * 1000) + 100
    }
  }

  return apiKeyManager.getKeyDetails(index)
}

export function updateApiKeyConfig(config) {
  if (shouldUseMockData()) {
    console.log('模拟更新API配置:', config)
    return Promise.resolve()
  }

  return apiKeyManager.updateConfig(config)
}

export function exportApiKeyStats() {
  const stats = getApiKeyStats()
  const config = getApiConfig()

  const exportData = {
    exportTime: new Date().toISOString(),
    systemConfig: config,
    keyStats: stats,
    summary: {
      totalKeys: stats.length,
      activeKeys: stats.filter(key => key.isActive).length,
      totalRequests: stats.reduce((sum, key) => sum + (key.totalRequests || 0), 0),
      totalSuccessfulRequests: stats.reduce((sum, key) => sum + (key.successfulRequests || 0), 0)
    }
  }

  return exportData
}

// 向后兼容的免费模型列表导出
export const OPENROUTER_FREE_MODELS = openrouterApi.freeModels

// 默认导出
export default {
  // 新的API配置管理器
  apiConfigManager,

  // 原有的API函数
  getAvailableModels,
  generateChatCompletion,
  generateChatCompletionStream,
  getModelInfo,
  handleOpenRouterError,

  // API密钥管理相关函数
  getApiKeyStats,
  getApiConfig,
  addApiKey,
  removeApiKey,
  rotateApiKey,
  performHealthCheck,
  resetAllApiKeys,
  testApiKey,
  switchToApiKey,
  getApiKeyDetails,
  updateApiKeyConfig,
  exportApiKeyStats,

  // 向后兼容
  OPENROUTER_FREE_MODELS,

  // 新增功能
  setCustomConfig: apiConfigManager.setCustomConfig.bind(apiConfigManager),
  resetToDefault: apiConfigManager.resetToDefault.bind(apiConfigManager),
  getCurrentConfig: apiConfigManager.getCurrentConfig.bind(apiConfigManager),
  testCurrentConfig: apiConfigManager.testCurrentConfig.bind(apiConfigManager),
  queryAllModels: apiConfigManager.queryAllModels.bind(apiConfigManager)
}
