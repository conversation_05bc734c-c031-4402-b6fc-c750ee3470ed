<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
          🔐 登录状态实时监控
        </h1>
        
        <!-- 实时状态显示 -->
        <div class="mb-8 p-6 bg-gray-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">实时登录状态</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-white rounded-lg shadow">
              <div class="text-2xl mb-2">
                {{ isLoggedIn ? '✅' : '❌' }}
              </div>
              <div class="font-medium">登录状态</div>
              <div class="text-sm text-gray-600">
                {{ isLoggedIn ? '已登录' : '未登录' }}
              </div>
            </div>
            
            <div class="text-center p-4 bg-white rounded-lg shadow">
              <div class="text-2xl mb-2">👤</div>
              <div class="font-medium">用户名</div>
              <div class="text-sm text-gray-600">
                {{ userName || '无' }}
              </div>
            </div>
            
            <div class="text-center p-4 bg-white rounded-lg shadow">
              <div class="text-2xl mb-2">🎭</div>
              <div class="font-medium">用户角色</div>
              <div class="text-sm text-gray-600">
                {{ userRole || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="mb-8 p-6 bg-blue-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">详细信息</h2>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="font-medium">Store Token:</span>
              <span :class="storeToken ? 'text-green-600' : 'text-red-600'">
                {{ storeToken ? '存在' : '不存在' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Store UserInfo:</span>
              <span :class="storeUserInfo ? 'text-green-600' : 'text-red-600'">
                {{ storeUserInfo ? '存在' : '不存在' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">LocalStorage Token:</span>
              <span :class="localToken ? 'text-green-600' : 'text-red-600'">
                {{ localToken ? '存在' : '不存在' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">LocalStorage UserInfo:</span>
              <span :class="localUserInfo ? 'text-green-600' : 'text-red-600'">
                {{ localUserInfo ? '存在' : '不存在' }}
              </span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 justify-center mb-8">
          <button
            @click="refreshStatus"
            class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            🔄 刷新状态
          </button>
          
          <button
            @click="testRefresh"
            class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            🌐 测试页面刷新
          </button>
          
          <button
            @click="simulateLogin"
            class="px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            🎭 模拟登录
          </button>
          
          <button
            @click="clearAll"
            class="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            🗑️ 清除所有数据
          </button>
        </div>

        <!-- 测试步骤 -->
        <div class="p-6 bg-yellow-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">测试步骤</h2>
          <ol class="list-decimal list-inside space-y-2 text-sm">
            <li>点击"模拟登录"按钮创建登录状态</li>
            <li>确认上方显示"已登录"状态</li>
            <li>点击"测试页面刷新"按钮或按F5刷新页面</li>
            <li>检查刷新后登录状态是否保持</li>
            <li>如果状态丢失，说明持久化有问题</li>
            <li>如果状态保持，说明修复成功！</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import { APP_CONFIG } from '@/config'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 响应式数据
const localToken = ref('')
const localUserInfo = ref('')
let updateInterval = null

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userName = computed(() => userStore.userName)
const userRole = computed(() => userStore.userRole)
const storeToken = computed(() => !!userStore.token)
const storeUserInfo = computed(() => !!userStore.userInfo)

// 更新本地存储信息
const updateLocalInfo = () => {
  localToken.value = getToken() || ''
  
  try {
    const userInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
    localUserInfo.value = userInfo || ''
  } catch (error) {
    localUserInfo.value = ''
  }
}

// 刷新状态
const refreshStatus = async () => {
  try {
    await userStore.initUserState()
    updateLocalInfo()
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新状态失败: ' + error.message)
  }
}

// 测试页面刷新
const testRefresh = () => {
  if (confirm('确定要刷新页面测试登录状态持久化吗？')) {
    window.location.reload()
  }
}

// 模拟登录
const simulateLogin = () => {
  if (window.loginDebug) {
    window.loginDebug.simulate('admin', 'admin')
    setTimeout(() => {
      refreshStatus()
    }, 100)
  } else {
    ElMessage.error('调试工具未加载')
  }
}

// 清除所有数据
const clearAll = () => {
  if (confirm('确定要清除所有登录数据吗？')) {
    if (window.loginDebug) {
      window.loginDebug.clear()
      setTimeout(() => {
        refreshStatus()
      }, 100)
    } else {
      localStorage.clear()
      window.location.reload()
    }
  }
}

onMounted(() => {
  updateLocalInfo()
  // 每秒更新一次本地存储信息
  updateInterval = setInterval(updateLocalInfo, 1000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>
