/**
 * 调试样式修复
 * 用于隐藏开发环境中可能出现的调试边框和样式
 */

// 隐藏所有可能的调试边框
* {
  // 移除所有outline
  outline: none !important;
  
  // 移除可能的调试边框
  &[style*="border: 1px solid red"],
  &[style*="border: 2px solid red"],
  &[style*="border: 1px solid #ff0000"],
  &[style*="border: 2px solid #ff0000"],
  &[style*="border: 1px solid rgb(255, 0, 0)"],
  &[style*="border: 2px solid rgb(255, 0, 0)"] {
    border: none !important;
  }
}

// Vue DevTools 相关样式隐藏
.__vue-devtools-highlight,
.__vue-devtools-overlay,
.__vue-devtools-inspector {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

// 浏览器开发者工具相关
[data-inspector],
[data-devtools] {
  border: none !important;
  outline: none !important;
}

// 确保主要容器没有调试样式
#app,
.app-container,
.main-container {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  
  // 移除可能的调试背景
  &[style*="background: red"],
  &[style*="background: #ff0000"],
  &[style*="background-color: red"],
  &[style*="background-color: #ff0000"] {
    background: transparent !important;
  }
}

// 隐藏可能的错误边框
.error-border,
.debug-border,
.inspector-border {
  border: none !important;
  outline: none !important;
}

// 确保聊天界面正常显示
.chat-container,
.chat-view,
.drawing-view {
  border: none !important;
  outline: none !important;
  
  // 移除可能的调试样式
  * {
    border-color: transparent !important;
    outline: none !important;
  }
}

// 移除可能的调试信息显示
.debug-info,
.dev-info,
.inspector-info {
  display: none !important;
}

// 确保Element Plus组件正常显示
.el-message,
.el-dialog,
.el-button,
.el-input {
  border-color: var(--el-border-color) !important;
  outline: none !important;
}

// 生产环境隐藏所有调试相关内容
[class*="debug"],
[class*="dev-"],
[id*="debug"],
[id*="dev-"] {
  display: none !important;
}
