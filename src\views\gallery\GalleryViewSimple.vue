<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-4">AI 艺术画廊</h1>
        <p class="text-xl opacity-90">探索无限创意，发现AI艺术之美</p>
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <div class="text-3xl font-bold mb-2">{{ totalArtworks }}+</div>
            <div class="text-purple-200">精彩作品</div>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <div class="text-3xl font-bold mb-2">{{ totalArtists }}+</div>
            <div class="text-purple-200">创作者</div>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <div class="text-3xl font-bold mb-2">{{ dailyViews }}+</div>
            <div class="text-purple-200">日浏览量</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <!-- 分类标签 -->
          <div class="flex space-x-2">
            <button
              v-for="category in categories"
              :key="category.value"
              @click="selectedCategory = category.value"
              class="px-4 py-2 rounded-lg transition-colors"
              :class="selectedCategory === category.value 
                ? 'bg-purple-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            >
              {{ category.label }}
            </button>
          </div>

          <!-- 搜索框 -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索作品..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-gray-400">
                  <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                  <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 作品展示区 -->
    <div class="max-w-7xl mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-24">
        <div class="text-center">
          <div class="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-600">正在加载精彩作品...</p>
        </div>
      </div>

      <!-- 作品网格 -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="artwork in filteredArtworks"
          :key="artwork.id"
          class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer group"
          @click="openArtworkDetail(artwork)"
        >
          <!-- 作品图片 -->
          <div class="relative aspect-square overflow-hidden">
            <img
              :src="artwork.imageUrl"
              :alt="artwork.title"
              class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              @load="artwork.imageLoaded = true"
            />
            <div v-if="!artwork.imageLoaded" class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" class="text-gray-400">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            
            <!-- 悬浮操作 -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div class="flex space-x-2">
                <button
                  @click.stop="handleLike(artwork)"
                  class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                  :class="artwork.isLiked ? 'text-red-500' : 'text-gray-600'"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                      :fill="artwork.isLiked ? 'currentColor' : 'none'"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                  </svg>
                </button>
                <button
                  @click.stop="handleShare(artwork)"
                  class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors text-gray-600"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" stroke="currentColor" stroke-width="2"/>
                    <polyline points="16,6 12,2 8,6" stroke="currentColor" stroke-width="2"/>
                    <line x1="12" y1="2" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 作品信息 -->
          <div class="p-4">
            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ artwork.title }}</h3>
            <div class="flex items-center space-x-3 mb-3">
              <img
                :src="artwork.author.avatar"
                :alt="artwork.author.name"
                class="w-8 h-8 rounded-full"
              />
              <div>
                <p class="text-sm font-medium text-gray-900">{{ artwork.author.name }}</p>
                <p class="text-xs text-gray-500">{{ formatDate(artwork.createdAt) }}</p>
              </div>
            </div>
            
            <!-- 标签 -->
            <div class="flex flex-wrap gap-1 mb-3">
              <span
                v-for="tag in artwork.tags.slice(0, 2)"
                :key="tag"
                class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
              >
                {{ tag }}
              </span>
              <span v-if="artwork.tags.length > 2" class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                +{{ artwork.tags.length - 2 }}
              </span>
            </div>

            <!-- 统计数据 -->
            <div class="flex items-center justify-between text-sm text-gray-500">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-1">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span>{{ formatNumber(artwork.views) }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span>{{ formatNumber(artwork.likes) }}</span>
                </div>
              </div>
              <span class="text-purple-600 font-medium">{{ artwork.category }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredArtworks.length === 0" class="text-center py-24">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-gray-400">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
          <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
          <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
        </svg>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">暂无作品</h3>
        <p class="text-gray-600 mb-6">没有找到符合条件的作品，试试其他搜索条件吧</p>
        <button
          @click="resetFilters"
          class="px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
        >
          重置筛选
        </button>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex justify-center mt-12">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalArtworks"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 作品详情对话框 -->
    <ArtworkDetail
      v-model="showDetail"
      :artwork-id="selectedArtworkId"
      @like="handleArtworkLike"
      @comment="handleArtworkComment"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ArtworkDetail from '@/components/gallery/ArtworkDetail.vue'

// 响应式数据
const loading = ref(true)
const searchKeyword = ref('')
const selectedCategory = ref('all')
const currentPage = ref(1)
const pageSize = ref(20)
const showDetail = ref(false)
const selectedArtworkId = ref(null)

// 分类选项
const categories = ref([
  { label: '全部', value: 'all' },
  { label: '人物', value: 'portrait' },
  { label: '风景', value: 'landscape' },
  { label: '抽象', value: 'abstract' },
  { label: '科幻', value: 'scifi' },
  { label: '动物', value: 'animal' }
])

// 统计数据
const totalArtworks = ref(1234)
const totalArtists = ref(567)
const dailyViews = ref(8901)

// 模拟作品数据
const artworks = ref([])

// 计算属性
const filteredArtworks = computed(() => {
  let filtered = artworks.value

  // 分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(artwork => artwork.category === selectedCategory.value)
  }

  // 搜索筛选
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(artwork =>
      artwork.title.toLowerCase().includes(keyword) ||
      artwork.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredArtworks.value.length / pageSize.value))

// 生成模拟数据
const generateMockArtworks = () => {
  const mockArtworks = []
  const categoryValues = categories.value.slice(1).map(c => c.value)
  
  for (let i = 1; i <= 50; i++) {
    mockArtworks.push({
      id: i,
      title: `AI艺术作品 ${i}`,
      imageUrl: `https://picsum.photos/400/400?random=${i}`,
      category: categoryValues[Math.floor(Math.random() * categoryValues.length)],
      author: {
        name: `艺术家${i}`,
        avatar: `https://picsum.photos/100/100?random=user${i}`
      },
      tags: ['AI艺术', '创意', '数字艺术'].slice(0, Math.floor(Math.random() * 3) + 1),
      views: Math.floor(Math.random() * 10000) + 100,
      likes: Math.floor(Math.random() * 1000) + 10,
      isLiked: Math.random() > 0.7,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      imageLoaded: false
    })
  }
  
  return mockArtworks
}

// 事件处理
const openArtworkDetail = (artwork) => {
  selectedArtworkId.value = artwork.id
  showDetail.value = true
}

const handleLike = (artwork) => {
  artwork.isLiked = !artwork.isLiked
  artwork.likes += artwork.isLiked ? 1 : -1
  ElMessage.success(artwork.isLiked ? '已点赞' : '已取消点赞')
}

const handleShare = (artwork) => {
  ElMessage.success('分享链接已复制到剪贴板')
}

const handleArtworkLike = (artwork) => {
  // 处理详情页面的点赞
  const index = artworks.value.findIndex(a => a.id === artwork.id)
  if (index !== -1) {
    artworks.value[index] = { ...artworks.value[index], ...artwork }
  }
}

const handleArtworkComment = (comment) => {
  ElMessage.success('评论发表成功')
}

const handlePageChange = (page) => {
  currentPage.value = page
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = 'all'
  currentPage.value = 1
}

const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const formatDate = (date) => {
  const now = new Date()
  const diff = now - new Date(date)
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return `${Math.floor(days / 30)}月前`
}

// 生命周期
onMounted(async () => {
  // 模拟加载
  setTimeout(() => {
    artworks.value = generateMockArtworks()
    loading.value = false
  }, 1000)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
