<template>
  <div>
    <!-- 管理员权限检查 -->
    <div v-if="!isLoggedIn" class="min-h-screen bg-gray-50 flex items-center justify-center">
      <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div class="mb-6">
          <el-icon class="text-6xl text-gray-400 mb-4"><Lock /></el-icon>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">需要登录</h2>
          <p class="text-gray-600">请先登录以访问此页面</p>
        </div>
        <div class="space-y-3">
          <el-button type="primary" @click="goToLogin" class="w-full">
            前往登录
          </el-button>
          <el-button @click="goToHome" class="w-full">
            返回首页
          </el-button>
        </div>
      </div>
    </div>

    <div v-else-if="!isAdmin" class="min-h-screen bg-gray-50 flex items-center justify-center">
      <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div class="mb-6">
          <el-icon class="text-6xl text-red-400 mb-4"><Warning /></el-icon>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">权限不足</h2>
          <p class="text-gray-600">只有管理员才能访问此页面</p>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div class="flex items-center">
            <el-icon class="text-yellow-600 mr-2"><InfoFilled /></el-icon>
            <div class="text-left">
              <p class="text-sm font-medium text-yellow-800">当前用户信息</p>
              <p class="text-sm text-yellow-700">用户名: {{ userName }}</p>
              <p class="text-sm text-yellow-700">角色: {{ userRoleText }}</p>
            </div>
          </div>
        </div>
        
        <div class="space-y-3">
          <el-button @click="goToHome" class="w-full">
            返回首页
          </el-button>
          <el-button type="info" @click="contactAdmin" class="w-full">
            联系管理员
          </el-button>
        </div>
      </div>
    </div>

    <!-- 管理员权限验证通过，显示内容 -->
    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Lock, Warning, InfoFilled } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const isAdmin = computed(() => userStore.isAdmin)
const userName = computed(() => userStore.userName)
const userRoleText = computed(() => {
  const role = userStore.userRole
  switch (role) {
    case 'admin':
      return '管理员'
    case 'vip':
      return 'VIP用户'
    case 'user':
      return '普通用户'
    default:
      return '未知角色'
  }
})

// 方法
const goToLogin = () => {
  router.push({
    name: 'Login',
    query: { redirect: router.currentRoute.value.fullPath }
  })
}

const goToHome = () => {
  router.push({ name: 'Home' })
}

const contactAdmin = () => {
  ElMessage.info('请联系系统管理员获取相应权限')
}
</script>

<style scoped>
/* 组件样式 */
</style>
