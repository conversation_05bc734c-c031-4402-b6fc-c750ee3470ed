<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端响应式测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e2e8f0;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .menu-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f1f5f9;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            color: #64748b;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }
        
        .title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .model-tag {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            background: #dbeafe;
            color: #2563eb;
            border-radius: 12px;
        }
        
        .messages {
            padding: 1rem 0.75rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            min-height: calc(100vh - 140px);
        }
        
        .message {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .avatar.ai {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .avatar.user {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .message-content {
            max-width: 85%;
            padding: 0.875rem 1rem;
            border-radius: 16px;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .message.ai .message-content {
            background: #ffffff;
            border: 1px solid #f1f5f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            color: #1f2937;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .input-area {
            padding: 0.75rem;
            border-top: 1px solid #e2e8f0;
            background: white;
        }
        
        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 0.375rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }
        
        .input-field {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 16px;
            padding: 0.625rem 0.75rem;
            resize: none;
            min-height: 20px;
            max-height: 100px;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 0.5rem 0.75rem;
            }
            
            .header-left {
                gap: 0.375rem;
            }
            
            .menu-btn {
                width: 32px;
                height: 32px;
            }
            
            .title {
                font-size: 0.9rem;
            }
            
            .model-tag {
                font-size: 0.65rem;
                padding: 0.2rem 0.4rem;
            }
            
            .messages {
                padding: 0.75rem 0.5rem;
                gap: 0.75rem;
            }
            
            .avatar {
                width: 28px;
                height: 28px;
            }
            
            .message-content {
                padding: 0.75rem 0.875rem;
                font-size: 0.875rem;
            }
            
            .input-area {
                padding: 0.5rem;
            }
            
            .input-container {
                padding: 0.625rem;
            }
            
            .send-btn {
                width: 40px;
                height: 40px;
                border-radius: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <button class="menu-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
                <div class="status-dot"></div>
                <div class="title">AI助手</div>
                <div class="model-tag">GPT-4</div>
            </div>
        </div>
        
        <div class="messages">
            <div class="message ai">
                <div class="avatar ai">AI</div>
                <div class="message-content">
                    你好！我是AI助手，很高兴为您服务。请问有什么可以帮助您的吗？
                </div>
            </div>
            
            <div class="message user">
                <div class="avatar user">我</div>
                <div class="message-content">
                    帮我写一个关于人工智能的简短介绍
                </div>
            </div>
            
            <div class="message ai">
                <div class="avatar ai">AI</div>
                <div class="message-content">
                    人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。AI技术正在改变我们的生活方式，从智能手机的语音助手到自动驾驶汽车，AI无处不在。
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-container">
                <textarea class="input-field" placeholder="输入您的问题..." rows="1"></textarea>
                <button class="send-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</body>
</html>
