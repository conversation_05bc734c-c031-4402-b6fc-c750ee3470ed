// HTTP请求封装
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { APP_CONFIG } from '@/config'
import { getToken, removeToken, isTokenExpired } from './auth'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: APP_CONFIG.api.baseURL,
  timeout: APP_CONFIG.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 添加token
    const token = getToken()
    if (token) {
      // 检查token是否过期
      if (isTokenExpired(token)) {
        removeToken()
        ElMessage({
          message: '登录已过期，请重新登录',
          type: 'warning',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        router.push('/login')
        return Promise.reject(new Error('Token expired'))
      }
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }

    // 打印请求信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('🚀 Request:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data,
      })
    }

    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const { data } = response

    // 打印响应信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('📦 Response:', {
        url: response.config.url,
        status: response.status,
        data,
      })
    }

    // 处理文件下载
    if (response.config.responseType === 'blob') {
      return response
    }

    // 统一响应格式处理
    if (data.code !== undefined) {
      // 成功响应
      if (data.code === 200) {
        return data
      }

      // 处理特殊错误码
      switch (data.code) {
        case 401:
          // 未授权，清除token并跳转登录
          const userStore = useUserStore()
          userStore.logout()
          router.push('/login')
          ElMessage({
            message: '登录已过期，请重新登录',
            type: 'warning',
            duration: 4000,
            showClose: true,
            customClass: 'high-contrast-message',
            center: true
          })
          break
        case 403:
          ElMessage({
            message: '没有权限访问该资源',
            type: 'error',
            duration: 4000,
            showClose: true,
            customClass: 'high-contrast-message',
            center: true
          })
          break
        case 404:
          ElMessage({
            message: '请求的资源不存在',
            type: 'error',
            duration: 4000,
            showClose: true,
            customClass: 'high-contrast-message',
            center: true
          })
          break
        case 500:
          ElMessage({
            message: '服务器内部错误',
            type: 'error',
            duration: 4000,
            showClose: true,
            customClass: 'high-contrast-message',
            center: true
          })
          break
        default:
          ElMessage({
            message: data.message || '请求失败',
            type: 'error',
            duration: 4000,
            showClose: true,
            customClass: 'high-contrast-message',
            center: true
          })
      }

      return Promise.reject(new Error(data.message || 'Request failed'))
    }

    // 直接返回数据
    return data
  },
  (error) => {
    console.error('Response error:', error)

    let message = '网络错误'

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          const userStore = useUserStore()
          userStore.logout()
          router.push('/login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = data.message || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message) {
      message = error.message
    }

    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 请求重试机制
const retryRequest = (config, retryCount = 0) => {
  return service(config).catch((error) => {
    if (retryCount < APP_CONFIG.api.retryTimes) {
      console.log(`请求失败，正在重试第${retryCount + 1}次...`)
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(retryRequest(config, retryCount + 1))
        }, 1000 * (retryCount + 1))
      })
    }
    return Promise.reject(error)
  })
}

// 封装请求方法
export const request = {
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config,
    })
  },

  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config,
    })
  },

  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config,
    })
  },

  delete(url, params = {}, config = {}) {
    return service({
      method: 'delete',
      url,
      params,
      ...config,
    })
  },

  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config,
    })
  },

  upload(url, formData, config = {}) {
    return service({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    })
  },

  download(url, params = {}, filename = '') {
    return service({
      method: 'get',
      url,
      params,
      responseType: 'blob',
    }).then((response) => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  },
}

export default service
