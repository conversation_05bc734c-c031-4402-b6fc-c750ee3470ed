<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">OpenRouter API 测试</h1>
      
      <!-- 模型选择 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">选择模型</h2>
        <select v-model="selectedModel" class="w-full p-3 border border-gray-300 rounded-lg">
          <option value="">请选择模型</option>
          <option v-for="model in openrouterModels" :key="model" :value="model">
            {{ model }}
          </option>
        </select>
      </div>

      <!-- 测试消息 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">测试消息</h2>
        <textarea 
          v-model="testMessage" 
          placeholder="输入测试消息..."
          class="w-full p-3 border border-gray-300 rounded-lg h-32 resize-none"
        ></textarea>
        <div class="mt-4 flex space-x-4">
          <button 
            @click="testNormalChat"
            :disabled="!selectedModel || !testMessage.trim() || loading"
            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '测试中...' : '测试普通对话' }}
          </button>
          <button 
            @click="testStreamChat"
            :disabled="!selectedModel || !testMessage.trim() || loading"
            class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '测试中...' : '测试流式对话' }}
          </button>
        </div>
      </div>

      <!-- 结果显示 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">测试结果</h2>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span class="ml-2 text-gray-600">正在测试...</span>
        </div>

        <!-- 错误信息 -->
        <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 class="text-red-800 font-semibold mb-2">错误信息</h3>
          <p class="text-red-700">{{ error }}</p>
        </div>

        <!-- 成功结果 -->
        <div v-else-if="result" class="space-y-4">
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 class="text-green-800 font-semibold mb-2">测试成功</h3>
            <div class="text-sm text-green-700">
              <p><strong>模型:</strong> {{ result.model }}</p>
              <p><strong>用时:</strong> {{ result.duration }}ms</p>
              <p v-if="result.usage"><strong>Token使用:</strong> {{ result.usage.total_tokens }} (输入: {{ result.usage.prompt_tokens }}, 输出: {{ result.usage.completion_tokens }})</p>
            </div>
          </div>
          
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 class="text-gray-800 font-semibold mb-2">AI回复</h3>
            <div class="text-gray-700 whitespace-pre-wrap">{{ result.content }}</div>
          </div>
        </div>

        <!-- 流式结果 -->
        <div v-else-if="streamResult" class="space-y-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-blue-800 font-semibold mb-2">流式测试进行中</h3>
            <div class="text-sm text-blue-700">
              <p><strong>模型:</strong> {{ selectedModel }}</p>
              <p><strong>已接收字符:</strong> {{ streamResult.length }}</p>
            </div>
          </div>
          
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 class="text-gray-800 font-semibold mb-2">实时回复</h3>
            <div class="text-gray-700 whitespace-pre-wrap">{{ streamResult }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-8 text-gray-500">
          选择模型并输入测试消息，然后点击测试按钮
        </div>
      </div>

      <!-- 可用模型列表 -->
      <div class="bg-white rounded-lg shadow p-6 mt-6">
        <h2 class="text-xl font-semibold mb-4">可用的OpenRouter免费模型</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="model in openrouterModels" :key="model" class="border border-gray-200 rounded-lg p-3">
            <div class="font-medium text-sm text-gray-900">{{ getModelName(model) }}</div>
            <div class="text-xs text-gray-500">{{ getProviderName(model) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService.js'
import { OPENROUTER_FREE_MODELS } from '@/services/openrouterApi.js'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedModel = ref('')
const testMessage = ref('你好，请介绍一下你自己。')
const loading = ref(false)
const result = ref(null)
const streamResult = ref('')
const error = ref('')

// OpenRouter模型列表
const openrouterModels = ref(OPENROUTER_FREE_MODELS)

// 辅助函数
const getModelName = (model) => {
  const parts = model.split('/')
  if (parts.length > 1) {
    return parts[1].replace(':free', '')
  }
  return model
}

const getProviderName = (model) => {
  const parts = model.split('/')
  return parts[0] || 'Unknown'
}

// 测试普通对话
const testNormalChat = async () => {
  if (!selectedModel.value || !testMessage.value.trim()) return
  
  loading.value = true
  result.value = null
  streamResult.value = ''
  error.value = ''
  
  const startTime = Date.now()
  
  try {
    const messages = [
      { role: 'user', content: testMessage.value }
    ]
    
    const response = await generateChatCompletion(messages, {
      model: selectedModel.value,
      temperature: 0.7,
      max_tokens: 500
    })
    
    const duration = Date.now() - startTime
    
    result.value = {
      content: response.content,
      model: response.model || selectedModel.value,
      usage: response.usage,
      duration: duration
    }
    
    ElMessage.success('普通对话测试成功！')
  } catch (err) {
    console.error('测试失败:', err)
    error.value = err.message || '测试失败，请检查网络连接和API配置'
    ElMessage.error('测试失败')
  } finally {
    loading.value = false
  }
}

// 测试流式对话
const testStreamChat = async () => {
  if (!selectedModel.value || !testMessage.value.trim()) return
  
  loading.value = true
  result.value = null
  streamResult.value = ''
  error.value = ''
  
  try {
    const messages = [
      { role: 'user', content: testMessage.value }
    ]
    
    await generateChatCompletionStream(messages, {
      model: selectedModel.value,
      temperature: 0.7,
      max_tokens: 500
    }, (chunk) => {
      streamResult.value += chunk
    })
    
    ElMessage.success('流式对话测试成功！')
  } catch (err) {
    console.error('流式测试失败:', err)
    error.value = err.message || '流式测试失败，请检查网络连接和API配置'
    ElMessage.error('流式测试失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时设置默认模型
onMounted(() => {
  if (openrouterModels.value.length > 0) {
    selectedModel.value = openrouterModels.value[0]
  }
})
</script>

<style scoped>
/* 组件样式 */
</style>
