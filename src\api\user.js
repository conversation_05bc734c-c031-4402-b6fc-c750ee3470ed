// 用户相关API
import { request } from '@/utils/request'

/**
 * 用户登录
 */
export function loginApi(data) {
  return request.post('/auth/login', data)
}

/**
 * 用户注册
 */
export function registerApi(data) {
  return request.post('/auth/register', data)
}

/**
 * 用户登出
 */
export function logoutApi() {
  return request.post('/auth/logout')
}

/**
 * 刷新token
 */
export function refreshTokenApi() {
  return request.post('/auth/refresh')
}

/**
 * 获取用户信息
 */
export function getUserInfoApi() {
  return request.get('/user/info')
}

/**
 * 更新用户信息
 */
export function updateUserInfoApi(data) {
  return request.put('/user/info', data)
}

/**
 * 修改密码
 */
export function changePasswordApi(data) {
  return request.put('/user/password', data)
}

/**
 * 上传头像
 */
export function uploadAvatarApi(formData) {
  return request.upload('/user/avatar', formData)
}

/**
 * 获取用户统计信息
 */
export function getUserStatsApi() {
  return request.get('/user/stats')
}

/**
 * 获取用户设置
 */
export function getUserSettingsApi() {
  return request.get('/user/settings')
}

/**
 * 更新用户设置
 */
export function updateUserSettingsApi(data) {
  return request.put('/user/settings', data)
}

/**
 * 发送验证码
 */
export function sendVerificationCodeApi(data) {
  return request.post('/auth/send-code', data)
}

/**
 * 验证邮箱
 */
export function verifyEmailApi(data) {
  return request.post('/auth/verify-email', data)
}

/**
 * 重置密码
 */
export function resetPasswordApi(data) {
  return request.post('/auth/reset-password', data)
}

/**
 * 绑定第三方账号
 */
export function bindThirdPartyApi(data) {
  return request.post('/user/bind-third-party', data)
}

/**
 * 解绑第三方账号
 */
export function unbindThirdPartyApi(provider) {
  return request.delete(`/user/unbind-third-party/${provider}`)
}

/**
 * 获取用户作品列表
 */
export function getUserWorksApi(params) {
  return request.get('/user/works', params)
}

/**
 * 获取用户收藏列表
 */
export function getUserFavoritesApi(params) {
  return request.get('/user/favorites', params)
}

/**
 * 获取用户关注列表
 */
export function getUserFollowingApi(params) {
  return request.get('/user/following', params)
}

/**
 * 获取用户粉丝列表
 */
export function getUserFollowersApi(params) {
  return request.get('/user/followers', params)
}

/**
 * 关注用户
 */
export function followUserApi(userId) {
  return request.post(`/user/follow/${userId}`)
}

/**
 * 取消关注用户
 */
export function unfollowUserApi(userId) {
  return request.delete(`/user/follow/${userId}`)
}

/**
 * 检查是否关注用户
 */
export function checkFollowStatusApi(userId) {
  return request.get(`/user/follow-status/${userId}`)
}

/**
 * 获取用户公开信息
 */
export function getUserPublicInfoApi(userId) {
  return request.get(`/user/public/${userId}`)
}

/**
 * 搜索用户
 */
export function searchUsersApi(params) {
  return request.get('/user/search', params)
}

/**
 * 举报用户
 */
export function reportUserApi(data) {
  return request.post('/user/report', data)
}

/**
 * 拉黑用户
 */
export function blockUserApi(userId) {
  return request.post(`/user/block/${userId}`)
}

/**
 * 取消拉黑用户
 */
export function unblockUserApi(userId) {
  return request.delete(`/user/block/${userId}`)
}

/**
 * 获取拉黑列表
 */
export function getBlockedUsersApi(params) {
  return request.get('/user/blocked', params)
}

/**
 * 注销账号
 */
export function deleteAccountApi(data) {
  return request.post('/user/delete-account', data)
}
