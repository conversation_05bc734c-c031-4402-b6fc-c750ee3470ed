# API密钥配置功能

这是一个类似Cherry Studio的API密钥配置界面，支持多种AI服务提供商的统一管理。

## 功能特性

### 🔑 多提供商支持
- **OpenRouter**: 支持多种免费AI模型，无需付费即可体验
- **OpenAI**: 官方API服务，稳定可靠
- **Anthropic**: Claude AI模型，擅长复杂对话
- **自定义**: 支持任何OpenAI兼容的API服务

### 🛡️ 安全存储
- API密钥仅存储在本地浏览器中
- 不会上传到服务器
- 支持密码显示/隐藏切换

### 🔗 连接测试
- 一键测试API连接状态
- 实时显示提供商状态
- 批量测试功能

### ⚙️ 灵活配置
- 自定义API基础地址
- 多模型选择支持
- 超时时间和重试次数配置

## 文件结构

```
src/
├── components/
│   └── ApiKeyConfig.vue          # 核心配置组件
├── views/
│   ├── ApiKeyManagement.vue      # 主配置页面
│   └── demo/
│       └── ApiKeyConfigDemo.vue  # 演示页面
└── router/
    └── index.js                  # 路由配置
```

## 使用方法

### 1. 访问配置页面

通过以下方式访问API密钥配置：

- 直接访问: `/api-key-management`
- 演示页面: `/api-key-demo`
- 菜单导航: 设置 → API密钥配置

### 2. 配置步骤

1. **选择提供商**: 从预设列表中选择AI服务提供商
2. **输入密钥**: 填入您的API密钥和相关配置
3. **测试连接**: 点击测试按钮验证配置
4. **保存配置**: 确认无误后保存配置

### 3. 快速配置

使用预设配置快速开始：

```javascript
// OpenRouter 免费配置示例
{
  name: 'OpenRouter',
  type: 'openai',
  baseUrl: 'https://openrouter.ai/api/v1',
  apiKey: 'your-api-key-here',
  models: [
    'deepseek/deepseek-chat-v3-0324:free',
    'qwen/qwen3-coder:free'
  ]
}
```

## 组件API

### ApiKeyConfig 组件

#### Props
无

#### Events
- `@provider-configured`: 提供商配置完成时触发
- `@provider-tested`: 提供商测试完成时触发

#### Methods
- `testProvider(provider)`: 测试指定提供商
- `configureProvider(provider)`: 配置指定提供商
- `addProvider(config)`: 添加新提供商

### ApiKeyManagement 页面

完整的API密钥管理页面，包含：
- 统计卡片显示
- 配置组件集成
- 快速配置向导
- 使用指南

## 样式定制

### CSS变量

```css
:root {
  --api-config-primary-color: #409eff;
  --api-config-success-color: #67c23a;
  --api-config-warning-color: #e6a23c;
  --api-config-danger-color: #f56c6c;
  --api-config-border-radius: 8px;
  --api-config-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```

### 响应式设计

- 桌面端: 网格布局，多列显示
- 平板端: 自适应列数
- 移动端: 单列布局，垂直排列

## 安全建议

1. **API密钥保护**
   - 不要在代码中硬编码API密钥
   - 定期更换API密钥
   - 监控API使用情况

2. **权限控制**
   - 使用最小权限原则
   - 为不同用途创建不同的API密钥
   - 设置使用限额

3. **网络安全**
   - 使用HTTPS连接
   - 验证API响应
   - 处理网络错误

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查API密钥是否正确
   - 验证API地址是否可访问
   - 确认网络连接正常

2. **模型不可用**
   - 检查模型名称是否正确
   - 验证账户权限
   - 确认模型是否支持

3. **配置丢失**
   - 检查浏览器存储设置
   - 清除缓存后重新配置
   - 导出/导入配置备份

### 调试模式

开启调试模式查看详细日志：

```javascript
// 在浏览器控制台中执行
localStorage.setItem('api-config-debug', 'true')
```

## 扩展开发

### 添加新提供商

1. 在 `providers` 数组中添加配置：

```javascript
{
  id: 'custom-provider',
  name: '自定义提供商',
  description: '描述信息',
  type: 'openai', // 或其他类型
  defaultBaseUrl: 'https://api.example.com/v1',
  availableModels: ['model1', 'model2'],
  // ... 其他配置
}
```

2. 实现对应的API适配器
3. 添加图标和样式
4. 更新文档

### 自定义主题

创建自定义主题文件：

```css
/* custom-theme.css */
.api-key-config {
  --primary-color: #your-color;
  /* 其他自定义变量 */
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持OpenRouter、OpenAI、Anthropic
- 基础配置和测试功能

### 计划功能
- [ ] 配置导入/导出
- [ ] 批量操作
- [ ] 使用统计
- [ ] 更多提供商支持

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个功能。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 邮箱: <EMAIL>
