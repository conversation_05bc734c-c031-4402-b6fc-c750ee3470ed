<!--
  登录状态测试页面
  用于测试和验证登录状态管理功能
-->
<template>
  <div class="login-state-test">
    <div class="container">
      <h1 class="page-title">🔐 登录状态管理测试</h1>
      <p class="page-description">
        测试登录状态的保存、恢复和管理功能
      </p>

      <!-- 当前登录状态 -->
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📊 当前登录状态</h3>
            <el-button @click="refreshStatus" :loading="refreshing" size="small" type="primary">
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>
          </div>
        </template>

        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">登录状态:</span>
            <el-tag :type="summary.isLoggedIn ? 'success' : 'info'">
              {{ summary.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </div>

          <div class="status-item">
            <span class="status-label">用户名:</span>
            <span class="status-value">{{ summary.username || '未知' }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">用户角色:</span>
            <el-tag :type="getRoleType(summary.role)" size="small">
              {{ summary.role || '未知' }}
            </el-tag>
          </div>

          <div class="status-item">
            <span class="status-label">Token状态:</span>
            <el-tag :type="summary.hasToken ? 'success' : 'danger'" size="small">
              {{ summary.hasToken ? '存在' : '不存在' }}
            </el-tag>
          </div>

          <div class="status-item">
            <span class="status-label">Token过期:</span>
            <el-tag :type="summary.tokenExpired ? 'danger' : 'success'" size="small">
              {{ summary.tokenExpired ? '已过期' : '有效' }}
            </el-tag>
          </div>

          <div class="status-item">
            <span class="status-label">记住我:</span>
            <el-tag :type="summary.rememberMe ? 'success' : 'info'" size="small">
              {{ summary.rememberMe ? '是' : '否' }}
            </el-tag>
          </div>

          <div v-if="summary.loginDuration" class="status-item full-width">
            <span class="status-label">登录时长:</span>
            <span class="status-value">{{ formatDuration(summary.loginDuration) }}</span>
          </div>

          <div v-if="summary.lastActivity" class="status-item full-width">
            <span class="status-label">最后活动:</span>
            <span class="status-value">{{ formatTime(summary.lastActivity) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 测试操作 -->
      <el-card class="test-card" shadow="hover">
        <template #header>
          <h3>🧪 测试操作</h3>
        </template>

        <div class="test-actions">
          <el-button-group>
            <el-button @click="testLogin" :loading="testing" type="primary">
              <el-icon><User /></el-icon>
              模拟登录
            </el-button>
            <el-button @click="testLogout" :loading="testing" type="danger">
              <el-icon><SwitchButton /></el-icon>
              测试登出
            </el-button>
          </el-button-group>

          <el-button-group>
            <el-button @click="testRefresh" :loading="testing" type="info">
              <el-icon><Refresh /></el-icon>
              测试页面刷新
            </el-button>
            <el-button @click="clearAllData" :loading="testing" type="warning">
              <el-icon><Delete /></el-icon>
              清除所有数据
            </el-button>
          </el-button-group>

          <el-button @click="runFullTest" :loading="fullTesting" type="success" size="large">
            <el-icon><CircleCheck /></el-icon>
            运行完整测试
          </el-button>
        </div>
      </el-card>

      <!-- 测试结果 -->
      <el-card v-if="testResults.length > 0" class="results-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>📋 测试结果</h3>
            <el-button @click="clearResults" size="small">清除结果</el-button>
          </div>
        </template>

        <div class="test-results">
          <div 
            v-for="(result, index) in testResults" 
            :key="index" 
            class="test-result-item"
            :class="{ success: result.success, error: !result.success }"
          >
            <div class="result-header">
              <el-icon>
                <CircleCheck v-if="result.success" />
                <CircleClose v-else />
              </el-icon>
              <span class="result-title">{{ result.title }}</span>
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
            </div>
            <div v-if="result.message" class="result-message">
              {{ result.message }}
            </div>
            <div v-if="result.details" class="result-details">
              <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 本地存储详情 -->
      <el-card class="storage-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>💾 本地存储详情</h3>
            <el-button @click="refreshStorage" size="small">刷新</el-button>
          </div>
        </template>

        <div class="storage-items">
          <div v-for="item in storageItems" :key="item.key" class="storage-item">
            <div class="storage-key">{{ item.key }}</div>
            <div class="storage-value">
              <el-tag v-if="item.exists" type="success" size="small">存在</el-tag>
              <el-tag v-else type="info" size="small">不存在</el-tag>
              <span v-if="item.value" class="value-preview">
                {{ item.preview }}
              </span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, User, SwitchButton, Delete, CircleCheck, CircleClose 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import loginStateManager from '@/utils/loginStateManager'
import { APP_CONFIG } from '@/config'

// 用户store
const userStore = useUserStore()

// 响应式数据
const refreshing = ref(false)
const testing = ref(false)
const fullTesting = ref(false)
const summary = reactive({
  isLoggedIn: false,
  hasToken: false,
  hasUserInfo: false,
  tokenExpired: null,
  username: null,
  role: null,
  loginDuration: null,
  rememberMe: false,
  lastActivity: null
})

const testResults = ref([])
const storageItems = ref([])

// 刷新状态
const refreshStatus = async () => {
  refreshing.value = true
  try {
    const newSummary = loginStateManager.getSummary()
    Object.assign(summary, newSummary)
    refreshStorage()
  } catch (error) {
    ElMessage.error('刷新状态失败: ' + error.message)
  } finally {
    refreshing.value = false
  }
}

// 刷新存储信息
const refreshStorage = () => {
  const keys = [
    APP_CONFIG.storage.tokenKey,
    APP_CONFIG.storage.userKey,
    'ai_creative_login_state',
    'ai_creative_login_timestamp',
    'ai_creative_last_activity',
    'ai_creative_remember_me'
  ]

  storageItems.value = keys.map(key => {
    const value = localStorage.getItem(key)
    return {
      key,
      exists: !!value,
      value,
      preview: value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : null
    }
  })
}

// 获取角色类型
const getRoleType = (role) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'vip': return 'warning'
    case 'user': return 'success'
    default: return 'info'
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '未知'
  return new Date(time).toLocaleString()
}

// 格式化持续时间
const formatDuration = (duration) => {
  if (!duration) return '未知'
  
  const { days, hours, minutes } = duration
  const parts = []
  
  if (days > 0) parts.push(`${days}天`)
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  
  return parts.length > 0 ? parts.join(' ') : '不到1分钟'
}

// 添加测试结果
const addTestResult = (title, success, message, details = null) => {
  testResults.value.unshift({
    title,
    success,
    message,
    details,
    timestamp: new Date()
  })
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 测试登录
const testLogin = async () => {
  testing.value = true
  try {
    const result = await userStore.login({
      username: 'admin',
      password: 'admin',
      rememberMe: true
    })
    
    if (result.success) {
      addTestResult('模拟登录', true, '登录成功')
      await refreshStatus()
    } else {
      addTestResult('模拟登录', false, result.message || '登录失败')
    }
  } catch (error) {
    addTestResult('模拟登录', false, '登录异常: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 测试登出
const testLogout = async () => {
  testing.value = true
  try {
    await userStore.logout()
    addTestResult('测试登出', true, '登出成功')
    await refreshStatus()
  } catch (error) {
    addTestResult('测试登出', false, '登出异常: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 测试页面刷新
const testRefresh = () => {
  if (confirm('确定要刷新页面测试登录状态持久化吗？')) {
    window.location.reload()
  }
}

// 清除所有数据
const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '这将清除所有登录相关的本地数据，确定继续吗？',
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    testing.value = true
    loginStateManager.clear()
    await refreshStatus()
    addTestResult('清除数据', true, '所有登录数据已清除')
  } catch (error) {
    if (error !== 'cancel') {
      addTestResult('清除数据', false, '清除失败: ' + error.message)
    }
  } finally {
    testing.value = false
  }
}

// 运行完整测试
const runFullTest = async () => {
  fullTesting.value = true
  
  try {
    addTestResult('完整测试', true, '开始运行完整测试套件...')
    
    // 1. 清除现有状态
    loginStateManager.clear()
    addTestResult('步骤1', true, '清除现有状态')
    
    // 2. 测试登录
    const loginResult = await userStore.login({
      username: 'admin',
      password: 'admin',
      rememberMe: true
    })
    
    if (loginResult.success) {
      addTestResult('步骤2', true, '登录测试通过')
    } else {
      addTestResult('步骤2', false, '登录测试失败')
      return
    }
    
    // 3. 验证状态保存
    const savedSummary = loginStateManager.getSummary()
    if (savedSummary.isLoggedIn) {
      addTestResult('步骤3', true, '状态保存验证通过')
    } else {
      addTestResult('步骤3', false, '状态保存验证失败')
      return
    }
    
    // 4. 测试状态恢复
    await userStore.initUserState()
    const restoredSummary = loginStateManager.getSummary()
    if (restoredSummary.isLoggedIn) {
      addTestResult('步骤4', true, '状态恢复验证通过')
    } else {
      addTestResult('步骤4', false, '状态恢复验证失败')
      return
    }
    
    // 5. 测试登出
    await userStore.logout()
    const logoutSummary = loginStateManager.getSummary()
    if (!logoutSummary.isLoggedIn) {
      addTestResult('步骤5', true, '登出测试通过')
    } else {
      addTestResult('步骤5', false, '登出测试失败')
      return
    }
    
    addTestResult('完整测试', true, '所有测试步骤均通过！')
    ElMessage.success('完整测试通过！')
    
  } catch (error) {
    addTestResult('完整测试', false, '测试异常: ' + error.message)
    ElMessage.error('测试失败: ' + error.message)
  } finally {
    fullTesting.value = false
    await refreshStatus()
  }
}

// 清除结果
const clearResults = () => {
  testResults.value = []
}

// 生命周期
onMounted(() => {
  refreshStatus()
})
</script>

<style lang="scss" scoped>
.login-state-test {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-description {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    color: #2c3e50;
  }
}

.status-card, .test-card, .results-card, .storage-card {
  margin-bottom: 1.5rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  
  &.full-width {
    grid-column: 1 / -1;
  }
}

.status-label {
  font-weight: 500;
  color: #495057;
}

.status-value {
  color: #212529;
  font-family: monospace;
}

.test-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.test-result-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  border-left: 4px solid;
  
  &.success {
    background: #f0f9ff;
    border-left-color: #10b981;
  }
  
  &.error {
    background: #fef2f2;
    border-left-color: #ef4444;
  }
}

.result-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.result-title {
  font-weight: 500;
  flex: 1;
}

.result-time {
  font-size: 0.8rem;
  color: #6b7280;
}

.result-message {
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.result-details {
  background: #f9fafb;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.storage-items {
  display: grid;
  gap: 0.5rem;
}

.storage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.storage-key {
  font-weight: 500;
  color: #495057;
  font-family: monospace;
}

.storage-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.value-preview {
  font-family: monospace;
  font-size: 0.8rem;
  color: #6c757d;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .test-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .storage-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
