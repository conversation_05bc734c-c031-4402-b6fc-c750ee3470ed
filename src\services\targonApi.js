/**
 * Targon API 服务类
 * 提供与 Targon API 的集成功能
 */

import axios from 'axios'

class TargonApiService {
  constructor() {
    this.baseURL = 'https://api.targon.com/v1'
    this.apiKey = null
    this.timeout = 60000
    this.maxRetries = 3

    // 密钥池管理
    this.keyPool = []
    this.currentKeyIndex = 0
    this.keyHealthStatus = new Map() // 记录每个密钥的健康状态
    this.keyUsageCount = new Map() // 记录每个密钥的使用次数
    this.lastKeyRotation = Date.now()
    this.keyRotationInterval = 5 * 60 * 1000 // 5分钟轮换一次

    // 初始化密钥池
    this.initializeKeyPool()
  }

  /**
   * 初始化密钥池
   */
  initializeKeyPool() {
    this.keyPool = [
      // 第一批密钥 (1-50)
      'sn4_flxher23klyekojixf6p73vwdafr', 'sn4_02iqzz7hiht769dh2rs052pjqexx', 'sn4_jlirb6v4pr8hn0oa4t1re55ucydo',
      'sn4_scnlu110zsabg3dpfr8fvbwsluwt', 'sn4_miewnql2fp5bh1dbtyx490yeuy5g', 'sn4_d4h1iv1nf2g4vhv36hytbp4pr6yj',
      'sn4_f1a74s5mv89z7co7oyzicc7p308c', 'sn4_tvl9lwbp03vl0qekw7hxo6nlwnz2', 'sn4_cydrctzr7o30qlzl00pmxcb6x2og',
      'sn4_eqzvm5j8wrr553vebobesuhvq4h1', 'sn4_kq4ydsazdsbzaswyotkbqkk3ocvd', 'sn4_gbftlfjstsd9216nqbob39a3c154',
      'sn4_heeeh7m3y0kfz6p1vrj701y9zs14', 'sn4_52x9xc10bz6ybi0g73mz4f6t781r', 'sn4_xxk33r1d80z1jvzav0sn7v9tvxv2',
      'sn4_twzs0kh80zlfq3gio56pim5trzik', 'sn4_8go487zkmvkswsf8wf0s7uwdi18a', 'sn4_p9xxrv6i0pqpknpl8j6ao0cc7r0o',
      'sn4_c4qtbz6l7ztgmrpnrazzxglr2cay', 'sn4_m479ta5nadiy4wpajfi2uj6afkoq', 'sn4_lmdhdz0l44jhwyflndc543153lg1',
      'sn4_2dndogsrlrmwr30ogf23dnlgx6j8', 'sn4_ev85f0fn2ge9vn9b2eyd6rl8dslt', 'sn4_3vgrxlayot4hm28mzttso4cwrzvz',
      'sn4_v8naqjz2y51tmr28xpyi00hy37wb', 'sn4_rontcxo4vqdisag6hr8ddg77dld9', 'sn4_fkpl153yn15rd3ypjr88vwuwb6c7',
      'sn4_e32n5ja702pv0lg3etk8jwnpsamh', 'sn4_gdeva84iyj1cuxe349we4i9b98yv', 'sn4_37bg3mhlnf89el6f0dxf1afuesl7',
      'sn4_xzhtw1v8xboli04vqg4n9m4chmdo', 'sn4_r143peeskkn6qzllb2u6fbju7ymy', 'sn4_aom4jiucoubztrg5e1hwy0ihhswl',
      'sn4_a37iu44q7eymbvg2cyk6guljqjn0', 'sn4_jd4c9zsawd1lrfwue4gp9id9gcak', 'sn4_n81432fgwsq9bl8nxzf8k15ykgqf',
      'sn4_vitaqw70uexf9ynho8zf9l0b0eu5', 'sn4_skibtsbe2wbnamkb9vqfzz5ckh8e', 'sn4_dbk4zb5e4xl1ms8kvces7sy9a54f',
      'sn4_ruyg6c8jb241uh9j4n1hl4hf04w4', 'sn4_si0m3svly6gm3lh627r8ec1axkqh', 'sn4_yru0auhl1qljyqhszw3hmvxhf7k3',
      'sn4_6jiw602m1swgq94w88pf8r8hm2pg', 'sn4_vyiedhphunlvsftt3mospfvvzjhe', 'sn4_uv5ikpl2vh38zduvo3df3djr9obb',
      'sn4_azavhi4yblwk66aalseq0y08m3j1', 'sn4_o4cixbdgaxwdcdv1wicqjclhk9by', 'sn4_ntffpv4hyykaaowqjt9msfurhi84',
      'sn4_f8rgc74bcl1q7v6gnprihfu2o9dy', 'sn4_1cf6a1iyqqcxwliaw2n7pljsjozh', 'sn4_0ork5j8oh5pwa8lf65tt22fx1mq9',
      'sn4_8mxiwk11is1slby6law77w8oefpd', 'sn4_ilbx5pf58mu6i5knf3kef5x63df2', 'sn4_hnq3ok16wkrmc6lzrx9fkaloot2y',

      // 第二批密钥 (51-100)
      'sn4_gr3tdbuzjzt6jovm1gldhy0o00cq', 'sn4_rd0vk5dx15wk2j34ldwci4gihx37', 'sn4_8xp7yvqykrtl36s8kefgg8b8p11w',
      'sn4_j1du9czkealntgvrgeqikov23b9i', 'sn4_z5psf7rhb27yre26t5ymd2h5jtpx', 'sn4_bevh49hwo3vcbauly4h2x8alr19w',
      'sn4_s9excsmz8y21t40kkf7mbjrldldu', 'sn4_9mrqorcbe9yzqhjjuzsk9o5g0ycd', 'sn4_7x71cpjkpklq1cdy4frq1raeubpg',
      'sn4_wsn5h6gbc18y0e16wterm62e400a', 'sn4_wv8qli7q4j6icnju93hoktauj00r', 'sn4_23fqv8691pzht30g1hym8v4o0q02',
      'sn4_dmbx8zeqfmubd7msv6a0c9l89b9a', 'sn4_mdlw4tyrp9alp535mofrsgia5i72', 'sn4_id2v60ji156mrlo7qj96b95t859v',
      'sn4_dpw10d4vyeg7dsjsytorjok369r9', 'sn4_w65a2mj189ta3mo52tq36932flkl', 'sn4_npa29gg3joun18gabx1o490j5ooi',
      'sn4_1sem6mfuv4k90wi5a9o4pan3jm40', 'sn4_1noh5vdyy1q9sjy7yx2swzi0qnwc', 'sn4_pcf2ms0xx5e7dmab3rpyl1t9i4yi',
      'sn4_dok4a6613e8unhk03qvrnx6mgjjo', 'sn4_20dxhrpnncm2ywri2f58uk3mmvbl', 'sn4_hggw9odx77ggvu3tgn480f8nelht',
      'sn4_z4aj56mhkxru8us5x28reawvcqt5', 'sn4_54p0plih1999pk93x8hyczq37wmo', 'sn4_zihys8efn3wdmlgw6vehuodvimdx',
      'sn4_7psfkifd8pc54p21xg7klooywexy', 'sn4_4yus8sn48w4f396ml98auzkzqn3p', 'sn4_h1f8mmt26r9ioe1patvrfqs9tzi5',
      'sn4_6w1dlx4k2p43bzg5wsrhzloi3aht', 'sn4_rjko1d2tc09hdalo5y5az7xd8j3v', 'sn4_few6be9h8mh6hmk0geoyafuon9un',
      'sn4_iwovy25b3tppp2y06qjqxcyyh41c', 'sn4_lcjo1384ig659dxvnekjptnyj59s', 'sn4_7nj3y3euzoaowpqcsiyxwyefsuiz',
      'sn4_3f2ijyhfti9nvpx31rc7ms4pyir5', 'sn4_ueml5ake4bx0off8zw21yyj07li7', 'sn4_gktxco2nnkagi80t4dr66ox0gxym',
      'sn4_kt0q3s2ahvcl4qml7elnfe9705sc', 'sn4_36ygqd0i7bxc1r8fthqa2sp3rhxh', 'sn4_lpx8xnuvyvvheiyyppcailmdeb27',
      'sn4_t2crae1tcwyvtpw7nozax15fqkb0', 'sn4_oc4iws9fm4y7j0ero6ioojpjskb6', 'sn4_14tsiudj4nsjniqypbjbawhcadtc',
      'sn4_2lswnd9aq9k3fg3nlz6vwz0fi1u8', 'sn4_quubuk45hhn6zey3d1exjbexgtqr', 'sn4_hgsl46376pv0s2lwe3qcj3k4uabx',
      'sn4_9acdkffeflovo0rr0reyjsqdyk3p', 'sn4_fyrrhi1lyrebj629xwgonm5q7nj8', 'sn4_gwpp699smiuboqujj6tzw0f4uaim',
      'sn4_ossen4doelyhak85b6bldn7nd6kn', 'sn4_nfwqf8emmvsncjuqy2fcok4gcn6e', 'sn4_y2w34m788z29aov53lrvngnloi5v'
    ]

    // 初始化密钥健康状态
    this.keyPool.forEach(key => {
      this.keyHealthStatus.set(key, { healthy: true, lastError: null, errorCount: 0 })
      this.keyUsageCount.set(key, 0)
    })

    console.log(`🔑 初始化 Targon 密钥池，共 ${this.keyPool.length} 个密钥`)
  }

  /**
   * 获取当前可用的API密钥
   * @returns {string} API密钥
   */
  getCurrentApiKey() {
    // 如果手动设置了密钥，优先使用
    if (this.apiKey) {
      return this.apiKey
    }

    // 检查是否需要轮换密钥
    if (Date.now() - this.lastKeyRotation > this.keyRotationInterval) {
      this.rotateKey()
    }

    // 获取健康的密钥
    const healthyKeys = this.keyPool.filter(key =>
      this.keyHealthStatus.get(key)?.healthy !== false
    )

    if (healthyKeys.length === 0) {
      console.warn('⚠️ 所有密钥都不可用，重置健康状态')
      this.resetKeyHealth()
      return this.keyPool[0]
    }

    // 使用轮询策略选择密钥
    const selectedKey = healthyKeys[this.currentKeyIndex % healthyKeys.length]

    // 增加使用计数
    const currentCount = this.keyUsageCount.get(selectedKey) || 0
    this.keyUsageCount.set(selectedKey, currentCount + 1)

    return selectedKey
  }

  /**
   * 轮换密钥
   */
  rotateKey() {
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keyPool.length
    this.lastKeyRotation = Date.now()
    console.log(`🔄 密钥轮换到索引: ${this.currentKeyIndex}`)
  }

  /**
   * 重置密钥健康状态
   */
  resetKeyHealth() {
    this.keyPool.forEach(key => {
      this.keyHealthStatus.set(key, { healthy: true, lastError: null, errorCount: 0 })
    })
    console.log('🔄 重置所有密钥健康状态')
  }

  /**
   * 标记密钥为不健康
   * @param {string} apiKey - API密钥
   * @param {Error} error - 错误信息
   */
  markKeyUnhealthy(apiKey, error) {
    const status = this.keyHealthStatus.get(apiKey) || { healthy: true, lastError: null, errorCount: 0 }
    status.errorCount += 1
    status.lastError = error.message

    // 如果错误次数超过3次，标记为不健康
    if (status.errorCount >= 3) {
      status.healthy = false
      console.warn(`❌ 密钥 ${apiKey.substring(0, 10)}... 标记为不健康`)
    }

    this.keyHealthStatus.set(apiKey, status)
  }

  /**
   * 设置API密钥
   * @param {string} apiKey - API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey
  }

  /**
   * 设置基础URL
   * @param {string} baseURL - 基础URL
   */
  setBaseURL(baseURL) {
    this.baseURL = baseURL
  }

  /**
   * 创建HTTP客户端
   * @returns {Object} axios实例
   */
  createClient() {
    return axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Creative-Platform/1.0.0'
      }
    })
  }

  /**
   * 测试API连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      
      // 尝试获取模型列表来测试连接
      const response = await client.get('/models')
      
      return {
        success: true,
        message: '连接成功',
        data: {
          modelsCount: response.data?.data?.length || 0,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 获取可用模型列表
   * @returns {Promise<Array>} 模型列表
   */
  async getModels() {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      const response = await client.get('/models')
      
      return {
        success: true,
        data: response.data?.data || []
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 发送聊天请求
   * @param {Object} params - 请求参数
   * @param {string} params.model - 模型名称
   * @param {Array} params.messages - 消息数组
   * @param {number} params.temperature - 温度参数
   * @param {number} params.max_tokens - 最大token数
   * @param {boolean} params.stream - 是否流式输出
   * @returns {Promise<Object>} 响应结果
   */
  async chat(params) {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      
      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: params.stream || false,
        ...params.extra
      }

      const response = await client.post('/chat/completions', requestData)
      
      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 流式聊天请求
   * @param {Object} params - 请求参数
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   * @returns {Promise<void>}
   */
  async streamChat(params, onMessage, onError, onComplete) {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: true,
        ...params.extra
      }

      // 使用 fetch 进行流式请求，兼容浏览器环境
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Creative-Platform/1.0.0'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            onComplete?.()
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              if (data === '[DONE]') {
                onComplete?.()
                return
              }
              if (data) {
                try {
                  const parsed = JSON.parse(data)
                  onMessage?.(parsed)
                } catch (e) {
                  // 忽略解析错误
                  console.warn('解析流式数据失败:', e, data)
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

    } catch (error) {
      console.error('流式聊天错误:', error)
      onError?.(error)
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data
      
      switch (status) {
        case 401:
          return 'API密钥无效或已过期'
        case 403:
          return '访问被拒绝，请检查API密钥权限'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return '服务器内部错误'
        case 502:
        case 503:
        case 504:
          return '服务暂时不可用，请稍后重试'
        default:
          return data?.error?.message || data?.message || `HTTP ${status} 错误`
      }
    } else if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    } else if (error.code === 'ENOTFOUND') {
      return '无法连接到服务器，请检查网络'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 获取预设的模型配置
   * @returns {Array} 模型配置列表
   */
  getPresetModels() {
    return [
      {
        id: 'deepseek-ai/DeepSeek-V3',
        name: 'DeepSeek V3',
        description: 'DeepSeek最新版本，强大的推理能力',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-V3-0324',
        name: 'DeepSeek V3 (0324)',
        description: 'DeepSeek V3 3月24日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1',
        name: 'DeepSeek R1',
        description: 'DeepSeek推理专用模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1-0528',
        name: 'DeepSeek R1 (0528)',
        description: 'DeepSeek R1 5月28日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'moonshot/Kimi-K2-Instruct',
        name: 'Kimi K2',
        description: 'Moonshot AI的Kimi K2指令模型',
        maxTokens: 16384,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8',
        name: 'Qwen3 Coder 480B',
        description: '通义千问3代码专用大模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
        name: 'Qwen3 235B',
        description: '通义千问3指令模型',
        maxTokens: 32768,
        supportsFunctions: true
      }
    ]
  }
}

// 创建单例实例
const targonApi = new TargonApiService()

export default targonApi
export { TargonApiService }
