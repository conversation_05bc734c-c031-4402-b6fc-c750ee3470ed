/**
 * Targon API 服务类
 * 提供与 Targon API 的集成功能
 */

import axios from 'axios'

class TargonApiService {
  constructor() {
    this.baseURL = 'https://api.targon.com/v1'
    this.apiKey = null
    this.timeout = 60000
    this.maxRetries = 3

    // 密钥池管理
    this.keyPool = []
    this.currentKeyIndex = 0
    this.keyHealthStatus = new Map() // 记录每个密钥的健康状态
    this.keyUsageCount = new Map() // 记录每个密钥的使用次数
    this.lastKeyRotation = Date.now()
    this.keyRotationInterval = 5 * 60 * 1000 // 5分钟轮换一次

    // 初始化密钥池
    this.initializeKeyPool()
  }

  /**
   * 初始化密钥池
   */
  initializeKeyPool() {
    // 从配置文件导入密钥池
    import('@/config/targonKeys.js').then(module => {
      this.keyPool = [...module.TARGON_API_KEYS]

      // 初始化密钥健康状态
      this.keyPool.forEach(key => {
        this.keyHealthStatus.set(key, { healthy: true, lastError: null, errorCount: 0 })
        this.keyUsageCount.set(key, 0)
      })

      console.log(`🔑 初始化 Targon 密钥池，共 ${this.keyPool.length} 个密钥`)
    }).catch(error => {
      console.error('❌ 加载密钥池失败，使用默认密钥:', error)
      // 使用默认密钥作为备用
      this.keyPool = [
        'sn4_flxher23klyekojixf6p73vwdafr', 'sn4_02iqzz7hiht769dh2rs052pjqexx', 'sn4_jlirb6v4pr8hn0oa4t1re55ucydo',
        'sn4_scnlu110zsabg3dpfr8fvbwsluwt', 'sn4_miewnql2fp5bh1dbtyx490yeuy5g', 'sn4_d4h1iv1nf2g4vhv36hytbp4pr6yj'
      ]

      // 初始化默认密钥健康状态
      this.keyPool.forEach(key => {
        this.keyHealthStatus.set(key, { healthy: true, lastError: null, errorCount: 0 })
        this.keyUsageCount.set(key, 0)
      })
    })
  }

  /**
   * 获取当前可用的API密钥
   * @returns {string} API密钥
   */
  getCurrentApiKey() {
    // 如果手动设置了密钥，优先使用
    if (this.apiKey) {
      return this.apiKey
    }

    // 检查是否需要轮换密钥
    if (Date.now() - this.lastKeyRotation > this.keyRotationInterval) {
      this.rotateKey()
    }

    // 获取健康的密钥
    const healthyKeys = this.keyPool.filter(key =>
      this.keyHealthStatus.get(key)?.healthy !== false
    )

    if (healthyKeys.length === 0) {
      console.warn('⚠️ 所有密钥都不可用，重置健康状态')
      this.resetKeyHealth()
      return this.keyPool[0]
    }

    // 使用轮询策略选择密钥
    const selectedKey = healthyKeys[this.currentKeyIndex % healthyKeys.length]

    // 增加使用计数
    const currentCount = this.keyUsageCount.get(selectedKey) || 0
    this.keyUsageCount.set(selectedKey, currentCount + 1)

    return selectedKey
  }

  /**
   * 轮换密钥
   */
  rotateKey() {
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keyPool.length
    this.lastKeyRotation = Date.now()
    console.log(`🔄 密钥轮换到索引: ${this.currentKeyIndex}`)
  }

  /**
   * 重置密钥健康状态
   */
  resetKeyHealth() {
    this.keyPool.forEach(key => {
      this.keyHealthStatus.set(key, { healthy: true, lastError: null, errorCount: 0 })
    })
    console.log('🔄 重置所有密钥健康状态')
  }

  /**
   * 标记密钥为不健康
   * @param {string} apiKey - API密钥
   * @param {Error} error - 错误信息
   */
  markKeyUnhealthy(apiKey, error) {
    const status = this.keyHealthStatus.get(apiKey) || { healthy: true, lastError: null, errorCount: 0 }
    status.errorCount += 1
    status.lastError = error.message

    // 如果错误次数超过3次，标记为不健康
    if (status.errorCount >= 3) {
      status.healthy = false
      console.warn(`❌ 密钥 ${apiKey.substring(0, 10)}... 标记为不健康`)
    }

    this.keyHealthStatus.set(apiKey, status)
  }

  /**
   * 设置API密钥
   * @param {string} apiKey - API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey
  }

  /**
   * 设置基础URL
   * @param {string} baseURL - 基础URL
   */
  setBaseURL(baseURL) {
    this.baseURL = baseURL
  }

  /**
   * 创建HTTP客户端
   * @returns {Object} axios实例
   */
  createClient() {
    const currentKey = this.getCurrentApiKey()
    return axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${currentKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Creative-Platform/1.0.0'
      }
    })
  }

  /**
   * 测试API连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    const currentKey = this.getCurrentApiKey()

    try {
      if (!currentKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()

      // 尝试获取模型列表来测试连接
      const response = await client.get('/models')

      return {
        success: true,
        message: '连接成功',
        data: {
          modelsCount: response.data?.data?.length || 0,
          timestamp: new Date().toISOString(),
          currentKey: currentKey.substring(0, 10) + '...'
        }
      }
    } catch (error) {
      // 标记当前密钥为不健康
      this.markKeyUnhealthy(currentKey, error)

      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 获取可用模型列表
   * @returns {Promise<Array>} 模型列表
   */
  async getModels() {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      const response = await client.get('/models')
      
      return {
        success: true,
        data: response.data?.data || []
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 发送聊天请求
   * @param {Object} params - 请求参数
   * @param {string} params.model - 模型名称
   * @param {Array} params.messages - 消息数组
   * @param {number} params.temperature - 温度参数
   * @param {number} params.max_tokens - 最大token数
   * @param {boolean} params.stream - 是否流式输出
   * @returns {Promise<Object>} 响应结果
   */
  async chat(params) {
    const currentKey = this.getCurrentApiKey()

    try {
      if (!currentKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()

      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: params.stream || false,
        ...params.extra
      }

      const response = await client.post('/chat/completions', requestData)

      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      // 标记当前密钥为不健康
      this.markKeyUnhealthy(currentKey, error)

      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 流式聊天请求
   * @param {Object} params - 请求参数
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   * @returns {Promise<void>}
   */
  async streamChat(params, onMessage, onError, onComplete) {
    const currentKey = this.getCurrentApiKey()

    try {
      if (!currentKey) {
        throw new Error('API密钥未设置')
      }

      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: true,
        ...params.extra
      }

      // 使用 fetch 进行流式请求，兼容浏览器环境
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Creative-Platform/1.0.0'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            onComplete?.()
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              if (data === '[DONE]') {
                onComplete?.()
                return
              }
              if (data) {
                try {
                  const parsed = JSON.parse(data)
                  onMessage?.(parsed)
                } catch (e) {
                  // 忽略解析错误
                  console.warn('解析流式数据失败:', e, data)
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

    } catch (error) {
      console.error('流式聊天错误:', error)
      // 标记当前密钥为不健康
      this.markKeyUnhealthy(currentKey, error)
      onError?.(error)
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data
      
      switch (status) {
        case 401:
          return 'API密钥无效或已过期'
        case 403:
          return '访问被拒绝，请检查API密钥权限'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return '服务器内部错误'
        case 502:
        case 503:
        case 504:
          return '服务暂时不可用，请稍后重试'
        default:
          return data?.error?.message || data?.message || `HTTP ${status} 错误`
      }
    } else if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    } else if (error.code === 'ENOTFOUND') {
      return '无法连接到服务器，请检查网络'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 获取预设的模型配置
   * @returns {Array} 模型配置列表
   */
  getPresetModels() {
    return [
      {
        id: 'deepseek-ai/DeepSeek-V3',
        name: 'DeepSeek V3',
        description: 'DeepSeek最新版本，强大的推理能力',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-V3-0324',
        name: 'DeepSeek V3 (0324)',
        description: 'DeepSeek V3 3月24日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1',
        name: 'DeepSeek R1',
        description: 'DeepSeek推理专用模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1-0528',
        name: 'DeepSeek R1 (0528)',
        description: 'DeepSeek R1 5月28日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'moonshot/Kimi-K2-Instruct',
        name: 'Kimi K2',
        description: 'Moonshot AI的Kimi K2指令模型',
        maxTokens: 16384,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8',
        name: 'Qwen3 Coder 480B',
        description: '通义千问3代码专用大模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
        name: 'Qwen3 235B',
        description: '通义千问3指令模型',
        maxTokens: 32768,
        supportsFunctions: true
      }
    ]
  }
  /**
   * 获取密钥池状态
   * @returns {Object} 密钥池状态信息
   */
  getKeyPoolStatus() {
    const healthyKeys = this.keyPool.filter(key =>
      this.keyHealthStatus.get(key)?.healthy !== false
    )

    const keyStats = this.keyPool.map(key => {
      const status = this.keyHealthStatus.get(key)
      const usage = this.keyUsageCount.get(key)
      return {
        key: key.substring(0, 10) + '...',
        healthy: status?.healthy || false,
        errorCount: status?.errorCount || 0,
        usageCount: usage || 0,
        lastError: status?.lastError
      }
    })

    return {
      totalKeys: this.keyPool.length,
      healthyKeys: healthyKeys.length,
      currentKeyIndex: this.currentKeyIndex,
      lastRotation: new Date(this.lastKeyRotation).toLocaleString(),
      keyStats: keyStats.slice(0, 10) // 只显示前10个密钥的状态
    }
  }

  /**
   * 手动轮换到下一个健康密钥
   * @returns {string} 新的当前密钥
   */
  forceKeyRotation() {
    this.rotateKey()
    const newKey = this.getCurrentApiKey()
    console.log(`🔄 手动轮换密钥到: ${newKey.substring(0, 10)}...`)
    return newKey
  }

  /**
   * 重置所有密钥健康状态
   */
  resetAllKeyHealth() {
    this.resetKeyHealth()
    console.log('🔄 已重置所有密钥健康状态')
  }
}

// 创建单例实例
const targonApi = new TargonApiService()

export default targonApi
export { TargonApiService }
