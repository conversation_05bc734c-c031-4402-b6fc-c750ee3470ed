/**
 * Targon API 服务类
 * 提供与 Targon API 的集成功能
 */

import axios from 'axios'

class TargonApiService {
  constructor() {
    this.baseURL = 'https://api.targon.com/v1'
    this.apiKey = null
    this.timeout = 60000
    this.maxRetries = 3
  }

  /**
   * 设置API密钥
   * @param {string} apiKey - API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey
  }

  /**
   * 设置基础URL
   * @param {string} baseURL - 基础URL
   */
  setBaseURL(baseURL) {
    this.baseURL = baseURL
  }

  /**
   * 创建HTTP客户端
   * @returns {Object} axios实例
   */
  createClient() {
    return axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Creative-Platform/1.0.0'
      }
    })
  }

  /**
   * 测试API连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      
      // 尝试获取模型列表来测试连接
      const response = await client.get('/models')
      
      return {
        success: true,
        message: '连接成功',
        data: {
          modelsCount: response.data?.data?.length || 0,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 获取可用模型列表
   * @returns {Promise<Array>} 模型列表
   */
  async getModels() {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      const response = await client.get('/models')
      
      return {
        success: true,
        data: response.data?.data || []
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 发送聊天请求
   * @param {Object} params - 请求参数
   * @param {string} params.model - 模型名称
   * @param {Array} params.messages - 消息数组
   * @param {number} params.temperature - 温度参数
   * @param {number} params.max_tokens - 最大token数
   * @param {boolean} params.stream - 是否流式输出
   * @returns {Promise<Object>} 响应结果
   */
  async chat(params) {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      
      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: params.stream || false,
        ...params.extra
      }

      const response = await client.post('/chat/completions', requestData)
      
      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 流式聊天请求
   * @param {Object} params - 请求参数
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   * @returns {Promise<void>}
   */
  async streamChat(params, onMessage, onError, onComplete) {
    try {
      if (!this.apiKey) {
        throw new Error('API密钥未设置')
      }

      const client = this.createClient()
      
      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 2048,
        stream: true,
        ...params.extra
      }

      const response = await client.post('/chat/completions', requestData, {
        responseType: 'stream'
      })

      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              onComplete?.()
              return
            }
            try {
              const parsed = JSON.parse(data)
              onMessage?.(parsed)
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      })

      response.data.on('error', (error) => {
        onError?.(error)
      })

      response.data.on('end', () => {
        onComplete?.()
      })

    } catch (error) {
      onError?.(error)
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data
      
      switch (status) {
        case 401:
          return 'API密钥无效或已过期'
        case 403:
          return '访问被拒绝，请检查API密钥权限'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return '服务器内部错误'
        case 502:
        case 503:
        case 504:
          return '服务暂时不可用，请稍后重试'
        default:
          return data?.error?.message || data?.message || `HTTP ${status} 错误`
      }
    } else if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    } else if (error.code === 'ENOTFOUND') {
      return '无法连接到服务器，请检查网络'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 获取预设的模型配置
   * @returns {Array} 模型配置列表
   */
  getPresetModels() {
    return [
      {
        id: 'deepseek-ai/DeepSeek-V3',
        name: 'DeepSeek V3',
        description: 'DeepSeek最新版本，强大的推理能力',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-V3-0324',
        name: 'DeepSeek V3 (0324)',
        description: 'DeepSeek V3 3月24日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1',
        name: 'DeepSeek R1',
        description: 'DeepSeek推理专用模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'deepseek-ai/DeepSeek-R1-0528',
        name: 'DeepSeek R1 (0528)',
        description: 'DeepSeek R1 5月28日版本',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'moonshot/Kimi-K2-Instruct',
        name: 'Kimi K2',
        description: 'Moonshot AI的Kimi K2指令模型',
        maxTokens: 16384,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8',
        name: 'Qwen3 Coder 480B',
        description: '通义千问3代码专用大模型',
        maxTokens: 32768,
        supportsFunctions: true
      },
      {
        id: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
        name: 'Qwen3 235B',
        description: '通义千问3指令模型',
        maxTokens: 32768,
        supportsFunctions: true
      }
    ]
  }
}

// 创建单例实例
const targonApi = new TargonApiService()

export default targonApi
export { TargonApiService }
