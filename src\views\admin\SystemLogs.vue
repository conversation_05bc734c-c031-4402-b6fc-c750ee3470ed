<template>
  <div class="system-logs">
    <div class="page-header">
      <h1>系统日志</h1>
      <p>查看和管理系统操作日志</p>
    </div>

    <!-- 日志统计 -->
    <div class="log-stats">
      <div class="stat-card">
        <div class="stat-icon info">ℹ️</div>
        <div class="stat-content">
          <div class="stat-number">{{ logStats.info }}</div>
          <div class="stat-label">信息日志</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon warning">⚠️</div>
        <div class="stat-content">
          <div class="stat-number">{{ logStats.warning }}</div>
          <div class="stat-label">警告日志</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon error">❌</div>
        <div class="stat-content">
          <div class="stat-number">{{ logStats.error }}</div>
          <div class="stat-label">错误日志</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon success">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ logStats.success }}</div>
          <div class="stat-label">成功日志</div>
        </div>
      </div>
    </div>

    <!-- 日志过滤器 -->
    <div class="log-filters">
      <div class="filter-group">
        <label>日志级别:</label>
        <select v-model="filters.level" @change="applyFilters" class="filter-select">
          <option value="">全部</option>
          <option value="info">信息</option>
          <option value="warning">警告</option>
          <option value="error">错误</option>
          <option value="success">成功</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>时间范围:</label>
        <select v-model="filters.timeRange" @change="applyFilters" class="filter-select">
          <option value="1h">最近1小时</option>
          <option value="24h">最近24小时</option>
          <option value="7d">最近7天</option>
          <option value="30d">最近30天</option>
          <option value="all">全部</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>搜索:</label>
        <input 
          type="text" 
          v-model="filters.search" 
          @input="applyFilters"
          placeholder="搜索日志内容..."
          class="filter-input"
        />
      </div>
      
      <div class="filter-actions">
        <button class="btn btn-primary" @click="refreshLogs" :disabled="loading">
          {{ loading ? '刷新中...' : '刷新' }}
        </button>
        <button class="btn btn-warning" @click="exportLogs">
          导出日志
        </button>
        <button class="btn btn-danger" @click="clearLogs">
          清空日志
        </button>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="logs-container">
      <div class="logs-header">
        <h3>日志记录 ({{ filteredLogs.length }} 条)</h3>
        <div class="auto-refresh">
          <label>
            <input type="checkbox" v-model="autoRefresh" @change="toggleAutoRefresh">
            自动刷新
          </label>
        </div>
      </div>
      
      <div class="logs-list" v-loading="loading">
        <div 
          v-for="log in paginatedLogs" 
          :key="log.id" 
          class="log-item"
          :class="log.level"
        >
          <div class="log-icon">
            {{ getLogIcon(log.level) }}
          </div>
          <div class="log-content">
            <div class="log-message">{{ log.message }}</div>
            <div class="log-details">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-source" v-if="log.source">{{ log.source }}</span>
              <span class="log-user" v-if="log.user">用户: {{ log.user }}</span>
            </div>
            <div v-if="log.details" class="log-extra">
              <details>
                <summary>详细信息</summary>
                <pre>{{ JSON.stringify(log.details, null, 2) }}</pre>
              </details>
            </div>
          </div>
          <div class="log-actions">
            <button class="btn-small btn-info" @click="viewLogDetail(log)">
              详情
            </button>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="no-logs">
          <p>暂无日志记录</p>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          class="btn btn-info btn-small"
          @click="currentPage--" 
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <button 
          class="btn btn-info btn-small"
          @click="currentPage++" 
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <div v-if="showLogDetail" class="log-detail-modal" @click="closeLogDetail">
      <div class="log-detail-content" @click.stop>
        <div class="modal-header">
          <h3>日志详情</h3>
          <button class="close-btn" @click="closeLogDetail">×</button>
        </div>
        <div class="modal-body" v-if="selectedLog">
          <div class="detail-item">
            <label>级别:</label>
            <span class="log-level" :class="selectedLog.level">
              {{ getLogLevelText(selectedLog.level) }}
            </span>
          </div>
          <div class="detail-item">
            <label>时间:</label>
            <span>{{ formatFullTime(selectedLog.timestamp) }}</span>
          </div>
          <div class="detail-item">
            <label>消息:</label>
            <span>{{ selectedLog.message }}</span>
          </div>
          <div class="detail-item" v-if="selectedLog.source">
            <label>来源:</label>
            <span>{{ selectedLog.source }}</span>
          </div>
          <div class="detail-item" v-if="selectedLog.user">
            <label>用户:</label>
            <span>{{ selectedLog.user }}</span>
          </div>
          <div class="detail-item" v-if="selectedLog.details">
            <label>详细信息:</label>
            <pre class="detail-json">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const showLogDetail = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const pageSize = 20

// 日志数据
const logs = ref([])
const logStats = reactive({
  info: 0,
  warning: 0,
  error: 0,
  success: 0
})

// 过滤器
const filters = reactive({
  level: '',
  timeRange: '24h',
  search: ''
})

// 定时器
let refreshTimer = null

// 计算属性
const filteredLogs = computed(() => {
  let result = [...logs.value]
  
  // 按级别过滤
  if (filters.level) {
    result = result.filter(log => log.level === filters.level)
  }
  
  // 按时间范围过滤
  if (filters.timeRange !== 'all') {
    const now = Date.now()
    const timeMap = {
      '1h': 3600000,
      '24h': 86400000,
      '7d': 604800000,
      '30d': 2592000000
    }
    const timeLimit = now - timeMap[filters.timeRange]
    result = result.filter(log => log.timestamp >= timeLimit)
  }
  
  // 按搜索关键词过滤
  if (filters.search) {
    const search = filters.search.toLowerCase()
    result = result.filter(log => 
      log.message.toLowerCase().includes(search) ||
      (log.source && log.source.toLowerCase().includes(search)) ||
      (log.user && log.user.toLowerCase().includes(search))
    )
  }
  
  return result.sort((a, b) => b.timestamp - a.timestamp)
})

const totalPages = computed(() => Math.ceil(filteredLogs.value.length / pageSize))

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredLogs.value.slice(start, end)
})

// 方法
const generateMockLogs = () => {
  const levels = ['info', 'warning', 'error', 'success']
  const sources = ['API', '用户管理', '密钥管理', '系统监控', '认证']
  const users = ['admin', 'user1', 'user2', null]
  const messages = {
    info: ['用户登录成功', '系统启动完成', '配置更新', '数据同步完成'],
    warning: ['API调用频率过高', '内存使用率达到80%', '密钥即将过期', '存储空间不足'],
    error: ['API调用失败', '数据库连接错误', '认证失败', '文件上传失败'],
    success: ['密钥测试成功', '用户注册成功', '数据备份完成', '系统更新成功']
  }
  
  const newLogs = []
  for (let i = 0; i < 100; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)]
    const log = {
      id: Date.now() + i,
      level,
      message: messages[level][Math.floor(Math.random() * messages[level].length)],
      timestamp: Date.now() - Math.random() * 2592000000, // 最近30天
      source: sources[Math.floor(Math.random() * sources.length)],
      user: users[Math.floor(Math.random() * users.length)],
      details: Math.random() > 0.7 ? {
        ip: '192.168.1.' + Math.floor(Math.random() * 255),
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        requestId: 'req_' + Math.random().toString(36).substr(2, 9)
      } : null
    }
    newLogs.push(log)
  }
  
  logs.value = newLogs
  updateLogStats()
}

const updateLogStats = () => {
  logStats.info = logs.value.filter(log => log.level === 'info').length
  logStats.warning = logs.value.filter(log => log.level === 'warning').length
  logStats.error = logs.value.filter(log => log.level === 'error').length
  logStats.success = logs.value.filter(log => log.level === 'success').length
}

const getLogIcon = (level) => {
  const icons = {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌',
    success: '✅'
  }
  return icons[level] || 'ℹ️'
}

const getLogLevelText = (level) => {
  const texts = {
    info: '信息',
    warning: '警告',
    error: '错误',
    success: '成功'
  }
  return texts[level] || '未知'
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }
}

const formatFullTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const applyFilters = () => {
  currentPage.value = 1
}

const refreshLogs = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    generateMockLogs()
    ElMessage.success('日志刷新成功')
  } catch (error) {
    ElMessage.error('日志刷新失败')
  } finally {
    loading.value = false
  }
}

const exportLogs = () => {
  try {
    const dataStr = JSON.stringify(filteredLogs.value, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system_logs_${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('日志导出成功')
  } catch (error) {
    ElMessage.error('日志导出失败')
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    logs.value = []
    updateLogStats()
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消操作
  }
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  showLogDetail.value = true
}

const closeLogDetail = () => {
  showLogDetail.value = false
  selectedLog.value = null
}

const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) clearInterval(refreshTimer)
  refreshTimer = setInterval(() => {
    generateMockLogs()
  }, 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  generateMockLogs()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.system-logs {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.log-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;

  .stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.info {
      background: linear-gradient(135deg, #3498db, #2980b9);
    }

    &.warning {
      background: linear-gradient(135deg, #f39c12, #e67e22);
    }

    &.error {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    &.success {
      background: linear-gradient(135deg, #27ae60, #229954);
    }
  }

  .stat-content {
    .stat-number {
      font-size: 1.8rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

.log-filters {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;

  label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
  }
}

.filter-select,
.filter-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;

  &:focus {
    outline: none;
    border-color: #409eff;
  }
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

.logs-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h3 {
    margin: 0;
    color: #2c3e50;
  }
}

.auto-refresh {
  label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    color: #7f8c8d;
  }
}

.logs-list {
  max-height: 600px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  &.error {
    border-left: 4px solid #e74c3c;
  }

  &.warning {
    border-left: 4px solid #f39c12;
  }

  &.success {
    border-left: 4px solid #27ae60;
  }

  &.info {
    border-left: 4px solid #3498db;
  }
}

.log-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  flex-shrink: 0;
}

.log-content {
  flex: 1;

  .log-message {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  .log-details {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #7f8c8d;
    flex-wrap: wrap;
  }

  .log-extra {
    margin-top: 0.5rem;

    details {
      summary {
        cursor: pointer;
        color: #409eff;
        font-size: 0.8rem;
      }

      pre {
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.7rem;
        margin-top: 0.5rem;
        overflow-x: auto;
      }
    }
  }
}

.log-actions {
  flex-shrink: 0;
}

.no-logs {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f3f4;
}

.page-info {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* 日志详情模态框 */
.log-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.log-detail-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f3f4;

  h3 {
    margin: 0;
    color: #2c3e50;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    &:hover {
      background: #f1f3f4;
    }
  }
}

.modal-body {
  padding: 1.5rem;
}

.detail-item {
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }

  span {
    color: #7f8c8d;
  }

  .log-level {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;

    &.info {
      background: #e3f2fd;
      color: #1976d2;
    }

    &.warning {
      background: #fff3e0;
      color: #f57c00;
    }

    &.error {
      background: #ffebee;
      color: #d32f2f;
    }

    &.success {
      background: #e8f5e8;
      color: #388e3c;
    }
  }
}

.detail-json {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  overflow-x: auto;
  margin: 0;
  border: 1px solid #e1e8ed;
}

/* 自定义按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
  text-align: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 60px;
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-info {
  background-color: #909399;
  border-color: #909399;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

.btn-warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #ebb563;
  border-color: #ebb563;
}

.btn-danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #f78989;
  border-color: #f78989;
}

@media (max-width: 768px) {
  .system-logs {
    padding: 1rem;
  }

  .log-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .log-filters {
    flex-direction: column;
    align-items: stretch;

    .filter-actions {
      margin-left: 0;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .logs-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .log-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .log-detail-content {
    width: 95%;
    margin: 1rem;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .log-stats {
    grid-template-columns: 1fr;
  }

  .filter-actions .btn {
    width: 100%;
  }
}
</style>
