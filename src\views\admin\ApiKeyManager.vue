<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">API密钥管理</h1>
        <p class="text-gray-600 mt-2">管理OpenRouter API密钥，支持轮询和健康检查</p>
      </div>

      <!-- 配置概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l6.879-6.88a6 6 0 018.242-.002z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">总密钥数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ config.totalKeys }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">可用密钥</p>
              <p class="text-2xl font-semibold text-gray-900">{{ config.activeKeys }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">健康检查间隔</p>
              <p class="text-2xl font-semibold text-gray-900">{{ Math.round(config.healthCheckInterval / 60000) }}分钟</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">轮询状态</p>
              <p class="text-2xl font-semibold text-gray-900">{{ config.keyRotationEnabled ? '启用' : '禁用' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">快速操作</h2>
        <div class="flex flex-wrap gap-4">
          <button
            @click="refreshStats"
            :disabled="loading"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            {{ loading ? '刷新中...' : '刷新状态' }}
          </button>
          
          <button
            @click="performHealthCheck"
            :disabled="healthCheckLoading"
            class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
          >
            {{ healthCheckLoading ? '检查中...' : '健康检查' }}
          </button>
          
          <button
            @click="rotateKey"
            class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
          >
            轮换密钥
          </button>
          
          <button
            @click="resetAllKeys"
            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            重置所有密钥
          </button>
          
          <button
            @click="showAddKeyDialog = true"
            class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600"
          >
            添加密钥
          </button>
        </div>
      </div>

      <!-- 密钥列表 -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-xl font-semibold">密钥状态</h2>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">密钥</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">统计</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后使用</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="keyStats in keyStatsList" :key="keyStats.index" :class="{ 'bg-blue-50': keyStats.isCurrent }">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">#{{ keyStats.index }}</span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ keyStats.keyPreview }}</div>
                      <div class="text-sm text-gray-500">{{ keyStats.isCurrent ? '当前使用' : '备用密钥' }}</div>
                    </div>
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    keyStats.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  ]">
                    {{ keyStats.isActive ? '可用' : '不可用' }}
                  </span>
                  <div class="text-xs text-gray-500 mt-1">
                    失败: {{ keyStats.failureCount }}
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>总请求: {{ keyStats.totalRequests }}</div>
                  <div>成功率: {{ keyStats.successRate }}%</div>
                  <div v-if="keyStats.responseTime > 0">响应时间: {{ keyStats.responseTime }}ms</div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div v-if="keyStats.lastUsed">{{ formatTime(keyStats.lastUsed) }}</div>
                  <div v-else>从未使用</div>
                  <div v-if="keyStats.lastSuccess" class="text-green-600">
                    成功: {{ formatTime(keyStats.lastSuccess) }}
                  </div>
                  <div v-if="keyStats.lastFailure" class="text-red-600">
                    失败: {{ formatTime(keyStats.lastFailure) }}
                  </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    v-if="keyStatsList.length > 1"
                    @click="removeKey(keyStats.index - 1)"
                    class="text-red-600 hover:text-red-900"
                  >
                    删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 添加密钥对话框 -->
      <el-dialog v-model="showAddKeyDialog" title="添加API密钥" width="500px">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
            <input
              v-model="newApiKey"
              type="password"
              placeholder="sk-or-v1-..."
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div class="text-sm text-gray-500">
            请输入有效的OpenRouter API密钥。密钥将被加密存储。
          </div>
        </div>
        
        <template #footer>
          <div class="flex justify-end space-x-3">
            <button
              @click="showAddKeyDialog = false"
              class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              @click="addKey"
              :disabled="!newApiKey.trim()"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
            >
              添加
            </button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElDialog } from 'element-plus'
import { 
  getApiKeyStats, 
  getApiConfig, 
  addApiKey, 
  removeApiKey, 
  rotateApiKey, 
  performHealthCheck as performApiHealthCheck,
  resetAllApiKeys 
} from '@/services/openrouterApi.js'

// 响应式数据
const loading = ref(false)
const healthCheckLoading = ref(false)
const keyStatsList = ref([])
const config = ref({})
const showAddKeyDialog = ref(false)
const newApiKey = ref('')

// 刷新统计信息
const refreshStats = async () => {
  loading.value = true
  try {
    keyStatsList.value = getApiKeyStats()
    config.value = getApiConfig()
  } catch (error) {
    ElMessage.error('获取统计信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 执行健康检查
const performHealthCheck = async () => {
  healthCheckLoading.value = true
  try {
    await performApiHealthCheck()
    ElMessage.success('健康检查完成')
    await refreshStats()
  } catch (error) {
    console.error('健康检查错误:', error)
    ElMessage.error('健康检查失败: ' + (error.message || '网络连接错误'))
  } finally {
    healthCheckLoading.value = false
  }
}

// 轮换密钥
const rotateKey = () => {
  try {
    rotateApiKey()
    ElMessage.success('密钥已轮换')
    refreshStats()
  } catch (error) {
    ElMessage.error('轮换密钥失败: ' + error.message)
  }
}

// 重置所有密钥
const resetAllKeys = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有密钥状态吗？这将清除失败计数并重新激活所有密钥。', '确认重置', {
      type: 'warning'
    })
    
    resetAllApiKeys()
    ElMessage.success('所有密钥状态已重置')
    refreshStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败: ' + error.message)
    }
  }
}

// 添加密钥
const addKey = async () => {
  if (!newApiKey.value.trim()) {
    ElMessage.error('请输入API密钥')
    return
  }
  
  try {
    addApiKey(newApiKey.value.trim())
    ElMessage.success('API密钥添加成功')
    showAddKeyDialog.value = false
    newApiKey.value = ''
    refreshStats()
  } catch (error) {
    ElMessage.error('添加密钥失败: ' + error.message)
  }
}

// 删除密钥
const removeKey = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个API密钥吗？', '确认删除', {
      type: 'warning'
    })
    
    removeApiKey(index)
    ElMessage.success('API密钥删除成功')
    refreshStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除密钥失败: ' + error.message)
    }
  }
}

// 格式化时间
const formatTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
  refreshStats()
  
  // 定期刷新统计信息
  setInterval(refreshStats, 30000) // 30秒刷新一次
})
</script>

<style scoped>
/* 组件样式 */
</style>
