// 全局配置文件

// 应用配置
export const APP_CONFIG = {
  // 应用信息
  name: 'AI创作助手平台',
  version: '1.0.0',
  description: '集AI聊天、AI绘画、内容创作和社交分享于一体的综合性创作平台',
  
  // API配置
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
    timeout: 30000,
    retryTimes: 3,
  },
  
  // 存储配置
  storage: {
    tokenKey: 'ai_creative_token',
    userKey: 'ai_creative_user',
    settingsKey: 'ai_creative_settings',
    chatHistoryKey: 'ai_creative_chat_history',
  },
  
  // 分页配置
  pagination: {
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
  },
  
  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    imageMaxWidth: 2048,
    imageMaxHeight: 2048,
  },
  
  // AI配置
  ai: {
    // 聊天配置
    chat: {
      maxMessages: 100,
      maxTokens: 4000,
      temperature: 0.7,
      models: [
        { value: 'gemini-2.5-flash', label: 'Gemini 2.5 Flash (推荐)' },
        { value: 'gemini-2.5-flash-lite', label: 'Gemini 2.5 Flash Lite (极速)' },
        { value: 'gemini-2.5-pro', label: 'Gemini 2.5 Pro (最强)' },
        { value: 'openai', label: 'OpenAI GPT-4o Mini' },
        { value: 'llama', label: 'Llama 3.3 70B' },
        { value: 'mistral', label: 'Mistral Small 3' },
      ],
    },
    
    // 绘画配置
    drawing: {
      maxPromptLength: 500,
      defaultSize: '512x512',
      sizes: [
        { value: '256x256', label: '256×256' },
        { value: '512x512', label: '512×512' },
        { value: '1024x1024', label: '1024×1024' },
      ],
      styles: [
        { value: 'realistic', label: '写实风格' },
        { value: 'cartoon', label: '卡通风格' },
        { value: 'anime', label: '动漫风格' },
        { value: 'oil_painting', label: '油画风格' },
        { value: 'watercolor', label: '水彩风格' },
      ],
    },
  },
  
  // 主题配置
  theme: {
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399',
  },
  
  // 路由配置
  router: {
    mode: 'history',
    base: '/',
  },
  
  // 开发配置
  dev: {
    enableMock: import.meta.env.VITE_ENABLE_MOCK === 'true',
    enableDevtools: import.meta.env.DEV,
  },
}

// 环境变量
export const ENV = {
  NODE_ENV: import.meta.env.MODE,
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  isTest: import.meta.env.MODE === 'test',
}

// 常量定义
export const CONSTANTS = {
  // 用户角色
  USER_ROLES: {
    ADMIN: 'admin',
    USER: 'user',
    VIP: 'vip',
  },
  
  // 消息类型
  MESSAGE_TYPES: {
    TEXT: 'text',
    IMAGE: 'image',
    FILE: 'file',
    SYSTEM: 'system',
  },
  
  // 作品状态
  ARTWORK_STATUS: {
    DRAFT: 'draft',
    PUBLISHED: 'published',
    PRIVATE: 'private',
    DELETED: 'deleted',
  },
  
  // 聊天状态
  CHAT_STATUS: {
    WAITING: 'waiting',
    TYPING: 'typing',
    COMPLETED: 'completed',
    ERROR: 'error',
  },
  
  // 绘画状态
  DRAWING_STATUS: {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
  },
}

// 默认设置
export const DEFAULT_SETTINGS = {
  // 界面设置
  ui: {
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false,
    showNotifications: true,
  },
  
  // AI设置
  ai: {
    chatModel: 'gemini-2.5-flash',
    chatTemperature: 0.7,
    drawingStyle: 'realistic',
    drawingSize: '512x512',
  },
  
  // 隐私设置
  privacy: {
    shareArtworks: true,
    showOnlineStatus: true,
    allowComments: true,
  },
}

export default APP_CONFIG
