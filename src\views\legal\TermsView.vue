<template>
  <div class="legal-page">
    <!-- 返回按钮 -->
    <div class="back-button-container">
      <button @click="goBack" class="back-button">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>返回</span>
      </button>
    </div>

    <div class="container">
      <!-- 页面头部 -->
      <div class="legal-header">
        <div class="header-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="page-title">用户协议</h1>
        <p class="page-subtitle">AI创作助手平台服务条款</p>
        <div class="update-info">
          <span class="update-label">最后更新</span>
          <span class="update-date">2025年1月20日</span>
        </div>
      </div>

      <!-- 目录导航 -->
      <div class="table-of-contents">
        <h3 class="toc-title">目录</h3>
        <div class="toc-list">
          <a href="#section-1" class="toc-item" @click="scrollToSection('section-1')">1. 服务条款</a>
          <a href="#section-2" class="toc-item" @click="scrollToSection('section-2')">2. 服务内容</a>
          <a href="#section-3" class="toc-item" @click="scrollToSection('section-3')">3. 用户权利与义务</a>
          <a href="#section-4" class="toc-item" @click="scrollToSection('section-4')">4. 内容规范</a>
          <a href="#section-5" class="toc-item" @click="scrollToSection('section-5')">5. 知识产权</a>
          <a href="#section-6" class="toc-item" @click="scrollToSection('section-6')">6. 免责声明</a>
          <a href="#section-7" class="toc-item" @click="scrollToSection('section-7')">7. 服务变更与终止</a>
          <a href="#section-8" class="toc-item" @click="scrollToSection('section-8')">8. 争议解决</a>
          <a href="#section-9" class="toc-item" @click="scrollToSection('section-9')">9. 协议修改</a>
          <a href="#section-10" class="toc-item" @click="scrollToSection('section-10')">10. 联系我们</a>
        </div>
      </div>

      <div class="legal-content">
        <section id="section-1" class="legal-section">
          <div class="section-header">
            <h2>1. 服务条款</h2>
            <div class="section-icon">📋</div>
          </div>
          <div class="highlight-box welcome-box">
            <h4>欢迎使用AI创作助手平台</h4>
            <p>感谢您选择我们的服务！在开始您的创作之旅前，请仔细阅读本协议。</p>
          </div>
          <p>欢迎使用AI创作助手平台（以下简称"本平台"）。在使用本平台提供的服务前，请您仔细阅读并充分理解本用户协议的各项条款。</p>
          <p>您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</p>

          <div class="important-notice">
            <div class="notice-icon">⚠️</div>
            <div class="notice-content">
              <strong>重要提醒：</strong>本协议具有法律效力，请务必仔细阅读每一条款。如有疑问，请联系我们的客服团队。
            </div>
          </div>
        </section>

        <section id="section-2" class="legal-section">
          <div class="section-header">
            <h2>2. 服务内容</h2>
            <div class="section-icon">🎨</div>
          </div>
          <p>本平台为用户提供以下创新服务：</p>
          <div class="service-grid">
            <div class="service-item">
              <div class="service-icon">💬</div>
              <h4>AI智能对话</h4>
              <p>与先进的AI模型进行自然对话，获得创意灵感和专业建议</p>
            </div>
            <div class="service-item">
              <div class="service-icon">🖼️</div>
              <h4>AI图像生成</h4>
              <p>通过文字描述生成高质量的艺术作品和创意图像</p>
            </div>
            <div class="service-item">
              <div class="service-icon">🌟</div>
              <h4>作品展示</h4>
              <p>展示和分享您的创作成果，与全球创作者交流</p>
            </div>
            <div class="service-item">
              <div class="service-icon">👥</div>
              <h4>社区交流</h4>
              <p>加入创作者社区，参与讨论，获得反馈和灵感</p>
            </div>
            <div class="service-item">
              <div class="service-icon">⚡</div>
              <h4>增值服务</h4>
              <p>享受更多高级功能和个性化定制服务</p>
            </div>
          </div>
        </section>

        <section id="section-3" class="legal-section">
          <div class="section-header">
            <h2>3. 用户权利与义务</h2>
            <div class="section-icon">⚖️</div>
          </div>

          <div class="rights-obligations">
            <div class="rights-section">
              <h3>3.1 您的权利</h3>
              <div class="rights-list">
                <div class="right-item">
                  <div class="right-icon">✅</div>
                  <div class="right-content">
                    <h4>服务享受权</h4>
                    <p>享受本平台提供的所有功能和服务</p>
                  </div>
                </div>
                <div class="right-item">
                  <div class="right-icon">🔒</div>
                  <div class="right-content">
                    <h4>知识产权</h4>
                    <p>对自己创作的内容享有完整的知识产权</p>
                  </div>
                </div>
                <div class="right-item">
                  <div class="right-icon">🛡️</div>
                  <div class="right-content">
                    <h4>隐私保护</h4>
                    <p>要求我们保护您的个人隐私和数据安全</p>
                  </div>
                </div>
                <div class="right-item">
                  <div class="right-icon">💬</div>
                  <div class="right-content">
                    <h4>意见反馈</h4>
                    <p>对平台服务提出意见、建议和改进要求</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="obligations-section">
              <h3>3.2 您的义务</h3>
              <div class="obligations-list">
                <div class="obligation-item">
                  <div class="obligation-icon">📜</div>
                  <div class="obligation-content">
                    <h4>遵守法律</h4>
                    <p>遵守国家法律法规和本协议的所有条款</p>
                  </div>
                </div>
                <div class="obligation-item">
                  <div class="obligation-icon">🚫</div>
                  <div class="obligation-content">
                    <h4>合规使用</h4>
                    <p>不得利用平台从事任何违法违规活动</p>
                  </div>
                </div>
                <div class="obligation-item">
                  <div class="obligation-icon">🔐</div>
                  <div class="obligation-content">
                    <h4>账户安全</h4>
                    <p>保护账户安全，不得转让或共享账户</p>
                  </div>
                </div>
                <div class="obligation-item">
                  <div class="obligation-icon">🤝</div>
                  <div class="obligation-content">
                    <h4>尊重他人</h4>
                    <p>尊重他人的知识产权和合法权益</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section class="legal-section">
          <h2>4. 内容规范</h2>
          <p>用户在使用本平台时，不得创作、发布以下内容：</p>
          <ul>
            <li>违反国家法律法规的内容</li>
            <li>涉及政治敏感话题的内容</li>
            <li>包含暴力、色情、恐怖主义的内容</li>
            <li>侵犯他人知识产权的内容</li>
            <li>虚假、误导性信息</li>
            <li>垃圾信息或恶意营销内容</li>
          </ul>
        </section>

        <section class="legal-section">
          <h2>5. 知识产权</h2>
          <p>本平台尊重并保护知识产权。用户通过本平台创作的内容，其知识产权归用户所有。</p>
          <p>本平台的技术、软件、界面设计等知识产权归本平台所有，未经授权不得使用。</p>
        </section>

        <section class="legal-section">
          <h2>6. 免责声明</h2>
          <p>本平台提供的AI服务基于机器学习技术，生成的内容仅供参考，不保证完全准确。</p>
          <p>用户应对使用AI生成内容的后果承担责任，本平台不承担因此产生的任何损失。</p>
        </section>

        <section class="legal-section">
          <h2>7. 服务变更与终止</h2>
          <p>本平台有权根据业务需要调整服务内容，并会提前通知用户。</p>
          <p>如用户违反本协议，本平台有权暂停或终止提供服务。</p>
        </section>

        <section class="legal-section">
          <h2>8. 争议解决</h2>
          <p>本协议的解释和争议解决适用中华人民共和国法律。</p>
          <p>如发生争议，双方应友好协商解决；协商不成的，可向本平台所在地人民法院提起诉讼。</p>
        </section>

        <section class="legal-section">
          <h2>9. 协议修改</h2>
          <p>本平台有权根据法律法规变化和业务发展需要修改本协议。</p>
          <p>修改后的协议将在平台上公布，用户继续使用服务即视为同意修改后的协议。</p>
        </section>

        <section class="legal-section">
          <h2>10. 联系我们</h2>
          <p>如您对本协议有任何疑问，请通过以下方式联系我们：</p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>客服热线：400-123-4567</li>
            <li>在线客服：平台内客服系统</li>
          </ul>
        </section>
      </div>

      <div class="legal-footer">
        <div class="action-buttons">
          <button @click="goBack" class="btn-secondary">返回</button>
          <button @click="acceptAndContinue" class="btn-primary">我已阅读并同意</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'

const router = useRouter()
const route = useRoute()

const goBack = () => {
  if (route.query.from) {
    router.push(route.query.from)
  } else {
    router.go(-1)
  }
}

const acceptAndContinue = () => {
  ElMessage.success('感谢您阅读用户协议')
  goBack()
}

const scrollToSection = async (sectionId) => {
  await nextTick()
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.legal-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.back-button-container {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 100;

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50px;
    color: $text-color-primary;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      background: white;
      transform: translateX(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    svg {
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: translateX(-2px);
    }
  }
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 6rem $spacing-lg 4rem;

  @media (max-width: 768px) {
    padding: 5rem $spacing-md 3rem;
  }
}

.legal-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-primary;
  }

  .header-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: $gradient-primary;
    border-radius: 20px;
    margin-bottom: 1.5rem;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  }

  .page-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: $text-color-primary;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    font-size: 1.2rem;
    color: $text-color-secondary;
    margin-bottom: 2rem;
  }

  .update-info {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50px;
    font-size: 0.9rem;

    .update-label {
      color: $text-color-secondary;
    }

    .update-date {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

.table-of-contents {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);

  .toc-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: $text-color-primary;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '📑';
      font-size: 1.2rem;
    }
  }

  .toc-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .toc-item {
    display: block;
    padding: 0.75rem 1rem;
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    color: $text-color-primary;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      border-color: rgba(102, 126, 234, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }
  }
}

.legal-content {
  line-height: 1.8;
  color: $text-color-primary;

  .legal-section {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    scroll-margin-top: 100px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 2px solid rgba(102, 126, 234, 0.1);

      h2 {
        font-size: 1.8rem;
        font-weight: 700;
        color: $text-color-primary;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .section-icon {
        font-size: 1.5rem;
        padding: 0.5rem;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 12px;
      }
    }

    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: $text-color-primary;
      margin: 2rem 0 1rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: $text-color-primary;
      margin: 1rem 0 0.5rem 0;
    }

    p {
      margin-bottom: 1rem;
      text-align: justify;
      line-height: 1.7;
    }

    ul {
      margin: 1rem 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        position: relative;

        &::marker {
          color: $primary-color;
        }
      }
    }
  }
}

// 特殊组件样式
.highlight-box {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;

  &.welcome-box {
    text-align: center;

    h4 {
      color: $primary-color;
      margin-bottom: 0.5rem;
    }
  }
}

.important-notice {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  margin: 1.5rem 0;

  .notice-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }

  .notice-content {
    flex: 1;

    strong {
      color: #d97706;
    }
  }
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;

  .service-item {
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    }

    .service-icon {
      font-size: 2rem;
      margin-bottom: 1rem;
    }

    h4 {
      color: $primary-color;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.9rem;
      color: $text-color-secondary;
      margin: 0;
    }
  }
}

// 权利义务部分样式
.rights-obligations {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.rights-section, .obligations-section {
  h3 {
    color: $primary-color;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }
}

.rights-list, .obligations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.right-item, .obligation-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(4px);
  }

  .right-icon, .obligation-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .right-content, .obligation-content {
    flex: 1;

    h4 {
      margin: 0 0 0.25rem 0;
      font-size: 1rem;
      color: $text-color-primary;
    }

    p {
      margin: 0;
      font-size: 0.9rem;
      color: $text-color-secondary;
      line-height: 1.5;
    }
  }
}

.legal-footer {
  margin-top: $spacing-xl;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color-light;

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: $spacing-md;

    @media (max-width: 480px) {
      flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
      padding: 12px 32px;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      font-size: 1rem;
    }

    .btn-secondary {
      background: rgba(0, 0, 0, 0.05);
      color: $text-color-secondary;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .btn-primary {
      background: $gradient-primary;
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

@media (max-width: 768px) {
  .legal-header .page-title {
    font-size: 2rem;
  }

  .legal-content .legal-section h2 {
    font-size: 1.3rem;
  }
}
</style>
