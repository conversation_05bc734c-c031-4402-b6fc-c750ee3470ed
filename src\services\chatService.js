// 统一聊天服务 - 整合多个AI提供商
import { generateChatCompletion as pollinationsChat, generateChatCompletionStream as pollinationsStream } from './pollinationsApi.js'
import { generateChatCompletion as openrouterChat, generateChatCompletionStream as openrouterStream, handleOpenRouterError } from './openrouterApi.js'
import targonApi from './targonApi.js'
import { ElMessage } from 'element-plus'

// 判断模型提供商
function getProvider(modelId) {
  if (!modelId) return 'pollinations'

  // Targon 模型通常以组织名/模型名格式，如 deepseek-ai/DeepSeek-V3
  if (modelId.includes('deepseek-ai/') ||
      modelId.includes('moonshot/') ||
      modelId.includes('Qwen/') ||
      modelId.includes('targon:')) {
    return 'targon'
  }

  // OpenRouter 模型通常包含 :free 后缀
  if (modelId.includes('/') && modelId.includes(':free')) {
    return 'openrouter'
  }

  return 'pollinations'
}

// 统一聊天完成接口
export async function generateChatCompletion(messages, options = {}) {
  const provider = getProvider(options.model)

  try {
    switch (provider) {
      case 'openrouter':
        return await openrouterChat(messages, options)
      case 'targon':
        return await generateTargonChatCompletion(messages, options)
      case 'pollinations':
      default:
        return await pollinationsChat(messages, options)
    }
  } catch (error) {
    console.error(`${provider} 聊天失败:`, error)

    // 根据提供商处理错误
    if (provider === 'openrouter') {
      handleOpenRouterError(error)
    } else if (provider === 'targon') {
      ElMessage.error(error.message || 'Targon API 请求失败')
    } else {
      ElMessage.error(error.message || '聊天请求失败')
    }

    throw error
  }
}

// 统一流式聊天接口
export async function generateChatCompletionStream(messages, options = {}, onChunk) {
  const provider = getProvider(options.model)

  try {
    switch (provider) {
      case 'openrouter':
        return await openrouterStream(messages, options, onChunk)
      case 'targon':
        return await generateTargonChatCompletionStream(messages, options, onChunk)
      case 'pollinations':
      default:
        return await pollinationsStream(messages, options, onChunk)
    }
  } catch (error) {
    console.error(`${provider} 流式聊天失败:`, error)

    // 根据提供商处理错误
    if (provider === 'openrouter') {
      handleOpenRouterError(error)
    } else if (provider === 'targon') {
      ElMessage.error(error.message || 'Targon API 流式请求失败')
    } else {
      ElMessage.error(error.message || '流式聊天请求失败')
    }

    throw error
  }
}

// Targon 聊天完成
async function generateTargonChatCompletion(messages, options = {}) {
  // 确保 API 密钥已设置
  const apiKey = localStorage.getItem('targon_api_key')
  if (!apiKey) {
    throw new Error('Targon API 密钥未设置，请先在设置中配置')
  }

  targonApi.setApiKey(apiKey)

  const result = await targonApi.chat({
    model: options.model,
    messages: messages,
    temperature: options.temperature || 0.7,
    max_tokens: options.max_tokens || 2048,
    stream: false
  })

  if (!result.success) {
    throw new Error(result.message || 'Targon API 请求失败')
  }

  return result.data
}

// Targon 流式聊天完成
async function generateTargonChatCompletionStream(messages, options = {}, onChunk) {
  // 确保 API 密钥已设置
  const apiKey = localStorage.getItem('targon_api_key')
  if (!apiKey) {
    throw new Error('Targon API 密钥未设置，请先在设置中配置')
  }

  targonApi.setApiKey(apiKey)

  return new Promise((resolve, reject) => {
    targonApi.streamChat(
      {
        model: options.model,
        messages: messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2048
      },
      (chunk) => {
        // 处理流式数据块
        if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
          const content = chunk.choices[0].delta.content
          if (content) {
            onChunk?.(content)
          }
        }
      },
      (error) => {
        reject(error)
      },
      () => {
        resolve()
      }
    )
  })
}

// 获取模型信息
export async function getModelInfo(modelId) {
  const provider = getProvider(modelId)
  
  if (provider === 'openrouter') {
    // OpenRouter模型信息
    const parts = modelId.split('/')
    const providerName = parts[0]
    const modelName = parts[1]?.replace(':free', '') || modelId
    
    const providerMap = {
      'deepseek': 'DeepSeek',
      'qwen': 'Alibaba',
      'moonshotai': 'Moonshot AI',
      'google': 'Google',
      'mistralai': 'Mistral AI',
      'microsoft': 'Microsoft',
      'meta-llama': 'Meta',
      'tngtech': 'TNG Technology',
      'agentica-org': 'Agentica',
      'cognitivecomputations': 'Cognitive Computations',
      'thudm': 'Tsinghua University',
      'shisa-ai': 'Shisa AI',
      'tencent': 'Tencent',
      'nousresearch': 'Nous Research',
      'arliai': 'Arli AI',
      'rekaai': 'Reka AI',
      'sarvamai': 'Sarvam AI',
      'featherless': 'Featherless',
      'nvidia': 'NVIDIA'
    }

    return {
      name: modelName,
      provider: providerMap[providerName] || providerName,
      capabilities: ['text'],
      isFree: true,
      source: 'OpenRouter'
    }
  } else {
    // Pollinations模型信息
    try {
      const { getModelInfo: getPollinationsModelInfo } = await import('./pollinationsApi.js')
      const info = getPollinationsModelInfo(modelId)
      return {
        ...info,
        source: 'Pollinations'
      }
    } catch (error) {
      return {
        name: modelId,
        provider: 'Unknown',
        capabilities: ['text'],
        isFree: true,
        source: 'Pollinations'
      }
    }
  }
}

// 验证模型可用性
export async function validateModel(modelId) {
  const provider = getProvider(modelId)
  
  try {
    // 发送一个简单的测试消息
    const testMessages = [
      { role: 'user', content: 'Hello' }
    ]
    
    const result = await generateChatCompletion(testMessages, {
      model: modelId,
      max_tokens: 10
    })
    
    return {
      available: true,
      provider: provider,
      response: result.content
    }
  } catch (error) {
    console.warn(`模型 ${modelId} 验证失败:`, error)
    return {
      available: false,
      provider: provider,
      error: error.message
    }
  }
}

// 获取推荐模型
export function getRecommendedModels() {
  return [
    // OpenRouter 推荐模型
    'deepseek/deepseek-chat-v3-0324:free',
    'deepseek/deepseek-r1:free',
    'qwen/qwen3-coder:free',
    'meta-llama/llama-3.3-70b-instruct:free',
    'google/gemini-2.0-flash-exp:free',
    'mistralai/mistral-small-3.2-24b-instruct:free',
    
    // Pollinations 推荐模型
    'openai',
    'deepseek',
    'gemini',
    'llama',
    'mistral',
    'qwen-coder'
  ]
}

// 获取快速模型（适合实时对话）
export function getFastModels() {
  return [
    'qwen/qwen3-4b:free',
    'meta-llama/llama-3.2-3b-instruct:free',
    'google/gemma-3-4b-it:free',
    'mistralai/mistral-7b-instruct:free',
    'openai',
    'gemini',
    'llama'
  ]
}

// 获取高质量模型（适合复杂任务）
export function getHighQualityModels() {
  return [
    'meta-llama/llama-3.1-405b-instruct:free',
    'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
    'qwen/qwen3-235b-a22b-2507:free',
    'deepseek/deepseek-r1-distill-llama-70b:free',
    'qwen/qwen-2.5-72b-instruct:free',
    'deepseek',
    'deepseek-reasoning',
    'openai-large'
  ]
}

// 获取支持视觉的模型
export function getVisionModels() {
  return [
    'qwen/qwen2.5-vl-72b-instruct:free',
    'qwen/qwen2.5-vl-32b-instruct:free',
    'meta-llama/llama-3.2-11b-vision-instruct:free',
    'moonshotai/kimi-vl-a3b-thinking:free',
    'openai',
    'gemini',
    'llama-vision',
    'phi'
  ]
}

// 获取代码专用模型
export function getCodeModels() {
  return [
    'qwen/qwen3-coder:free',
    'qwen/qwen-2.5-coder-32b-instruct:free',
    'mistralai/devstral-small-2505:free',
    'agentica-org/deepcoder-14b-preview:free',
    'qwen-coder',
    'deepseek'
  ]
}

export default {
  generateChatCompletion,
  generateChatCompletionStream,
  getModelInfo,
  validateModel,
  getRecommendedModels,
  getFastModels,
  getHighQualityModels,
  getVisionModels,
  getCodeModels
}
