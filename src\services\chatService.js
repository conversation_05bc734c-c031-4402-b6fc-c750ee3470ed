// 统一聊天服务 - 整合多个AI提供商
import { generateChatCompletion as pollinationsChat, generateChatCompletionStream as pollinationsStream } from './pollinationsApi.js'
import { generateChatCompletion as openrouterChat, generateChatCompletionStream as openrouterStream, handleOpenRouterError } from './openrouterApi.js'
import targonApi from './targonApi.js'
import zhipuApi from './zhipuApi.js'
import claudeApi from './claudeApi.js'
import { ElMessage } from 'element-plus'

// 判断模型提供商
function getProvider(modelId) {
  if (!modelId) return 'pollinations'

  // Claude 模型
  if (modelId.includes('claude-3.5-sonnet') ||
      modelId.includes('claude-3.7-sonnet') ||
      modelId.includes('claude-4-sonnet') ||
      modelId.includes('deepseek-r1') ||
      modelId.includes('openai-gpt-4.1') ||
      modelId.includes('claude:')) {
    return 'claude'
  }

  // 智谱 GLM 模型
  if (modelId.includes('glm-4.5') ||
      modelId.includes('glm-4') ||
      modelId.includes('zhipu:')) {
    return 'zhipu'
  }

  // Targon 模型通常以组织名/模型名格式，如 deepseek-ai/DeepSeek-V3
  if (modelId.includes('deepseek-ai/') ||
      modelId.includes('moonshot/') ||
      modelId.includes('Qwen/') ||
      modelId.includes('targon:')) {
    return 'targon'
  }

  // OpenRouter 模型通常包含 :free 后缀
  if (modelId.includes('/') && modelId.includes(':free')) {
    return 'openrouter'
  }

  return 'pollinations'
}

// 统一聊天完成接口
export async function generateChatCompletion(messages, options = {}) {
  const provider = getProvider(options.model)

  try {
    switch (provider) {
      case 'claude':
        return await generateClaudeChatCompletion(messages, options)
      case 'openrouter':
        return await openrouterChat(messages, options)
      case 'targon':
        return await generateTargonChatCompletion(messages, options)
      case 'zhipu':
        return await generateZhipuChatCompletion(messages, options)
      case 'pollinations':
      default:
        return await pollinationsChat(messages, options)
    }
  } catch (error) {
    console.error(`${provider} 聊天失败:`, error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    })

    // 根据提供商处理错误
    if (provider === 'openrouter') {
      handleOpenRouterError(error)
    } else if (provider === 'targon') {
      ElMessage.error(error.message || 'Targon API 请求失败')
    } else if (provider === 'zhipu') {
      ElMessage.error(error.message || '智谱 API 请求失败')
    } else if (provider === 'claude') {
      ElMessage.error(error.message || 'Claude API 请求失败')
    } else {
      ElMessage.error(error.message || '聊天请求失败')
    }

    throw error
  }
}

// 统一流式聊天接口
export async function generateChatCompletionStream(messages, options = {}, onChunk) {
  const provider = getProvider(options.model)

  try {
    switch (provider) {
      case 'claude':
        return await generateClaudeChatCompletionStream(messages, options, onChunk)
      case 'openrouter':
        return await openrouterStream(messages, options, onChunk)
      case 'targon':
        return await generateTargonChatCompletionStream(messages, options, onChunk)
      case 'zhipu':
        return await generateZhipuChatCompletionStream(messages, options, onChunk)
      case 'pollinations':
      default:
        return await pollinationsStream(messages, options, onChunk)
    }
  } catch (error) {
    console.error(`${provider} 流式聊天失败:`, error)

    // 根据提供商处理错误
    if (provider === 'openrouter') {
      handleOpenRouterError(error)
    } else if (provider === 'targon') {
      ElMessage.error(error.message || 'Targon API 流式请求失败')
    } else if (provider === 'claude') {
      ElMessage.error(error.message || 'Claude API 流式请求失败')
    } else {
      ElMessage.error(error.message || '流式聊天请求失败')
    }

    throw error
  }
}

// Targon 聊天完成
async function generateTargonChatCompletion(messages, options = {}) {
  // 确保 API 密钥已设置
  const apiKey = localStorage.getItem('targon_api_key')
  if (!apiKey) {
    throw new Error('Targon API 密钥未设置，请先在设置中配置')
  }

  targonApi.setApiKey(apiKey)

  const result = await targonApi.chat({
    model: options.model,
    messages: messages,
    temperature: options.temperature || 0.7,
    max_tokens: options.max_tokens || 2048,
    stream: false
  })

  if (!result.success) {
    throw new Error(result.message || 'Targon API 请求失败')
  }

  return result.data
}

// Targon 流式聊天完成
async function generateTargonChatCompletionStream(messages, options = {}, onChunk) {
  // 确保 API 密钥已设置
  const apiKey = localStorage.getItem('targon_api_key')
  if (!apiKey) {
    throw new Error('Targon API 密钥未设置，请先在设置中配置')
  }

  targonApi.setApiKey(apiKey)

  return new Promise((resolve, reject) => {
    targonApi.streamChat(
      {
        model: options.model,
        messages: messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2048
      },
      (chunk) => {
        // 处理流式数据块
        if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
          const content = chunk.choices[0].delta.content
          if (content) {
            onChunk?.(content)
          }
        }
      },
      (error) => {
        reject(error)
      },
      () => {
        resolve()
      }
    )
  })
}

// 获取模型信息
export async function getModelInfo(modelId) {
  const provider = getProvider(modelId)

  if (provider === 'claude') {
    // Claude 模型信息
    const modelMap = {
      'claude-3.5-sonnet': {
        name: 'Claude 3.5 Sonnet',
        provider: 'Anthropic',
        capabilities: ['text', 'reasoning'],
        maxTokens: 200000,
        source: 'Claude API'
      },
      'claude-3.7-sonnet': {
        name: 'Claude 3.7 Sonnet',
        provider: 'Anthropic',
        capabilities: ['text', 'reasoning'],
        maxTokens: 200000,
        source: 'Claude API'
      },
      'claude-4-sonnet': {
        name: 'Claude 4 Sonnet',
        provider: 'Anthropic',
        capabilities: ['text', 'reasoning'],
        maxTokens: 200000,
        source: 'Claude API'
      },
      'deepseek-r1': {
        name: 'DeepSeek R1',
        provider: 'DeepSeek',
        capabilities: ['text', 'reasoning'],
        maxTokens: 128000,
        source: 'Claude API'
      },
      'openai-gpt-4.1': {
        name: 'OpenAI GPT-4.1',
        provider: 'OpenAI',
        capabilities: ['text', 'reasoning'],
        maxTokens: 128000,
        source: 'Claude API'
      }
    }

    return modelMap[modelId] || {
      name: modelId,
      provider: 'Unknown',
      capabilities: ['text'],
      source: 'Claude API'
    }
  } else if (provider === 'openrouter') {
    // OpenRouter模型信息
    const parts = modelId.split('/')
    const providerName = parts[0]
    const modelName = parts[1]?.replace(':free', '') || modelId
    
    const providerMap = {
      'deepseek': 'DeepSeek',
      'qwen': 'Alibaba',
      'moonshotai': 'Moonshot AI',
      'google': 'Google',
      'mistralai': 'Mistral AI',
      'microsoft': 'Microsoft',
      'meta-llama': 'Meta',
      'tngtech': 'TNG Technology',
      'agentica-org': 'Agentica',
      'cognitivecomputations': 'Cognitive Computations',
      'thudm': 'Tsinghua University',
      'shisa-ai': 'Shisa AI',
      'tencent': 'Tencent',
      'nousresearch': 'Nous Research',
      'arliai': 'Arli AI',
      'rekaai': 'Reka AI',
      'sarvamai': 'Sarvam AI',
      'featherless': 'Featherless',
      'nvidia': 'NVIDIA'
    }

    return {
      name: modelName,
      provider: providerMap[providerName] || providerName,
      capabilities: ['text'],
      isFree: true,
      source: 'OpenRouter'
    }
  } else {
    // Pollinations模型信息
    try {
      const { getModelInfo: getPollinationsModelInfo } = await import('./pollinationsApi.js')
      const info = getPollinationsModelInfo(modelId)
      return {
        ...info,
        source: 'Pollinations'
      }
    } catch (error) {
      return {
        name: modelId,
        provider: 'Unknown',
        capabilities: ['text'],
        isFree: true,
        source: 'Pollinations'
      }
    }
  }
}

// 验证模型可用性
export async function validateModel(modelId) {
  const provider = getProvider(modelId)
  
  try {
    // 发送一个简单的测试消息
    const testMessages = [
      { role: 'user', content: 'Hello' }
    ]
    
    const result = await generateChatCompletion(testMessages, {
      model: modelId,
      max_tokens: 10
    })
    
    return {
      available: true,
      provider: provider,
      response: result.content
    }
  } catch (error) {
    console.warn(`模型 ${modelId} 验证失败:`, error)
    return {
      available: false,
      provider: provider,
      error: error.message
    }
  }
}

// 获取推荐模型
export function getRecommendedModels() {
  return [
    // Claude 推荐模型
    'claude-3.5-sonnet',
    'claude-3.7-sonnet',
    'claude-4-sonnet',
    'deepseek-r1',
    'openai-gpt-4.1',

    // OpenRouter 推荐模型
    'deepseek/deepseek-chat-v3-0324:free',
    'deepseek/deepseek-r1:free',
    'qwen/qwen3-coder:free',
    'meta-llama/llama-3.3-70b-instruct:free',
    'google/gemini-2.0-flash-exp:free',
    'mistralai/mistral-small-3.2-24b-instruct:free',

    // Pollinations 推荐模型
    'openai',
    'deepseek',
    'gemini',
    'llama',
    'mistral',
    'qwen-coder'
  ]
}

// 获取快速模型（适合实时对话）
export function getFastModels() {
  return [
    'qwen/qwen3-4b:free',
    'meta-llama/llama-3.2-3b-instruct:free',
    'google/gemma-3-4b-it:free',
    'mistralai/mistral-7b-instruct:free',
    'openai',
    'gemini',
    'llama'
  ]
}

// 获取高质量模型（适合复杂任务）
export function getHighQualityModels() {
  return [
    // Claude 高质量模型
    'claude-4-sonnet',
    'claude-3.7-sonnet',
    'deepseek-r1',
    'openai-gpt-4.1',

    // 其他高质量模型
    'meta-llama/llama-3.1-405b-instruct:free',
    'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
    'qwen/qwen3-235b-a22b-2507:free',
    'deepseek/deepseek-r1-distill-llama-70b:free',
    'qwen/qwen-2.5-72b-instruct:free',
    'deepseek',
    'deepseek-reasoning',
    'openai-large'
  ]
}

// 获取支持视觉的模型
export function getVisionModels() {
  return [
    'qwen/qwen2.5-vl-72b-instruct:free',
    'qwen/qwen2.5-vl-32b-instruct:free',
    'meta-llama/llama-3.2-11b-vision-instruct:free',
    'moonshotai/kimi-vl-a3b-thinking:free',
    'openai',
    'gemini',
    'llama-vision',
    'phi'
  ]
}

// 获取代码专用模型
export function getCodeModels() {
  return [
    'qwen/qwen3-coder:free',
    'qwen/qwen-2.5-coder-32b-instruct:free',
    'mistralai/devstral-small-2505:free',
    'agentica-org/deepcoder-14b-preview:free',
    'qwen-coder',
    'deepseek'
  ]
}

// 智谱聊天完成函数
async function generateZhipuChatCompletion(messages, options = {}) {
  try {
    console.log('🤖 使用智谱 API 生成聊天回复...')
    console.log('请求参数:', {
      model: options.model || 'glm-4.5-air',
      messageCount: messages.length,
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 2048
    })

    const result = await zhipuApi.chat({
      model: options.model || 'glm-4.5-air',
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 2048,
      stream: false
    })

    console.log('智谱 API 原始响应:', result)

    if (result.success) {
      console.log('✅ 智谱聊天完成成功')
      // 统一返回格式
      return {
        success: true,
        content: result.data.choices?.[0]?.message?.content || result.data.content || '无回复内容',
        usage: result.data.usage || null
      }
    } else {
      throw new Error(result.message || '智谱 API 请求失败')
    }
  } catch (error) {
    console.error('❌ 智谱聊天完成失败:', error)
    throw error
  }
}

// 智谱流式聊天完成函数
async function generateZhipuChatCompletionStream(messages, options = {}, onChunk) {
  try {
    console.log('🌊 使用智谱 API 生成流式聊天回复...')

    return new Promise((resolve, reject) => {
      let fullContent = ''

      zhipuApi.streamChat(
        {
          model: options.model || 'glm-4.5-air',
          messages: messages,
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || 2048
        },
        // onMessage
        (content) => {
          fullContent += content
          if (onChunk) {
            onChunk(content)
          }
        },
        // onError
        (error) => {
          console.error('❌ 智谱流式聊天失败:', error)
          reject(error)
        },
        // onComplete
        () => {
          console.log('✅ 智谱流式聊天完成')
          resolve({
            choices: [{
              message: {
                role: 'assistant',
                content: fullContent
              }
            }]
          })
        }
      )
    })
  } catch (error) {
    console.error('❌ 智谱流式聊天失败:', error)
    throw error
  }
}

// Claude 聊天完成函数
async function generateClaudeChatCompletion(messages, options = {}) {
  try {
    console.log('🤖 使用 Claude API 生成聊天回复...')
    console.log('请求参数:', {
      model: options.model || 'claude-3.5-sonnet',
      messageCount: messages.length,
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 4000
    })

    // API Key 已内置，无需额外设置

    const result = await claudeApi.createChatCompletion({
      model: options.model || 'claude-3.5-sonnet',
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 4000,
      stream: false
    })

    console.log('Claude API 原始响应:', result)

    if (result.success) {
      console.log('✅ Claude 聊天完成成功')
      // 统一返回格式
      return {
        success: true,
        content: result.data.content?.[0]?.text || result.data.choices?.[0]?.message?.content || '无回复内容',
        usage: result.data.usage || null
      }
    } else {
      throw new Error(result.message || 'Claude API 请求失败')
    }
  } catch (error) {
    console.error('❌ Claude 聊天完成失败:', error)
    throw error
  }
}

// Claude 流式聊天完成函数
async function generateClaudeChatCompletionStream(messages, options = {}, onChunk) {
  try {
    console.log('🌊 使用 Claude API 生成流式聊天回复...')

    // API Key 已内置，无需额外设置

    return new Promise((resolve, reject) => {
      let fullContent = ''

      claudeApi.createChatCompletionStream(
        {
          model: options.model || 'claude-3.5-sonnet',
          messages: messages,
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || 4000
        },
        // onMessage
        (content) => {
          fullContent += content
          if (onChunk) {
            onChunk(content)
          }
        },
        // onError
        (error) => {
          console.error('❌ Claude 流式聊天失败:', error)
          reject(error)
        },
        // onComplete
        () => {
          console.log('✅ Claude 流式聊天完成')
          resolve({
            choices: [{
              message: {
                role: 'assistant',
                content: fullContent
              }
            }]
          })
        }
      )
    })
  } catch (error) {
    console.error('❌ Claude 流式聊天完成失败:', error)
    throw error
  }
}

export default {
  generateChatCompletion,
  generateChatCompletionStream,
  getModelInfo,
  validateModel,
  getRecommendedModels,
  getFastModels,
  getHighQualityModels,
  getVisionModels,
  getCodeModels
}
