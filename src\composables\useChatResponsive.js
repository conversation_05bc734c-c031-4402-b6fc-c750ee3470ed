// 聊天界面专用响应式组合式函数
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useResponsive } from './useResponsive.js'

export function useChatResponsive() {
  const { isMobile, isTablet, isSmallMobile, isTouchDevice } = useResponsive()
  
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  const sidebarVisible = ref(false)
  
  // 聊天输入框状态
  const inputFocused = ref(false)
  const inputHeight = ref(40)
  const maxInputHeight = ref(120)
  
  // 消息列表状态
  const messageListScrolled = ref(false)
  const showScrollToBottom = ref(false)
  
  // 移动端特定状态
  const keyboardVisible = ref(false)
  const viewportHeight = ref(typeof window !== 'undefined' ? window.innerHeight : 768)
  
  // 计算属性
  const shouldShowMobileSidebar = computed(() => {
    return isMobile.value && sidebarVisible.value
  })
  
  const chatInputClasses = computed(() => ({
    'mobile-optimized': isMobile.value,
    'small-mobile': isSmallMobile.value,
    'keyboard-visible': keyboardVisible.value,
    'input-focused': inputFocused.value
  }))
  
  const messageListClasses = computed(() => ({
    'mobile-optimized': isMobile.value,
    'tablet-optimized': isTablet.value,
    'touch-device': isTouchDevice.value,
    'keyboard-visible': keyboardVisible.value
  }))
  
  const sidebarClasses = computed(() => ({
    'mobile-sidebar': isMobile.value,
    'collapsed': sidebarCollapsed.value,
    'visible': sidebarVisible.value
  }))
  
  // 移动端输入框高度计算
  const adaptiveInputHeight = computed(() => {
    if (isMobile.value) {
      return keyboardVisible.value ? 
        Math.min(inputHeight.value, maxInputHeight.value * 0.8) : 
        inputHeight.value
    }
    return inputHeight.value
  })
  
  // 消息气泡最大宽度
  const messageBubbleMaxWidth = computed(() => {
    if (isSmallMobile.value) return '95%'
    if (isMobile.value) return '85%'
    if (isTablet.value) return '75%'
    return '70%'
  })
  
  // 方法
  const toggleSidebar = () => {
    if (isMobile.value) {
      sidebarVisible.value = !sidebarVisible.value
    } else {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }
  }
  
  const closeSidebar = () => {
    if (isMobile.value) {
      sidebarVisible.value = false
    }
  }
  
  const handleInputFocus = () => {
    inputFocused.value = true
    if (isMobile.value) {
      // 延迟检测键盘是否弹出
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          const currentHeight = window.innerHeight
          if (currentHeight < viewportHeight.value * 0.8) {
            keyboardVisible.value = true
          }
        }
      }, 300)
    }
  }
  
  const handleInputBlur = () => {
    inputFocused.value = false
    if (isMobile.value) {
      setTimeout(() => {
        keyboardVisible.value = false
      }, 100)
    }
  }
  
  const handleInputResize = (height) => {
    inputHeight.value = Math.min(height, maxInputHeight.value)
  }
  
  const scrollToBottom = (smooth = true) => {
    nextTick(() => {
      const messageList = document.querySelector('.messages-container')
      if (messageList) {
        messageList.scrollTo({
          top: messageList.scrollHeight,
          behavior: smooth ? 'smooth' : 'auto'
        })
      }
    })
  }
  
  const handleMessageListScroll = (event) => {
    const { scrollTop, scrollHeight, clientHeight } = event.target
    messageListScrolled.value = scrollTop > 100
    showScrollToBottom.value = scrollTop < scrollHeight - clientHeight - 100
  }
  
  // 视口高度变化监听（用于检测键盘弹出）
  const handleViewportChange = () => {
    if (typeof window !== 'undefined') {
      const currentHeight = window.innerHeight
      if (isMobile.value) {
        if (currentHeight < viewportHeight.value * 0.8 && inputFocused.value) {
          keyboardVisible.value = true
        } else if (currentHeight >= viewportHeight.value * 0.9) {
          keyboardVisible.value = false
        }
      }
    }
  }
  
  // 触摸事件处理
  const handleTouchStart = (event) => {
    if (isMobile.value && sidebarVisible.value) {
      const sidebar = document.querySelector('.chat-sidebar')
      if (sidebar && !sidebar.contains(event.target)) {
        closeSidebar()
      }
    }
  }
  
  // 生命周期
  onMounted(() => {
    if (typeof window !== 'undefined') {
      viewportHeight.value = window.innerHeight

      // 监听视口变化
      window.addEventListener('resize', handleViewportChange)
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          viewportHeight.value = window.innerHeight
          handleViewportChange()
        }, 500)
      })

      // 移动端触摸事件
      if (isMobile.value) {
        document.addEventListener('touchstart', handleTouchStart)
      }
    }

    // 初始化侧边栏状态
    if (isMobile.value) {
      sidebarCollapsed.value = false
      sidebarVisible.value = false
    }
  })

  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', handleViewportChange)
      document.removeEventListener('touchstart', handleTouchStart)
    }
  })
  
  return {
    // 状态
    sidebarCollapsed,
    sidebarVisible,
    inputFocused,
    inputHeight,
    keyboardVisible,
    messageListScrolled,
    showScrollToBottom,
    
    // 计算属性
    shouldShowMobileSidebar,
    chatInputClasses,
    messageListClasses,
    sidebarClasses,
    adaptiveInputHeight,
    messageBubbleMaxWidth,
    
    // 方法
    toggleSidebar,
    closeSidebar,
    handleInputFocus,
    handleInputBlur,
    handleInputResize,
    scrollToBottom,
    handleMessageListScroll,
    
    // 响应式判断
    isMobile,
    isTablet,
    isSmallMobile,
    isTouchDevice
  }
}

// 聊天消息优化
export function useChatMessageOptimization() {
  const { isMobile, isSmallMobile } = useResponsive()
  
  // 消息渲染优化
  const getMessageClasses = (message, index) => {
    const baseClasses = ['message-item']
    
    if (message.role === 'user') {
      baseClasses.push('user-message')
    } else {
      baseClasses.push('ai-message')
    }
    
    if (isMobile.value) {
      baseClasses.push('mobile-message')
    }
    
    if (isSmallMobile.value) {
      baseClasses.push('small-mobile-message')
    }
    
    return baseClasses
  }
  
  // 消息内容截断
  const truncateMessage = (content, maxLength = null) => {
    if (!maxLength) {
      maxLength = isSmallMobile.value ? 200 : isMobile.value ? 300 : 500
    }
    
    if (content.length <= maxLength) return content
    
    return content.substring(0, maxLength) + '...'
  }
  
  // 代码块优化
  const optimizeCodeBlock = (code) => {
    if (isMobile.value) {
      // 移动端代码块优化：添加水平滚动
      return {
        maxWidth: '100%',
        overflowX: 'auto',
        fontSize: isSmallMobile.value ? '12px' : '13px',
        lineHeight: '1.4'
      }
    }
    return {}
  }
  
  return {
    getMessageClasses,
    truncateMessage,
    optimizeCodeBlock,
    isMobile,
    isSmallMobile
  }
}
