// Pollinations AI API 服务
import { ElMessage } from 'element-plus'

// API 基础配置
const API_CONFIG = {
  baseUrl: 'https://text.pollinations.ai',
  openaiUrl: 'https://text.pollinations.ai/openai',
  imageUrl: 'https://image.pollinations.ai/prompt',
  geminiUrl: 'https://apiv2.aliyahzombie.top/v1beta/openai',
  geminiApiKey: '6dc26766-144a-4db7-8375-72423b0d4a10',
  timeout: 30000,
  defaultModel: 'openai'
}

// 支持的模型列表 (基于最新 Pollinations.AI llms.txt)
export const SUPPORTED_MODELS = [
  // Pollinations.AI 官方模型
  'openai',                    // OpenAI GPT-4.1-nano
  'openai-large',             // OpenAI GPT-4.1 mini
  'openai-reasoning',         // OpenAI o4-mini
  'openai-audio',             // OpenAI GPT-4o-audio-preview
  'qwen-coder',               // Qwen 2.5 Coder 32B
  'llama',                    // Llama 3.3 70B
  'llamascout',               // Llama 4 Scout 17B
  'llama-vision',             // Llama 3.2 11B Vision
  'mistral',                  // Mistral Small 3
  'unity',                    // Unity Mistral Large (uncensored)
  'midijourney',              // Midijourney (music generation)
  'rtist',                    // Rtist (creative writing)
  'searchgpt',                // SearchGPT (search-augmented)
  'evil',                     // Evil (uncensored)
  'deepseek',                 // DeepSeek-V3
  'deepseek-reasoning',       // DeepSeek-R1 Distill Qwen 32B
  'deepseek-reasoning-large', // DeepSeek R1 - Llama 70B
  'phi',                      // Phi-4 Instruct
  'gemini',                   // Gemini 2.5 Flash Preview
  'hormoz',                   // Hormoz 8b
  'hypnosis-tracy',           // Hypnosis Tracy 7B
  'sur',                      // Sur AI Assistant
  'flux',                     // Latest stable diffusion model
  'turbo',                    // Fast image generation model

  // GeminiPool 模型 (仅保留 2.0 和 2.5 系列)
  'gemini-2.0-flash-exp',
  'gemini-2.0-flash-thinking-exp-1219',
  'gemini-exp-1114',
  'gemini-exp-1121',
  'gemini-exp-1206',

  // Gemini 2.5 系列模型
  'gemini-2.5-flash-preview-05-20',
  'gemini-2.5-flash',
  'gemini-2.5-flash-lite-preview-06-17',
  'gemini-2.5-pro',
  'gemini-2.5-flash-lite'
]

// 错误处理
class PollinationsError extends Error {
  constructor(message, status, response) {
    super(message)
    this.name = 'PollinationsError'
    this.status = status
    this.response = response
  }
}

// HTTP 请求工具
async function makeRequest(url, options = {}) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout)

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      throw new PollinationsError(
        `API请求失败: ${response.status} ${response.statusText}`,
        response.status,
        errorText
      )
    }

    return response
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new PollinationsError('请求超时，请稍后重试', 408)
    }
    throw error
  }
}

// 获取可用模型列表 (从 Pollinations.AI API)
export async function getAvailableModels() {
  try {
    // 获取文本模型列表
    const textResponse = await makeRequest(`${API_CONFIG.baseUrl}/models`)
    const textModels = await textResponse.json()

    // 获取图像模型列表
    let imageModels = []
    try {
      const imageResponse = await makeRequest(`${API_CONFIG.imageUrl}/models`)
      imageModels = await imageResponse.json()
    } catch (error) {
      console.warn('获取图像模型列表失败:', error)
    }

    // 合并并转换模型格式
    const allModels = [
      ...textModels.map(model => ({
        id: model.name || model.id,
        name: model.description || model.name || model.id,
        description: model.description || `${model.name || model.id} 文本模型`,
        provider: model.provider || 'pollinations',
        tier: model.tier || 'anonymous',
        type: 'text',
        capabilities: {
          text: true,
          vision: model.vision || model.input_modalities?.includes('image') || false,
          audio: model.audio || model.input_modalities?.includes('audio') || false,
          tools: model.tools || false,
          reasoning: model.reasoning || false,
          uncensored: model.uncensored || false
        },
        maxInputChars: model.maxInputChars || 4000,
        voices: model.voices || [],
        community: model.community || false,
        aliases: Array.isArray(model.aliases) ? model.aliases : (model.aliases ? [model.aliases] : [])
      })),
      ...imageModels.map(model => ({
        id: model.name || model.id,
        name: model.description || model.name || model.id,
        description: model.description || `${model.name || model.id} 图像模型`,
        provider: 'pollinations',
        tier: 'anonymous',
        type: 'image',
        capabilities: {
          text: false,
          vision: false,
          audio: false,
          tools: false,
          image: true
        },
        maxInputChars: 1000,
        community: false
      }))
    ]

    return allModels
  } catch (error) {
    console.error('获取模型列表失败:', error)
    ElMessage.error('获取模型列表失败，使用默认模型')
    return getDefaultModels()
  }
}

// 默认模型列表（作为备用）
function getDefaultModels() {
  return [
    {
      id: 'openai',
      name: 'OpenAI GPT-4o Mini',
      description: '快速高效的对话模型',
      provider: 'azure',
      tier: 'anonymous',
      capabilities: { text: true, vision: true, audio: false, tools: true },
      maxInputChars: 4000,
      community: false
    },
    {
      id: 'mistral',
      name: 'Mistral Small 3.1 24B',
      description: '强大的开源模型',
      provider: 'cloudflare',
      tier: 'anonymous',
      capabilities: { text: true, vision: true, audio: false, tools: true },
      maxInputChars: 4000,
      community: false
    },
    {
      id: 'llama-roblox',
      name: 'Llama 3.1 8B Instruct',
      description: 'Meta开源的高效模型',
      provider: 'nebius',
      tier: 'anonymous',
      capabilities: { text: true, vision: false, audio: false, tools: true },
      maxInputChars: 4000,
      community: false
    }
  ]
}

// 简单文本生成 (GET 方式)
export async function generateTextSimple(prompt) {
  try {
    // 使用GET方式调用：https://text.pollinations.ai/{prompt}
    const encodedPrompt = encodeURIComponent(prompt)
    const url = `${API_CONFIG.baseUrl}/${encodedPrompt}`

    const response = await makeRequest(url, {
      method: 'GET'
    })
    const text = await response.text()

    return {
      success: true,
      content: text,
      model: 'default',
      method: 'GET'
    }
  } catch (error) {
    console.error('简单文本生成失败:', error)
    return {
      success: false,
      error: error.message,
      content: '抱歉，生成文本时出现错误，请稍后重试。'
    }
  }
}

// 高级聊天完成 (POST 方式，支持多模态)
export async function generateChatCompletion(messages, options = {}) {
  try {
    // 验证模型是否支持
    const model = options.model || API_CONFIG.defaultModel

    // 如果是Gemini模型，使用Gemini API
    if (model.includes('gemini')) {
      return await generateGeminiCompletion(messages, options)
    }

    if (!SUPPORTED_MODELS.includes(model)) {
      console.warn(`模型 ${model} 可能不受支持，使用默认模型`)
    }

    const payload = {
      model: model,
      messages: messages
    }

    // 添加可选参数
    if (options.temperature !== undefined) payload.temperature = options.temperature
    if (options.seed !== undefined) payload.seed = options.seed
    if (options.maxTokens !== undefined) payload.max_tokens = options.maxTokens
    if (options.stream !== undefined) payload.stream = options.stream
    if (options.tools !== undefined) payload.tools = options.tools
    if (options.toolChoice !== undefined) payload.tool_choice = options.toolChoice
    if (options.responseFormat !== undefined) payload.response_format = options.responseFormat

    const response = await makeRequest(API_CONFIG.openaiUrl, {
      method: 'POST',
      body: JSON.stringify(payload)
    })

    if (options.stream) {
      return response // 返回流响应供调用者处理
    }

    const result = await response.json()

    return {
      success: true,
      content: result.choices?.[0]?.message?.content || '无响应内容',
      model: options.model || API_CONFIG.defaultModel,
      usage: result.usage,
      toolCalls: result.choices?.[0]?.message?.tool_calls,
      finishReason: result.choices?.[0]?.finish_reason
    }
  } catch (error) {
    console.error('聊天完成失败:', error)
    return {
      success: false,
      error: error.message,
      content: '抱歉，生成回复时出现错误，请稍后重试。'
    }
  }
}

// 流式聊天完成
export async function generateChatCompletionStream(messages, options = {}, onChunk) {
  try {
    const payload = {
      model: options.model || API_CONFIG.defaultModel,
      messages: messages,
      stream: true
    }

    // 添加可选参数
    if (options.temperature !== undefined) payload.temperature = options.temperature
    if (options.seed !== undefined) payload.seed = options.seed
    if (options.maxTokens !== undefined) payload.max_tokens = options.maxTokens
    if (options.tools !== undefined) payload.tools = options.tools
    if (options.toolChoice !== undefined) payload.tool_choice = options.toolChoice
    if (options.private !== undefined) payload.private = options.private

    const response = await makeRequest(API_CONFIG.openaiUrl, {
      method: 'POST',
      body: JSON.stringify(payload)
    })

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data === '[DONE]') {
              return { success: true }
            }

            try {
              const chunk = JSON.parse(data)
              const content = chunk.choices?.[0]?.delta?.content
              if (content && onChunk) {
                onChunk(content)
              }
            } catch (e) {
              console.warn('解析流数据失败:', e)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return { success: true }
  } catch (error) {
    console.error('流式聊天失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 文本转语音
export async function generateTextToSpeech(text, options = {}) {
  try {
    const params = new URLSearchParams({
      model: 'openai-audio',
      voice: options.voice || 'alloy'
    })

    const encodedText = encodeURIComponent(text)
    const url = `${API_CONFIG.baseUrl}/${encodedText}?${params}`

    const response = await makeRequest(url)
    
    if (response.headers.get('content-type')?.includes('audio')) {
      const audioBlob = await response.blob()
      return {
        success: true,
        audioBlob: audioBlob,
        audioUrl: URL.createObjectURL(audioBlob)
      }
    } else {
      throw new Error('响应不是音频格式')
    }
  } catch (error) {
    console.error('文本转语音失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 语音转文本
export async function generateSpeechToText(audioFile, options = {}) {
  try {
    // 将音频文件转换为base64
    const base64Audio = await fileToBase64(audioFile)
    const audioFormat = audioFile.name.split('.').pop().toLowerCase()

    const messages = [
      {
        role: 'user',
        content: [
          { type: 'text', text: options.prompt || '请转录这段音频:' },
          {
            type: 'input_audio',
            input_audio: {
              data: base64Audio,
              format: audioFormat
            }
          }
        ]
      }
    ]

    const result = await generateChatCompletion(messages, {
      model: 'openai-audio',
      ...options
    })

    return result
  } catch (error) {
    console.error('语音转文本失败:', error)
    return {
      success: false,
      error: error.message,
      content: '语音转文本失败，请稍后重试。'
    }
  }
}

// 图像分析
export async function analyzeImage(imageUrl, prompt = '描述这张图片', options = {}) {
  try {
    const messages = [
      {
        role: 'user',
        content: [
          { type: 'text', text: prompt },
          {
            type: 'image_url',
            image_url: { url: imageUrl }
          }
        ]
      }
    ]

    const result = await generateChatCompletion(messages, {
      model: options.model || 'openai',
      maxTokens: options.maxTokens || 500,
      ...options
    })

    return result
  } catch (error) {
    console.error('图像分析失败:', error)
    return {
      success: false,
      error: error.message,
      content: '图像分析失败，请稍后重试。'
    }
  }
}

// 图片生成 - Image Link Bot v3.0 - Pro 格式
export async function generateImage(prompt, options = {}) {
  try {
    // URL编码提示词
    const encodedPrompt = encodeURIComponent(prompt)

    // 生成1到1,000,000之间的随机种子
    const randomSeed = options.seed || Math.floor(Math.random() * 1000000) + 1

    // 添加所有参数
    const params = new URLSearchParams()
    params.append('seed', randomSeed)
    params.append('nologo', 'true')

    if (options.width) params.append('width', options.width)
    if (options.height) params.append('height', options.height)
    if (options.model) params.append('model', options.model)
    if (options.enhance !== undefined) params.append('enhance', options.enhance)
    if (options.safe !== undefined) params.append('safe', options.safe)

    const finalUrl = `${API_CONFIG.imageUrl}/${encodedPrompt}?${params.toString()}`

    const response = await makeRequest(finalUrl, {
      method: 'GET'
    })

    if (response.headers.get('content-type')?.includes('image')) {
      const imageBlob = await response.blob()
      const imageUrl = URL.createObjectURL(imageBlob)

      return {
        success: true,
        imageBlob: imageBlob,
        imageUrl: imageUrl,
        directUrl: finalUrl, // 直接访问的URL
        prompt: prompt,
        seed: randomSeed,
        options: options
      }
    } else {
      throw new Error('响应不是图片格式')
    }
  } catch (error) {
    console.error('图片生成失败:', error)
    return {
      success: false,
      error: error.message,
      prompt: prompt
    }
  }
}

// 批量图片生成
export async function generateMultipleImages(prompts, options = {}) {
  try {
    const results = await Promise.allSettled(
      prompts.map(prompt => generateImage(prompt, options))
    )

    const successful = results
      .filter(result => result.status === 'fulfilled' && result.value.success)
      .map(result => result.value)

    const failed = results
      .filter(result => result.status === 'rejected' || !result.value.success)
      .map((result, index) => ({
        prompt: prompts[index],
        error: result.status === 'rejected' ? result.reason : result.value.error
      }))

    return {
      success: successful.length > 0,
      successful: successful,
      failed: failed,
      total: prompts.length,
      successCount: successful.length,
      failCount: failed.length
    }
  } catch (error) {
    console.error('批量图片生成失败:', error)
    return {
      success: false,
      error: error.message,
      successful: [],
      failed: prompts.map(prompt => ({ prompt, error: error.message }))
    }
  }
}

// Gemini API 聊天完成 (使用 GeminiPool 的 OpenAI 兼容格式)
async function generateGeminiCompletion(messages, options = {}) {
  try {
    // 使用 OpenAI 兼容格式，无需转换消息格式
    const payload = {
      model: options.model || 'gemini-2.5-flash',
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 1000,
      top_p: options.topP || 0.8,
      stream: options.stream || false
    }

    // 添加可选参数
    if (options.tools !== undefined) payload.tools = options.tools
    if (options.toolChoice !== undefined) payload.tool_choice = options.toolChoice
    if (options.responseFormat !== undefined) payload.response_format = options.responseFormat

    console.log('发送 Gemini API 请求:', {
      url: API_CONFIG.geminiUrl + '/chat/completions',
      model: payload.model,
      messagesCount: payload.messages.length
    })

    const response = await makeRequest(API_CONFIG.geminiUrl + '/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_CONFIG.geminiApiKey}`
      },
      body: JSON.stringify(payload)
    })

    if (options.stream) {
      return response // 返回流响应供调用者处理
    }

    const result = await response.json()
    console.log('Gemini API 响应:', result)

    return {
      success: true,
      content: result.choices?.[0]?.message?.content || '无响应内容',
      model: options.model || 'gemini-2.5-flash',
      usage: result.usage || {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      },
      toolCalls: result.choices?.[0]?.message?.tool_calls,
      finishReason: result.choices?.[0]?.finish_reason || 'stop'
    }
  } catch (error) {
    console.error('Gemini聊天完成失败:', error)

    let errorMessage = '抱歉，Gemini模型生成回复时出现错误，请稍后重试或切换其他模型。'

    // 检查是否是 PollinationsError
    if (error instanceof PollinationsError) {
      console.error('API 错误详情:', {
        status: error.status,
        message: error.message,
        response: error.response
      })

      switch (error.status) {
        case 400:
          errorMessage = `请求参数错误 (400)：${error.response || '模型可能不支持或参数格式有误'}。请尝试切换其他 Gemini 模型。`
          break
        case 401:
          errorMessage = 'API 密钥无效 (401)，请检查 Gemini API 配置。'
          break
        case 403:
          errorMessage = '权限不足 (403)，请检查 API 密钥权限。'
          break
        case 404:
          errorMessage = '模型不存在 (404)，请检查模型名称是否正确。'
          break
        case 429:
          errorMessage = '请求频率过高 (429)，请稍后重试。'
          break
        case 500:
          errorMessage = '服务器内部错误 (500)，请稍后重试。'
          break
        default:
          errorMessage = `API 错误 (${error.status})：${error.message}`
      }
    }

    return {
      success: false,
      error: error.message,
      content: errorMessage
    }
  }
}



// 工具函数：文件转base64
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const base64 = reader.result.split(',')[1] // 移除data:前缀
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

// 模型信息获取 (基于最新 Pollinations.AI llms.txt)
export function getModelInfo(modelId) {
  const modelMap = {
    // Pollinations.AI 官方模型
    'openai': { name: 'OpenAI GPT-4.1-nano', provider: 'OpenAI', capabilities: ['text', 'vision'] },
    'openai-large': { name: 'OpenAI GPT-4.1 mini', provider: 'OpenAI', capabilities: ['text', 'vision'] },
    'openai-reasoning': { name: 'OpenAI o4-mini', provider: 'OpenAI', capabilities: ['text', 'reasoning'] },
    'openai-audio': { name: 'OpenAI GPT-4o-audio-preview', provider: 'OpenAI', capabilities: ['text', 'vision', 'audio'] },
    'qwen-coder': { name: 'Qwen 2.5 Coder 32B', provider: 'Alibaba', capabilities: ['text', 'code'] },
    'llama': { name: 'Llama 3.3 70B', provider: 'Meta', capabilities: ['text'] },
    'llamascout': { name: 'Llama 4 Scout 17B', provider: 'Meta', capabilities: ['text'] },
    'llama-vision': { name: 'Llama 3.2 11B Vision', provider: 'Meta', capabilities: ['text', 'vision'] },
    'mistral': { name: 'Mistral Small 3', provider: 'Mistral AI', capabilities: ['text', 'vision'] },
    'unity': { name: 'Unity Mistral Large', provider: 'Mistral AI', capabilities: ['text', 'vision', 'uncensored'] },
    'midijourney': { name: 'Midijourney', provider: 'Pollinations', capabilities: ['music', 'audio'] },
    'rtist': { name: 'Rtist', provider: 'Pollinations', capabilities: ['text', 'creative'] },
    'searchgpt': { name: 'SearchGPT', provider: 'Pollinations', capabilities: ['text', 'search'] },
    'evil': { name: 'Evil', provider: 'Pollinations', capabilities: ['text', 'vision', 'uncensored'] },
    'deepseek': { name: 'DeepSeek-V3', provider: 'DeepSeek', capabilities: ['text', 'reasoning'] },
    'deepseek-reasoning': { name: 'DeepSeek-R1 Distill Qwen 32B', provider: 'DeepSeek', capabilities: ['text', 'reasoning'] },
    'deepseek-reasoning-large': { name: 'DeepSeek R1 - Llama 70B', provider: 'DeepSeek', capabilities: ['text', 'reasoning'] },
    'phi': { name: 'Phi-4 Instruct', provider: 'Microsoft', capabilities: ['text', 'vision', 'audio'] },
    'gemini': { name: 'Gemini 2.5 Flash Preview', provider: 'Google', capabilities: ['text', 'vision', 'audio'] },
    'hormoz': { name: 'Hormoz 8b', provider: 'Pollinations', capabilities: ['text'] },
    'hypnosis-tracy': { name: 'Hypnosis Tracy 7B', provider: 'Pollinations', capabilities: ['text', 'audio'] },
    'sur': { name: 'Sur AI Assistant', provider: 'Pollinations', capabilities: ['text', 'vision'] },
    'flux': { name: 'Flux (Stable Diffusion)', provider: 'Pollinations', capabilities: ['image'] },
    'turbo': { name: 'Turbo (Fast Image Gen)', provider: 'Pollinations', capabilities: ['image'] },

    // GeminiPool 支持的模型 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash Experimental', provider: 'Google', capabilities: ['text', 'vision', 'reasoning', 'code'] },
    'gemini-2.0-flash-thinking-exp-1219': { name: 'Gemini 2.0 Flash Thinking Exp', provider: 'Google', capabilities: ['text', 'reasoning', 'thinking'] },
    'gemini-exp-1114': { name: 'Gemini Experimental 1114', provider: 'Google', capabilities: ['text', 'vision', 'reasoning'] },
    'gemini-exp-1121': { name: 'Gemini Experimental 1121', provider: 'Google', capabilities: ['text', 'vision', 'reasoning'] },
    'gemini-exp-1206': { name: 'Gemini Experimental 1206', provider: 'Google', capabilities: ['text', 'vision', 'reasoning'] },

    // Gemini 2.5 系列模型
    'gemini-2.5-flash-preview-05-20': { name: 'Gemini 2.5 Flash Preview 05-20', provider: 'Google', capabilities: ['text', 'vision', 'reasoning', 'preview'] },
    'gemini-2.5-flash': { name: 'Gemini 2.5 Flash', provider: 'Google', capabilities: ['text', 'vision', 'reasoning', 'fast'] },
    'gemini-2.5-flash-lite-preview-06-17': { name: 'Gemini 2.5 Flash Lite Preview 06-17', provider: 'Google', capabilities: ['text', 'vision', 'lightweight', 'preview'] },
    'gemini-2.5-pro': { name: 'Gemini 2.5 Pro', provider: 'Google', capabilities: ['text', 'vision', 'reasoning', 'code', 'advanced'] },
    'gemini-2.5-flash-lite': { name: 'Gemini 2.5 Flash Lite', provider: 'Google', capabilities: ['text', 'vision', 'lightweight', 'fast'] }
  }

  return modelMap[modelId] || { name: modelId, provider: 'Unknown', capabilities: ['text'] }
}

// 导出API配置
export { API_CONFIG, PollinationsError }
