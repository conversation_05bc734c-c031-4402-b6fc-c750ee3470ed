<template>
  <div style="min-height: 100vh; padding: 2rem; background: linear-gradient(135deg, #f3e8ff 0%, #fce7f3 50%, #dbeafe 100%);">
    <div style="max-width: 64rem; margin: 0 auto;">
      <h1 style="font-size: 2.5rem; font-weight: bold; text-align: center; margin-bottom: 2rem; background: linear-gradient(135deg, #9333ea 0%, #ec4899 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
        样式测试页面 - Tailwind CSS 测试
      </h1>

      <!-- 内联样式测试 -->
      <div style="margin-bottom: 2rem; padding: 1rem; background: white; border-radius: 0.5rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
        <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">内联样式测试</h2>
        <div style="display: flex; flex-direction: column; gap: 1rem;">
          <div style="padding: 1rem; background: #ef4444; color: white; border-radius: 0.25rem;">红色背景 (内联样式)</div>
          <div style="padding: 1rem; background: #3b82f6; color: white; border-radius: 0.25rem;">蓝色背景 (内联样式)</div>
          <div style="padding: 1rem; background: #10b981; color: white; border-radius: 0.25rem;">绿色背景 (内联样式)</div>
        </div>
      </div>

      <!-- Tailwind类测试 -->
      <div class="mb-8 p-4 bg-white rounded-lg shadow-lg">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800">Tailwind类测试</h2>
        <div class="space-y-4">
          <div class="p-4 bg-red-500 text-white rounded">红色背景 (bg-red-500)</div>
          <div class="p-4 bg-blue-500 text-white rounded">蓝色背景 (bg-blue-500)</div>
          <div class="p-4 bg-green-500 text-white rounded">绿色背景 (bg-green-500)</div>
          <div class="flex space-x-4">
            <div class="flex-1 p-4 bg-purple-500 text-white rounded">Flex 1</div>
            <div class="flex-1 p-4 bg-pink-500 text-white rounded">Flex 2</div>
          </div>
        </div>
      </div>
      
      <!-- 颜色测试 -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800">颜色测试</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-purple-500 text-white p-4 rounded-lg text-center">Purple</div>
          <div class="bg-pink-500 text-white p-4 rounded-lg text-center">Pink</div>
          <div class="bg-blue-500 text-white p-4 rounded-lg text-center">Blue</div>
          <div class="bg-green-500 text-white p-4 rounded-lg text-center">Green</div>
        </div>
      </div>
      
      <!-- 按钮测试 -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800">按钮测试</h2>
        <div class="flex flex-wrap gap-4">
          <button class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
            普通按钮
          </button>
          <button class="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all">
            渐变按钮
          </button>
          <button class="px-4 py-2 border-2 border-purple-500 text-purple-500 rounded-lg hover:bg-purple-500 hover:text-white transition-all">
            边框按钮
          </button>
        </div>
      </div>
      
      <!-- 表单测试 -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800">表单测试</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">输入框</label>
            <input 
              type="text" 
              placeholder="请输入内容..." 
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">文本域</label>
            <textarea 
              placeholder="请输入多行内容..." 
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors resize-none"
            ></textarea>
          </div>
        </div>
      </div>
      
      <!-- 卡片测试 -->
      <div class="grid md:grid-cols-2 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="h-32 bg-gradient-to-r from-purple-400 to-pink-400"></div>
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-2">卡片标题</h3>
            <p class="text-gray-600">这是一个测试卡片，用来验证样式是否正常工作。</p>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="h-32 bg-gradient-to-r from-blue-400 to-green-400"></div>
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-2">另一个卡片</h3>
            <p class="text-gray-600">Tailwind CSS 样式正在正常工作！</p>
          </div>
        </div>
      </div>
      
      <!-- 动画测试 -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800">动画测试</h2>
        <div class="flex flex-wrap gap-4">
          <div class="w-16 h-16 bg-purple-500 rounded-full animate-bounce"></div>
          <div class="w-16 h-16 bg-pink-500 rounded-full animate-pulse"></div>
          <div class="w-16 h-16 bg-blue-500 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这个页面用于测试样式是否正常工作
</script>

<style scoped>
/* 组件特定样式 */
</style>
