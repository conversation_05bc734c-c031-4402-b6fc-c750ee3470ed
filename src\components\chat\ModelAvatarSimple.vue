<template>
  <div class="model-avatar" :class="`size-${size}`">
    <div class="avatar-content">
      <!-- 使用 SVG 图标 -->
      <div v-if="modelIcon" class="model-icon" v-html="modelIcon"></div>
      <!-- 回退到文字 -->
      <span v-else class="avatar-text">{{ avatarText }}</span>
    </div>
    <div v-if="showStatus" class="status-indicator" :class="status"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getModelIcon, getModelColor } from './ModelIconsNew.js'

const props = defineProps({
  modelName: {
    type: String,
    required: true
  },
  status: {
    type: String,
    default: 'online',
    validator: (value) => ['online', 'offline', 'busy'].includes(value)
  },
  showStatus: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large', 'xlarge'].includes(value)
  }
})

// 计算属性
const modelIcon = computed(() => {
  return getModelIcon(props.modelName)
})

const avatarText = computed(() => {
  const words = props.modelName.split(/[\s-]/)
  if (words.length >= 2) {
    return (words[0][0] + words[1][0]).toUpperCase()
  }
  return props.modelName.substring(0, 2).toUpperCase()
})

// 移除了 avatarStyle 计算属性，现在完全依赖CSS样式
// 更新：为 Rtist、Evil、Hormoz、DeepSeek 等模型添加了专门的官方图标
</script>

<style lang="scss" scoped>
.model-avatar {
  position: relative;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
  background: transparent !important; /* 强制透明背景 */
  border: none !important; /* 移除任何边框 */
  box-shadow: none !important; /* 移除任何阴影 */

  // 不同尺寸
  &.size-small {
    width: 24px;
    height: 24px;
  }

  &.size-medium {
    width: 32px;
    height: 32px;
  }

  &.size-large {
    width: 64px;
    height: 64px;
  }

  &.size-xlarge {
    width: 80px;
    height: 80px;
  }
}

.avatar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: transparent !important; /* 确保内容区域透明 */
}

.model-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent !important; /* 确保图标区域透明 */

  svg {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    background: transparent !important; /* 确保SVG透明 */
    object-fit: contain; /* 确保SVG完整显示 */
  }
}

.avatar-text {
  background: transparent !important; /* 确保文字回退也透明 */
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 25%;
  height: 25%;
  border-radius: 50%;
  border: 2px solid white;

  &.online {
    background-color: #10b981;
  }

  &.offline {
    background-color: #ef4444;
  }

  &.busy {
    background-color: #f59e0b;
  }
}
</style>

<!-- 全局样式，确保模型头像在任何地方都显示为纯SVG -->
<style lang="scss">
.model-avatar,
.model-avatar *,
.model-avatar .avatar-content,
.model-avatar .model-icon,
.model-avatar svg {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}
</style>
