/**
 * Targon API 使用示例
 * 展示如何在项目中使用 Targon API 进行各种操作
 */

import targonApi from '@/services/targonApi'
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService'

// ================================
// 1. 基础配置和连接测试
// ================================

/**
 * 配置 Targon API
 */
export async function setupTargonApi() {
  // 设置 API 密钥（从环境变量或用户输入获取）
  const apiKey = 'your-targon-api-key-here'
  targonApi.setApiKey(apiKey)
  
  // 可选：设置自定义基础 URL
  targonApi.setBaseURL('https://api.targon.com/v1')
  
  // 保存到本地存储以便后续使用
  localStorage.setItem('targon_api_key', apiKey)
  
  console.log('✅ Targon API 配置完成')
}

/**
 * 测试 API 连接
 */
export async function testConnection() {
  try {
    const result = await targonApi.testConnection()
    
    if (result.success) {
      console.log('✅ 连接成功:', result.message)
      console.log('📊 可用模型数量:', result.data.modelsCount)
      return true
    } else {
      console.error('❌ 连接失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 连接测试异常:', error.message)
    return false
  }
}

// ================================
// 2. 模型管理
// ================================

/**
 * 获取可用模型列表
 */
export async function getAvailableModels() {
  try {
    const result = await targonApi.getModels()
    
    if (result.success) {
      console.log('📋 获取到模型列表:')
      result.data.forEach(model => {
        console.log(`  - ${model.id} (${model.owned_by || 'Unknown'})`)
      })
      return result.data
    } else {
      console.error('❌ 获取模型失败:', result.message)
      return []
    }
  } catch (error) {
    console.error('❌ 获取模型异常:', error.message)
    return []
  }
}

/**
 * 推荐模型选择
 */
export function getRecommendedModels() {
  return [
    {
      id: 'deepseek-ai/DeepSeek-V3',
      name: 'DeepSeek V3',
      description: '强大的推理能力，适合复杂任务',
      useCase: '代码生成、逻辑推理、复杂问答'
    },
    {
      id: 'deepseek-ai/DeepSeek-R1',
      name: 'DeepSeek R1',
      description: '专门优化的推理模型',
      useCase: '数学问题、逻辑推理、分析任务'
    },
    {
      id: 'moonshot/Kimi-K2-Instruct',
      name: 'Kimi K2',
      description: 'Moonshot AI 的指令优化模型',
      useCase: '对话、创作、通用任务'
    }
  ]
}

// ================================
// 3. 聊天功能示例
// ================================

/**
 * 简单聊天示例
 */
export async function simpleChatExample() {
  try {
    const response = await targonApi.chat({
      model: 'deepseek-ai/DeepSeek-V3',
      messages: [
        { role: 'user', content: '你好，请介绍一下你自己。' }
      ],
      temperature: 0.7,
      max_tokens: 200
    })
    
    if (response.success) {
      const aiMessage = response.data.choices[0].message.content
      console.log('🤖 AI 回复:', aiMessage)
      return aiMessage
    } else {
      console.error('❌ 聊天失败:', response.message)
      return null
    }
  } catch (error) {
    console.error('❌ 聊天异常:', error.message)
    return null
  }
}

/**
 * 多轮对话示例
 */
export async function multiTurnChatExample() {
  const conversation = [
    { role: 'user', content: '我想学习 JavaScript，有什么建议吗？' }
  ]
  
  try {
    // 第一轮对话
    let response = await targonApi.chat({
      model: 'deepseek-ai/DeepSeek-V3',
      messages: conversation,
      temperature: 0.7,
      max_tokens: 300
    })
    
    if (response.success) {
      const aiReply = response.data.choices[0].message.content
      conversation.push({ role: 'assistant', content: aiReply })
      console.log('🤖 AI:', aiReply)
      
      // 继续对话
      conversation.push({ role: 'user', content: '能推荐一些具体的学习资源吗？' })
      
      response = await targonApi.chat({
        model: 'deepseek-ai/DeepSeek-V3',
        messages: conversation,
        temperature: 0.7,
        max_tokens: 300
      })
      
      if (response.success) {
        const secondReply = response.data.choices[0].message.content
        console.log('🤖 AI:', secondReply)
        return conversation
      }
    }
  } catch (error) {
    console.error('❌ 多轮对话异常:', error.message)
  }
  
  return conversation
}

/**
 * 流式聊天示例
 */
export async function streamChatExample() {
  console.log('🌊 开始流式聊天...')
  
  let fullResponse = ''
  
  try {
    await new Promise((resolve, reject) => {
      targonApi.streamChat(
        {
          model: 'deepseek-ai/DeepSeek-V3',
          messages: [
            { role: 'user', content: '请写一首关于春天的短诗。' }
          ],
          temperature: 0.8,
          max_tokens: 200
        },
        // 消息回调
        (chunk) => {
          if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
            const content = chunk.choices[0].delta.content
            if (content) {
              process.stdout.write(content) // 实时输出
              fullResponse += content
            }
          }
        },
        // 错误回调
        (error) => {
          console.error('\n❌ 流式聊天错误:', error.message)
          reject(error)
        },
        // 完成回调
        () => {
          console.log('\n✅ 流式聊天完成')
          resolve()
        }
      )
    })
    
    return fullResponse
  } catch (error) {
    console.error('❌ 流式聊天异常:', error.message)
    return null
  }
}

// ================================
// 4. 统一聊天服务示例
// ================================

/**
 * 使用统一聊天服务
 */
export async function unifiedChatExample() {
  try {
    // 使用统一接口，系统会自动识别 Targon 模型
    const response = await generateChatCompletion(
      [
        { role: 'user', content: '解释一下什么是机器学习。' }
      ],
      {
        model: 'deepseek-ai/DeepSeek-V3', // 系统会自动路由到 Targon
        temperature: 0.7,
        max_tokens: 300
      }
    )
    
    console.log('🤖 统一服务回复:', response.choices[0].message.content)
    return response
  } catch (error) {
    console.error('❌ 统一服务异常:', error.message)
    return null
  }
}

/**
 * 使用统一流式服务
 */
export async function unifiedStreamExample() {
  console.log('🌊 使用统一流式服务...')
  
  let streamContent = ''
  
  try {
    await generateChatCompletionStream(
      [
        { role: 'user', content: '请简要介绍人工智能的发展历程。' }
      ],
      {
        model: 'deepseek-ai/DeepSeek-V3',
        temperature: 0.7,
        max_tokens: 400
      },
      // 内容回调
      (content) => {
        process.stdout.write(content)
        streamContent += content
      }
    )
    
    console.log('\n✅ 统一流式服务完成')
    return streamContent
  } catch (error) {
    console.error('\n❌ 统一流式服务异常:', error.message)
    return null
  }
}

// ================================
// 5. 完整使用流程示例
// ================================

/**
 * 完整的使用流程演示
 */
export async function fullUsageExample() {
  console.log('🚀 开始 Targon API 完整使用演示...\n')
  
  // 1. 配置 API
  await setupTargonApi()
  
  // 2. 测试连接
  const connected = await testConnection()
  if (!connected) {
    console.log('❌ 连接失败，演示终止')
    return
  }
  
  // 3. 获取模型列表
  const models = await getAvailableModels()
  if (models.length === 0) {
    console.log('❌ 无可用模型，演示终止')
    return
  }
  
  // 4. 简单聊天
  console.log('\n--- 简单聊天示例 ---')
  await simpleChatExample()
  
  // 5. 流式聊天
  console.log('\n--- 流式聊天示例 ---')
  await streamChatExample()
  
  // 6. 统一服务
  console.log('\n--- 统一服务示例 ---')
  await unifiedChatExample()
  
  console.log('\n✅ 完整演示结束')
}

// 导出所有示例函数
export default {
  setup: setupTargonApi,
  test: testConnection,
  models: getAvailableModels,
  simpleChat: simpleChatExample,
  multiTurnChat: multiTurnChatExample,
  streamChat: streamChatExample,
  unifiedChat: unifiedChatExample,
  unifiedStream: unifiedStreamExample,
  fullDemo: fullUsageExample
}
