/**
 * Targon 模型修复工具
 * 用于验证和修复 Targon 模型列表问题
 */

import targonApi from '@/services/targonApi'
import { getTargonModels } from '@/config/aiModels'

/**
 * 检查 GLM 模型是否存在
 */
export function checkGLMModels() {
  const presetModels = targonApi.getPresetModels()
  
  const glmModels = {
    'GLM-4.5-Air': presetModels.find(m => m.id.includes('GLM-4.5-Air')),
    'GLM-4.5': presetModels.find(m => m.id.includes('GLM-4.5') && !m.id.includes('Air')),
    'GLM-4-Plus': presetModels.find(m => m.id.includes('GLM-4-Plus'))
  }
  
  const results = {
    total: presetModels.length,
    glmCount: Object.values(glmModels).filter(Boolean).length,
    glmModels,
    allModels: presetModels.map(m => ({ id: m.id, name: m.name }))
  }
  
  console.log('🔍 GLM 模型检查结果:', results)
  return results
}

/**
 * 获取完整的 Targon 模型列表
 */
export async function getFullTargonModels() {
  try {
    console.log('📋 获取 Targon 模型列表...')
    
    // 获取预设模型
    const presetModels = targonApi.getPresetModels()
    console.log(`✅ 预设模型: ${presetModels.length} 个`)
    
    // 尝试从 API 获取模型
    let apiModels = []
    try {
      const result = await targonApi.getModels()
      if (result.success && result.data) {
        apiModels = result.data
        console.log(`✅ API 模型: ${apiModels.length} 个`)
      } else {
        console.warn(`⚠️ API 获取失败: ${result.message}`)
      }
    } catch (error) {
      console.warn(`⚠️ API 调用异常: ${error.message}`)
    }
    
    // 获取配置中的模型
    let configModels = []
    try {
      configModels = await getTargonModels()
      console.log(`✅ 配置模型: ${configModels.length} 个`)
    } catch (error) {
      console.warn(`⚠️ 配置获取失败: ${error.message}`)
    }
    
    return {
      preset: presetModels,
      api: apiModels,
      config: configModels,
      summary: {
        presetCount: presetModels.length,
        apiCount: apiModels.length,
        configCount: configModels.length,
        glmInPreset: presetModels.filter(m => m.id.includes('GLM')).length,
        glmInApi: apiModels.filter(m => m.id && m.id.includes('GLM')).length,
        glmInConfig: configModels.filter(m => m.includes('GLM')).length
      }
    }
  } catch (error) {
    console.error('❌ 获取模型列表失败:', error)
    throw error
  }
}

/**
 * 验证模型配置
 */
export function validateModelConfig() {
  const issues = []
  
  try {
    const presetModels = targonApi.getPresetModels()
    
    // 检查必需的 GLM 模型
    const requiredGLMModels = ['GLM-4.5-Air', 'GLM-4.5', 'GLM-4-Plus']
    const missingGLMModels = requiredGLMModels.filter(required => 
      !presetModels.some(model => model.id.includes(required))
    )
    
    if (missingGLMModels.length > 0) {
      issues.push({
        type: 'missing_models',
        message: `缺少 GLM 模型: ${missingGLMModels.join(', ')}`,
        severity: 'error'
      })
    }
    
    // 检查模型数量
    if (presetModels.length < 10) {
      issues.push({
        type: 'insufficient_models',
        message: `模型数量不足: 当前 ${presetModels.length} 个，建议至少 10 个`,
        severity: 'warning'
      })
    }
    
    // 检查模型格式
    presetModels.forEach((model, index) => {
      if (!model.id || !model.name || !model.description) {
        issues.push({
          type: 'invalid_format',
          message: `模型 ${index + 1} 格式不完整: ${model.id || '未知ID'}`,
          severity: 'error'
        })
      }
    })
    
    const result = {
      valid: issues.filter(i => i.severity === 'error').length === 0,
      issues,
      modelCount: presetModels.length,
      glmCount: presetModels.filter(m => m.id.includes('GLM')).length
    }
    
    console.log('🔧 模型配置验证结果:', result)
    return result
    
  } catch (error) {
    console.error('❌ 验证模型配置失败:', error)
    return {
      valid: false,
      issues: [{
        type: 'validation_error',
        message: `验证失败: ${error.message}`,
        severity: 'error'
      }],
      modelCount: 0,
      glmCount: 0
    }
  }
}

/**
 * 修复模型配置（如果可能）
 */
export function fixModelConfig() {
  console.log('🔧 尝试修复模型配置...')
  
  const validation = validateModelConfig()
  
  if (validation.valid) {
    console.log('✅ 模型配置正常，无需修复')
    return { success: true, message: '配置正常' }
  }
  
  const fixes = []
  
  validation.issues.forEach(issue => {
    switch (issue.type) {
      case 'missing_models':
        fixes.push('需要手动添加缺失的 GLM 模型到预设列表')
        break
      case 'insufficient_models':
        fixes.push('建议添加更多模型到预设列表')
        break
      case 'invalid_format':
        fixes.push('需要修复模型格式问题')
        break
      default:
        fixes.push(`需要处理: ${issue.message}`)
    }
  })
  
  console.log('🔧 建议的修复措施:', fixes)
  
  return {
    success: false,
    message: '需要手动修复',
    fixes,
    issues: validation.issues
  }
}

/**
 * 运行完整的诊断
 */
export async function runFullDiagnostic() {
  console.log('🚀 开始 Targon 模型完整诊断...')
  
  const results = {
    timestamp: new Date().toISOString(),
    glmCheck: null,
    modelLists: null,
    validation: null,
    fix: null
  }
  
  try {
    // 1. 检查 GLM 模型
    console.log('1️⃣ 检查 GLM 模型...')
    results.glmCheck = checkGLMModels()
    
    // 2. 获取模型列表
    console.log('2️⃣ 获取模型列表...')
    results.modelLists = await getFullTargonModels()
    
    // 3. 验证配置
    console.log('3️⃣ 验证模型配置...')
    results.validation = validateModelConfig()
    
    // 4. 尝试修复
    console.log('4️⃣ 尝试修复配置...')
    results.fix = fixModelConfig()
    
    // 生成报告
    const report = generateDiagnosticReport(results)
    console.log('📊 诊断报告:', report)
    
    return {
      success: true,
      results,
      report
    }
    
  } catch (error) {
    console.error('❌ 诊断过程失败:', error)
    return {
      success: false,
      error: error.message,
      results
    }
  }
}

/**
 * 生成诊断报告
 */
function generateDiagnosticReport(results) {
  const report = {
    summary: '🎯 Targon 模型诊断报告',
    status: 'unknown',
    details: [],
    recommendations: []
  }
  
  // GLM 模型检查
  if (results.glmCheck) {
    const glmCount = results.glmCheck.glmCount
    if (glmCount >= 3) {
      report.details.push(`✅ GLM 模型: ${glmCount}/3 个已找到`)
      report.status = 'good'
    } else if (glmCount > 0) {
      report.details.push(`⚠️ GLM 模型: ${glmCount}/3 个已找到`)
      report.status = 'warning'
      report.recommendations.push('添加缺失的 GLM 模型')
    } else {
      report.details.push(`❌ GLM 模型: 0/3 个已找到`)
      report.status = 'error'
      report.recommendations.push('添加所有 GLM 模型')
    }
  }
  
  // 模型总数检查
  if (results.modelLists) {
    const presetCount = results.modelLists.summary.presetCount
    report.details.push(`📋 预设模型总数: ${presetCount} 个`)
    
    if (presetCount < 8) {
      report.status = 'error'
      report.recommendations.push('增加预设模型数量')
    } else if (presetCount < 12) {
      if (report.status !== 'error') report.status = 'warning'
      report.recommendations.push('建议添加更多模型')
    }
  }
  
  // 验证结果
  if (results.validation) {
    if (results.validation.valid) {
      report.details.push('✅ 模型配置验证通过')
    } else {
      report.details.push(`❌ 模型配置验证失败: ${results.validation.issues.length} 个问题`)
      report.status = 'error'
      results.validation.issues.forEach(issue => {
        report.recommendations.push(issue.message)
      })
    }
  }
  
  // 设置最终状态
  if (report.status === 'unknown') {
    report.status = 'good'
  }
  
  return report
}

// 导出默认对象
export default {
  checkGLMModels,
  getFullTargonModels,
  validateModelConfig,
  fixModelConfig,
  runFullDiagnostic,
  generateDiagnosticReport
}
