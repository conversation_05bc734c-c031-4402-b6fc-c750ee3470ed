<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 400" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
  <!-- 背景 -->
  <rect width="800" height="400" fill="url(#gradient)"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 装饰性几何图形 -->
  <circle cx="100" cy="100" r="30" fill="rgba(255,255,255,0.1)"/>
  <circle cx="700" cy="300" r="40" fill="rgba(255,255,255,0.1)"/>
  <polygon points="50,350 80,300 110,350" fill="rgba(255,255,255,0.1)"/>
  <polygon points="720,80 750,30 780,80" fill="rgba(255,255,255,0.1)"/>
  
  <!-- 主标题 -->
  <text x="400" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="url(#textGradient)">
    🎨 AI创作助手平台
  </text>
  
  <!-- 副标题 -->
  <text x="400" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="rgba(255,255,255,0.9)">
    释放创意无限可能，让AI成为您的创作伙伴
  </text>
  
  <!-- 功能图标 -->
  <g transform="translate(200, 280)">
    <rect x="0" y="0" width="80" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
    <text x="40" y="25" text-anchor="middle" font-size="20">💬</text>
    <text x="40" y="45" text-anchor="middle" font-size="12" fill="white">AI聊天</text>
  </g>
  
  <g transform="translate(320, 280)">
    <rect x="0" y="0" width="80" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
    <text x="40" y="25" text-anchor="middle" font-size="20">🎨</text>
    <text x="40" y="45" text-anchor="middle" font-size="12" fill="white">AI绘画</text>
  </g>
  
  <g transform="translate(440, 280)">
    <rect x="0" y="0" width="80" height="60" rx="10" fill="rgba(255,255,255,0.2)"/>
    <text x="40" y="25" text-anchor="middle" font-size="20">🖼️</text>
    <text x="40" y="45" text-anchor="middle" font-size="12" fill="white">作品展示</text>
  </g>
  
  <!-- 技术标签 -->
  <text x="400" y="370" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">
    Vue 3 • Vite 5.x • Element Plus • Pinia
  </text>
</svg>
