// API密钥存储服务 - 用于管理员添加和管理密钥
import { ElMessage } from 'element-plus'

const STORAGE_KEY = 'ai_creative_api_keys'
const CONFIG_KEY = 'ai_creative_api_config'

// 默认配置
const DEFAULT_CONFIG = {
  keyRotationEnabled: true,
  failureThreshold: 3,
  healthCheckInterval: 300000, // 5分钟
  maxRetries: 3,
  timeout: 60000
}

// 密钥存储类
export class ApiKeyStorage {
  constructor() {
    this.keys = this.loadKeys()
    this.config = this.loadConfig()
  }

  // 从本地存储加载密钥
  loadKeys() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const keys = JSON.parse(stored)
        return keys.map((key, index) => ({
          ...key,
          index: index + 1,
          id: key.id || this.generateKeyId()
        }))
      }
    } catch (error) {
      console.error('加载API密钥失败:', error)
    }
    return []
  }

  // 保存密钥到本地存储
  saveKeys() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.keys))
      return true
    } catch (error) {
      console.error('保存API密钥失败:', error)
      ElMessage.error('保存密钥失败: ' + error.message)
      return false
    }
  }

  // 从本地存储加载配置
  loadConfig() {
    try {
      const stored = localStorage.getItem(CONFIG_KEY)
      if (stored) {
        return { ...DEFAULT_CONFIG, ...JSON.parse(stored) }
      }
    } catch (error) {
      console.error('加载API配置失败:', error)
    }
    return { ...DEFAULT_CONFIG }
  }

  // 保存配置到本地存储
  saveConfig() {
    try {
      localStorage.setItem(CONFIG_KEY, JSON.stringify(this.config))
      return true
    } catch (error) {
      console.error('保存API配置失败:', error)
      ElMessage.error('保存配置失败: ' + error.message)
      return false
    }
  }

  // 生成密钥ID
  generateKeyId() {
    return 'key_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 添加新密钥
  addKey(keyData) {
    try {
      // 验证密钥格式
      if (!keyData.key || !keyData.key.startsWith('sk-or-v1-')) {
        throw new Error('无效的OpenRouter API密钥格式')
      }

      // 检查是否已存在
      if (this.keys.some(k => k.key === keyData.key)) {
        throw new Error('此API密钥已存在')
      }

      const newKey = {
        id: this.generateKeyId(),
        name: keyData.name || `密钥 #${this.keys.length + 1}`,
        key: keyData.key,
        description: keyData.description || '',
        priority: keyData.priority || 2,
        isActive: true,
        isCurrent: this.keys.length === 0, // 第一个密钥设为当前
        createdAt: new Date().toISOString(),
        createdBy: 'admin', // 这里可以从用户store获取
        
        // 统计信息
        totalRequests: 0,
        successfulRequests: 0,
        failureCount: 0,
        lastUsed: null,
        lastSuccess: null,
        lastFailure: null,
        responseTime: 0,
        
        // 预览信息
        keyPreview: keyData.key.substring(0, 15) + '***' + keyData.key.substring(keyData.key.length - 4)
      }

      this.keys.push(newKey)
      this.saveKeys()
      
      ElMessage.success('API密钥添加成功')
      return newKey.id
    } catch (error) {
      ElMessage.error('添加密钥失败: ' + error.message)
      throw error
    }
  }

  // 删除密钥
  removeKey(keyId) {
    try {
      const index = this.keys.findIndex(k => k.id === keyId)
      if (index === -1) {
        throw new Error('密钥不存在')
      }

      if (this.keys.length <= 1) {
        throw new Error('至少需要保留一个API密钥')
      }

      const removedKey = this.keys[index]
      this.keys.splice(index, 1)

      // 重新编号
      this.keys.forEach((key, newIndex) => {
        key.index = newIndex + 1
      })

      // 如果删除的是当前密钥，切换到第一个
      if (removedKey.isCurrent && this.keys.length > 0) {
        this.keys[0].isCurrent = true
      }

      this.saveKeys()
      ElMessage.success('API密钥删除成功')
      return true
    } catch (error) {
      ElMessage.error('删除密钥失败: ' + error.message)
      throw error
    }
  }

  // 更新密钥信息
  updateKey(keyId, updates) {
    try {
      const key = this.keys.find(k => k.id === keyId)
      if (!key) {
        throw new Error('密钥不存在')
      }

      Object.assign(key, updates, {
        updatedAt: new Date().toISOString()
      })

      this.saveKeys()
      return true
    } catch (error) {
      ElMessage.error('更新密钥失败: ' + error.message)
      throw error
    }
  }

  // 切换当前密钥
  switchToKey(keyId) {
    try {
      const targetKey = this.keys.find(k => k.id === keyId)
      if (!targetKey) {
        throw new Error('密钥不存在')
      }

      if (!targetKey.isActive) {
        throw new Error('无法切换到不可用的密钥')
      }

      // 取消所有密钥的当前状态
      this.keys.forEach(key => {
        key.isCurrent = false
      })

      // 设置目标密钥为当前
      targetKey.isCurrent = true
      targetKey.lastUsed = new Date().toISOString()

      this.saveKeys()
      ElMessage.success(`已切换到密钥: ${targetKey.name}`)
      return true
    } catch (error) {
      ElMessage.error('切换密钥失败: ' + error.message)
      throw error
    }
  }

  // 测试密钥
  async testKey(keyId) {
    try {
      const key = this.keys.find(k => k.id === keyId)
      if (!key) {
        throw new Error('密钥不存在')
      }

      const startTime = Date.now()

      try {
        // 创建AbortController用于超时控制
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

        // 发送真实的API测试请求
        const baseUrl = import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1'
        const response = await fetch(`${baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${key.key}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin,
            'X-Title': 'AI Creative Platform'
          },
          body: JSON.stringify({
            model: 'deepseek/deepseek-chat-v3-0324:free', // 使用免费模型进行测试
            messages: [
              {
                role: 'user',
                content: 'Hello! This is a test message. Please respond with "Test successful".'
              }
            ],
            max_tokens: 10,
            temperature: 0.1
          }),
          signal: controller.signal
        })

        clearTimeout(timeoutId)
        const responseTime = Date.now() - startTime

        if (response.ok) {
          const data = await response.json()

          // 更新成功统计
          key.totalRequests++
          key.successfulRequests++
          key.lastSuccess = new Date().toISOString()
          key.responseTime = Math.round((key.responseTime + responseTime) / 2)
          key.status = 'active'

          key.lastUsed = new Date().toISOString()
          this.saveKeys()

          const result = {
            success: true,
            responseTime: responseTime,
            message: '密钥测试成功',
            keyName: key.name,
            response: data.choices?.[0]?.message?.content || '测试响应'
          }

          ElMessage.success(`${key.name} 测试成功 (${responseTime}ms)`)
          return result
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
      } catch (error) {
        const responseTime = Date.now() - startTime

        // 更新失败统计
        key.totalRequests++
        key.failureCount++
        key.lastFailure = new Date().toISOString()

        // 如果失败次数过多，标记为不可用
        if (key.failureCount >= 3) {
          key.status = 'inactive'
        }

        key.lastUsed = new Date().toISOString()
        this.saveKeys()

        const result = {
          success: false,
          responseTime: responseTime,
          message: error.name === 'AbortError' ? '请求超时' : error.message,
          keyName: key.name,
          error: error.message
        }

        ElMessage.error(`${key.name} 测试失败: ${result.message}`)
        return result
      }
    } catch (error) {
      ElMessage.error('测试密钥失败: ' + error.message)
      throw error
    }
  }

  // 重置所有密钥状态
  resetAllKeys() {
    try {
      this.keys.forEach(key => {
        key.isActive = true
        key.failureCount = 0
        key.lastFailure = null
      })

      this.saveKeys()
      ElMessage.success('所有密钥状态已重置')
      return true
    } catch (error) {
      ElMessage.error('重置密钥失败: ' + error.message)
      throw error
    }
  }

  // 更新系统配置
  updateConfig(newConfig) {
    try {
      this.config = { ...this.config, ...newConfig }
      this.saveConfig()
      ElMessage.success('系统配置已更新')
      return true
    } catch (error) {
      ElMessage.error('更新配置失败: ' + error.message)
      throw error
    }
  }

  // 获取所有密钥
  getAllKeys() {
    return [...this.keys]
  }

  // 获取当前密钥
  getCurrentKey() {
    return this.keys.find(key => key.isCurrent) || this.keys[0]
  }

  // 获取配置
  getConfig() {
    return { ...this.config }
  }

  // 导出数据
  exportData() {
    return {
      keys: this.keys.map(key => ({
        ...key,
        key: key.key.substring(0, 15) + '***' // 隐藏完整密钥
      })),
      config: this.config,
      exportTime: new Date().toISOString()
    }
  }

  // 获取统计信息
  getStats() {
    const totalKeys = this.keys.length
    const activeKeys = this.keys.filter(key => key.isActive).length
    const totalRequests = this.keys.reduce((sum, key) => sum + key.totalRequests, 0)
    const totalSuccessful = this.keys.reduce((sum, key) => sum + key.successfulRequests, 0)
    const averageResponseTime = this.keys.length > 0 
      ? Math.round(this.keys.reduce((sum, key) => sum + key.responseTime, 0) / this.keys.length)
      : 0
    const overallSuccessRate = totalRequests > 0 
      ? ((totalSuccessful / totalRequests) * 100).toFixed(1)
      : '0.0'

    return {
      totalKeys,
      activeKeys,
      totalRequests,
      totalSuccessful,
      averageResponseTime,
      overallSuccessRate
    }
  }
}

// 创建全局实例
export const apiKeyStorage = new ApiKeyStorage()

// 导出默认实例
export default apiKeyStorage
