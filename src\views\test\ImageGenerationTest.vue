<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">图像生成测试页面</h1>
        
        <!-- Pollinations API 测试 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Pollinations API 测试</h2>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">图像描述</label>
              <textarea
                v-model="testPrompt"
                placeholder="输入您想要生成的图像描述..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
              ></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">宽度</label>
                <input
                  v-model.number="testOptions.width"
                  type="number"
                  min="256"
                  max="1024"
                  step="64"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">高度</label>
                <input
                  v-model.number="testOptions.height"
                  type="number"
                  min="256"
                  max="1024"
                  step="64"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  v-model="testOptions.enhance"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">增强画质</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="testOptions.safe"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">安全模式</span>
              </label>
            </div>
            
            <button
              @click="testPollinationsAPI"
              :disabled="loading || !testPrompt.trim()"
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {{ loading ? '生成中...' : '测试 Pollinations API' }}
            </button>
          </div>
        </div>

        <!-- 音频功能测试 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Pollinations 音频功能测试</h2>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">文本转语音</label>
              <textarea
                v-model="audioTestText"
                placeholder="输入要转换为语音的文本..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                rows="2"
              ></textarea>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">语音</label>
                <select
                  v-model="selectedVoice"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option v-for="voice in availableVoices" :key="voice" :value="voice">
                    {{ voice }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">音频生成描述</label>
                <input
                  v-model="audioGenerationPrompt"
                  type="text"
                  placeholder="描述要生成的音频..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            <div class="flex space-x-4">
              <button
                @click="testTextToSpeech"
                :disabled="audioLoading || !audioTestText.trim()"
                class="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {{ audioLoading ? '转换中...' : '测试文本转语音' }}
              </button>

              <button
                @click="testAudioGeneration"
                :disabled="audioLoading || !audioGenerationPrompt.trim()"
                class="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {{ audioLoading ? '生成中...' : '测试音频生成' }}
              </button>
            </div>
          </div>
        </div>

        <!-- GeminiPool API 测试 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">GeminiPool API 测试</h2>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
              <select
                v-model="selectedGeminiModel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option v-for="model in geminiModels" :key="model" :value="model">
                  {{ model }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">测试消息</label>
              <textarea
                v-model="geminiTestMessage"
                placeholder="输入测试消息..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                rows="3"
              ></textarea>
            </div>

            <button
              @click="testGeminiAPI"
              :disabled="geminiLoading || !geminiTestMessage.trim()"
              class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {{ geminiLoading ? '请求中...' : '测试 GeminiPool API' }}
            </button>
          </div>
        </div>
        
        <!-- 结果显示 -->
        <div v-if="results.length > 0" class="space-y-6">
          <h2 class="text-xl font-semibold text-gray-800">测试结果</h2>
          
          <div
            v-for="(result, index) in results"
            :key="index"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-gray-600">{{ result.type }}</span>
              <span class="text-xs text-gray-500">{{ result.timestamp }}</span>
            </div>
            
            <div v-if="result.type === 'Pollinations 图像生成'">
              <div v-if="result.success" class="space-y-3">
                <img
                  :src="result.data.imageUrl"
                  :alt="result.data.prompt"
                  class="max-w-full h-auto rounded-lg shadow-md"
                />
                <div class="text-sm text-gray-600">
                  <p><strong>提示词:</strong> {{ result.data.prompt }}</p>
                  <p><strong>种子:</strong> {{ result.data.seed }}</p>
                  <p><strong>尺寸:</strong> {{ result.data.options.width }}×{{ result.data.options.height }}</p>
                  <p><strong>处理时间:</strong> {{ result.data.metadata?.processingTime }}ms</p>
                </div>
              </div>
              <div v-else class="text-red-600">
                <p><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>
            
            <div v-else-if="result.type === '文本转语音'">
              <div v-if="result.success" class="space-y-3">
                <div class="bg-purple-50 p-3 rounded-md">
                  <p class="text-sm text-gray-600 mb-2"><strong>原文本:</strong></p>
                  <p>{{ result.data.text }}</p>
                </div>
                <div class="flex items-center space-x-4">
                  <audio :src="result.data.audioUrl" controls class="flex-1">
                    您的浏览器不支持音频播放
                  </audio>
                  <button
                    @click="audioService.downloadAudio(result.data.audioUrl, `tts_${Date.now()}.mp3`)"
                    class="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                  >
                    下载
                  </button>
                </div>
                <div class="text-sm text-gray-600">
                  <p><strong>语音:</strong> {{ result.data.voice }}</p>
                </div>
              </div>
              <div v-else class="text-red-600">
                <p><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>

            <div v-else-if="result.type === '音频生成'">
              <div v-if="result.success" class="space-y-3">
                <div class="bg-indigo-50 p-3 rounded-md">
                  <p class="text-sm text-gray-600 mb-2"><strong>生成描述:</strong></p>
                  <p>{{ result.data.prompt }}</p>
                </div>
                <div class="flex items-center space-x-4">
                  <audio :src="result.data.audioUrl" controls class="flex-1">
                    您的浏览器不支持音频播放
                  </audio>
                  <button
                    @click="audioService.downloadAudio(result.data.audioUrl, `generated_${Date.now()}.mp3`)"
                    class="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700"
                  >
                    下载
                  </button>
                </div>
              </div>
              <div v-else class="text-red-600">
                <p><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>

            <div v-else-if="result.type === 'Gemini 对话'">
              <div v-if="result.success" class="space-y-3">
                <div class="bg-gray-50 p-3 rounded-md">
                  <p class="text-sm text-gray-600 mb-2"><strong>用户:</strong></p>
                  <p>{{ result.data.userMessage }}</p>
                </div>
                <div class="bg-blue-50 p-3 rounded-md">
                  <p class="text-sm text-gray-600 mb-2"><strong>Gemini:</strong></p>
                  <p>{{ result.data.response }}</p>
                </div>
                <div class="text-sm text-gray-600">
                  <p><strong>模型:</strong> {{ result.data.model }}</p>
                  <p><strong>Token使用:</strong> {{ result.data.usage?.totalTokens || 'N/A' }}</p>
                </div>
              </div>
              <div v-else class="text-red-600">
                <p><strong>错误:</strong> {{ result.error }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import imageGenerationService from '@/services/imageGenerationService.js'
import { generateChatCompletion } from '@/services/pollinationsApi.js'
import audioService, { SUPPORTED_VOICES } from '@/services/audioService.js'

// 响应式数据
const loading = ref(false)
const geminiLoading = ref(false)
const audioLoading = ref(false)
const results = ref([])

// Pollinations 测试数据
const testPrompt = ref('一只可爱的小猫坐在花园里，阳光明媚，水彩画风格')
const testOptions = ref({
  width: 512,
  height: 512,
  enhance: true,
  safe: true
})

// 音频测试数据
const audioTestText = ref('你好，这是一个音频测试。')
const audioGenerationPrompt = ref('轻松愉快的背景音乐')
const selectedVoice = ref('alloy')
const availableVoices = SUPPORTED_VOICES

// Gemini 测试数据
const geminiTestMessage = ref('你好，请简单介绍一下自己')
const selectedGeminiModel = ref('gemini-2.5-flash')

// 可用的 Gemini 模型 (仅保留 2.0 和 2.5 系列)
const geminiModels = [
  // Gemini 2.5 系列 (最新推荐)
  'gemini-2.5-flash-preview-05-20',
  'gemini-2.5-flash',
  'gemini-2.5-flash-lite-preview-06-17',
  'gemini-2.5-pro',
  'gemini-2.5-flash-lite',

  // Gemini 2.0 系列
  'gemini-2.0-flash-exp',
  'gemini-2.0-flash-thinking-exp-1219',

  // 实验版本
  'gemini-exp-1114',
  'gemini-exp-1121',
  'gemini-exp-1206'
]

// 测试 Pollinations API
const testPollinationsAPI = async () => {
  loading.value = true
  
  try {
    ElMessage.info('正在测试 Pollinations API...')
    
    const result = await imageGenerationService.generateImage(testPrompt.value, testOptions.value)
    
    results.value.unshift({
      type: 'Pollinations 图像生成',
      success: result.success,
      data: result.success ? result : null,
      error: result.success ? null : result.error,
      timestamp: new Date().toLocaleString()
    })
    
    if (result.success) {
      ElMessage.success('Pollinations API 测试成功！')
    } else {
      ElMessage.error('Pollinations API 测试失败')
    }
  } catch (error) {
    console.error('Pollinations API 测试错误:', error)
    ElMessage.error('测试过程中出现错误')
    
    results.value.unshift({
      type: 'Pollinations 图像生成',
      success: false,
      data: null,
      error: error.message,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    loading.value = false
  }
}

// 测试 Gemini API
const testGeminiAPI = async () => {
  geminiLoading.value = true
  
  try {
    ElMessage.info('正在测试 Gemini API...')
    
    const messages = [
      { role: 'user', content: geminiTestMessage.value }
    ]
    
    const result = await generateChatCompletion(messages, {
      model: selectedGeminiModel.value,
      temperature: 0.7
    })
    
    results.value.unshift({
      type: 'Gemini 对话',
      success: result.success,
      data: result.success ? {
        userMessage: geminiTestMessage.value,
        response: result.content,
        model: result.model,
        usage: result.usage
      } : null,
      error: result.success ? null : result.error,
      timestamp: new Date().toLocaleString()
    })
    
    if (result.success) {
      ElMessage.success('Gemini API 测试成功！')
    } else {
      ElMessage.error('Gemini API 测试失败')
    }
  } catch (error) {
    console.error('Gemini API 测试错误:', error)
    ElMessage.error('测试过程中出现错误')
    
    results.value.unshift({
      type: 'Gemini 对话',
      success: false,
      data: null,
      error: error.message,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    geminiLoading.value = false
  }
}

// 测试文本转语音
const testTextToSpeech = async () => {
  audioLoading.value = true

  try {
    ElMessage.info('正在测试文本转语音...')

    const result = await audioService.textToSpeech(audioTestText.value, {
      voice: selectedVoice.value,
      model: 'openai-audio'
    })

    results.value.unshift({
      type: '文本转语音',
      success: result.success,
      data: result.success ? {
        text: audioTestText.value,
        voice: selectedVoice.value,
        audioUrl: result.audioUrl
      } : null,
      error: result.success ? null : result.error,
      timestamp: new Date().toLocaleString()
    })

    if (result.success) {
      ElMessage.success('文本转语音测试成功！')
      // 自动播放音频
      audioService.playAudio(result.audioUrl)
    } else {
      ElMessage.error('文本转语音测试失败')
    }
  } catch (error) {
    console.error('文本转语音测试错误:', error)
    ElMessage.error('测试过程中出现错误')

    results.value.unshift({
      type: '文本转语音',
      success: false,
      data: null,
      error: error.message,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    audioLoading.value = false
  }
}

// 测试音频生成
const testAudioGeneration = async () => {
  audioLoading.value = true

  try {
    ElMessage.info('正在测试音频生成...')

    const result = await audioService.generateAudio(audioGenerationPrompt.value, {
      duration: 10,
      style: 'music'
    })

    results.value.unshift({
      type: '音频生成',
      success: result.success,
      data: result.success ? {
        prompt: audioGenerationPrompt.value,
        audioUrl: result.audioUrl
      } : null,
      error: result.success ? null : result.error,
      timestamp: new Date().toLocaleString()
    })

    if (result.success) {
      ElMessage.success('音频生成测试成功！')
      // 自动播放音频
      audioService.playAudio(result.audioUrl)
    } else {
      ElMessage.error('音频生成测试失败')
    }
  } catch (error) {
    console.error('音频生成测试错误:', error)
    ElMessage.error('测试过程中出现错误')

    results.value.unshift({
      type: '音频生成',
      success: false,
      data: null,
      error: error.message,
      timestamp: new Date().toLocaleString()
    })
  } finally {
    audioLoading.value = false
  }
}
</script>
