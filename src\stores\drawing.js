import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { APP_CONFIG, CONSTANTS } from '@/config'
// import { generateImageApi, getDrawingHistoryApi, deleteDrawing<PERSON>pi } from '@/api/drawing'
import { generateId } from '@/utils/common'

export const useDrawingStore = defineStore('drawing', () => {
  // 状态
  const drawings = ref([])
  const currentDrawing = ref(null)
  const loading = ref(false)
  const progress = ref(0)
  const settings = ref({
    style: 'realistic',
    size: '512x512',
    quality: 'standard',
    steps: 20,
    guidance: 7.5,
  })

  // 计算属性
  const hasDrawings = computed(() => drawings.value.length > 0)

  const completedDrawings = computed(() => {
    return drawings.value.filter(drawing => drawing.status === CONSTANTS.DRAWING_STATUS.COMPLETED)
  })

  const pendingDrawings = computed(() => {
    return drawings.value.filter(drawing => 
      drawing.status === CONSTANTS.DRAWING_STATUS.PENDING || 
      drawing.status === CONSTANTS.DRAWING_STATUS.PROCESSING
    )
  })

  const failedDrawings = computed(() => {
    return drawings.value.filter(drawing => drawing.status === CONSTANTS.DRAWING_STATUS.FAILED)
  })

  // 生成图像
  const generateImage = async (prompt, negativePrompt = '') => {
    if (!prompt.trim()) {
      ElMessage.warning('请输入绘画描述')
      return
    }

    // 创建绘画任务
    const drawing = {
      id: generateId(),
      prompt: prompt.trim(),
      negativePrompt: negativePrompt.trim(),
      settings: { ...settings.value },
      status: CONSTANTS.DRAWING_STATUS.PENDING,
      imageUrl: '',
      thumbnailUrl: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      progress: 0,
    }

    drawings.value.unshift(drawing)
    currentDrawing.value = drawing

    try {
      loading.value = true
      progress.value = 0
      drawing.status = CONSTANTS.DRAWING_STATUS.PROCESSING

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        if (progress.value < 90) {
          progress.value += Math.random() * 15
          drawing.progress = progress.value
        }
      }, 800)

      // 模拟AI图像生成 - 前端模拟
      const mockImages = [
        'https://picsum.photos/512/512?random=1',
        'https://picsum.photos/512/512?random=2',
        'https://picsum.photos/512/512?random=3',
        'https://picsum.photos/512/512?random=4',
        'https://picsum.photos/512/512?random=5',
        'https://picsum.photos/512/512?random=6',
        'https://picsum.photos/512/512?random=7',
        'https://picsum.photos/512/512?random=8'
      ]

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000))

      clearInterval(progressInterval)

      // 随机选择一个模拟图片
      const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)]

      // 更新绘画结果
      drawing.status = CONSTANTS.DRAWING_STATUS.COMPLETED
      drawing.imageUrl = randomImage
      drawing.thumbnailUrl = randomImage
      drawing.updatedAt = new Date().toISOString()
      drawing.progress = 100
      progress.value = 100

      // 保存到本地存储
      saveDrawingHistory()

      ElMessage.success('图像生成成功')
      return { success: true, drawing }
    } catch (error) {
      console.error('生成图像错误:', error)
      drawing.status = CONSTANTS.DRAWING_STATUS.FAILED
      drawing.updatedAt = new Date().toISOString()
      ElMessage.error('图像生成失败，请检查网络连接')
      return { success: false, message: '网络错误' }
    } finally {
      loading.value = false
      progress.value = 0
    }
  }

  // 重新生成图像
  const regenerateImage = async (drawingId) => {
    const drawing = drawings.value.find(d => d.id === drawingId)
    if (!drawing) return

    await generateImage(drawing.prompt, drawing.negativePrompt)
  }

  // 删除绘画 - 前端模拟
  const deleteDrawing = async (drawingId) => {
    try {
      const drawing = drawings.value.find(d => d.id === drawingId)
      if (!drawing) return

      // 从列表中移除
      drawings.value = drawings.value.filter(d => d.id !== drawingId)

      // 如果是当前绘画，清空当前绘画
      if (currentDrawing.value?.id === drawingId) {
        currentDrawing.value = null
      }

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 300))

      // 保存到本地存储
      saveDrawingHistory()

      ElMessage.success('绘画已删除')
    } catch (error) {
      console.error('删除绘画错误:', error)
      ElMessage.error('删除绘画失败')
    }
  }

  // 收藏/取消收藏绘画
  const toggleFavorite = (drawingId) => {
    const drawing = drawings.value.find(d => d.id === drawingId)
    if (drawing) {
      drawing.isFavorite = !drawing.isFavorite
      drawing.updatedAt = new Date().toISOString()
      saveDrawingHistory()
    }
  }

  // 分享绘画
  const shareDrawing = async (drawingId, isPublic = true) => {
    try {
      const drawing = drawings.value.find(d => d.id === drawingId)
      if (!drawing) return

      drawing.isPublic = isPublic
      drawing.updatedAt = new Date().toISOString()

      // 这里可以调用API更新分享状态
      // await updateDrawingShareStatusApi(drawingId, isPublic)

      saveDrawingHistory()
      ElMessage.success(isPublic ? '已公开分享' : '已取消分享')
    } catch (error) {
      console.error('分享绘画错误:', error)
      ElMessage.error('分享操作失败')
    }
  }

  // 下载图像
  const downloadImage = async (drawing) => {
    try {
      if (!drawing.imageUrl) {
        ElMessage.warning('图像不存在')
        return
      }

      // 创建下载链接
      const link = document.createElement('a')
      link.href = drawing.imageUrl
      link.download = `ai-drawing-${drawing.id}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success('开始下载')
    } catch (error) {
      console.error('下载图像错误:', error)
      ElMessage.error('下载失败')
    }
  }

  // 更新绘画设置
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem('drawing_settings', JSON.stringify(settings.value))
  }

  // 设置当前绘画
  const setCurrentDrawing = (drawing) => {
    currentDrawing.value = drawing
  }

  // 保存绘画历史到本地存储
  const saveDrawingHistory = () => {
    const drawingData = {
      drawings: drawings.value,
      currentDrawing: currentDrawing.value,
    }
    localStorage.setItem('drawing_history', JSON.stringify(drawingData))
  }

  // 从本地存储加载绘画历史
  const loadDrawingHistory = async () => {
    try {
      console.log('🎨 开始加载绘画历史...')

      // 安全加载绘画历史数据
      try {
        const savedData = localStorage.getItem('drawing_history')
        if (savedData) {
          const drawingData = JSON.parse(savedData)
          drawings.value = Array.isArray(drawingData.drawings) ? drawingData.drawings : []
          currentDrawing.value = drawingData.currentDrawing || null
          console.log(`✅ 绘画历史加载完成: ${drawings.value.length}个作品`)
        } else {
          console.log('🎨 没有找到绘画历史数据')
        }
      } catch (historyError) {
        console.warn('⚠️ 绘画历史数据损坏，已重置:', historyError)
        localStorage.removeItem('drawing_history')
        drawings.value = []
        currentDrawing.value = null
      }

      // 安全加载绘画设置
      try {
        const savedSettings = localStorage.getItem('drawing_settings')
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings)
          settings.value = { ...settings.value, ...parsedSettings }
          console.log('✅ 绘画设置加载完成')
        } else {
          console.log('⚙️ 使用默认绘画设置')
        }
      } catch (settingsError) {
        console.warn('⚠️ 绘画设置数据损坏，使用默认设置:', settingsError)
        localStorage.removeItem('drawing_settings')
      }
    } catch (error) {
      console.error('❌ 加载绘画历史失败:', error)
      // 重置为默认状态，但不抛出错误
      drawings.value = []
      currentDrawing.value = null
      console.warn('⚠️ 绘画历史加载失败，已重置为空状态')
    }
  }

  // 清空绘画历史
  const clearDrawingHistory = () => {
    drawings.value = []
    currentDrawing.value = null
    localStorage.removeItem('drawing_history')
    ElMessage.success('绘画历史已清空')
  }

  // 获取绘画统计
  const getDrawingStats = () => {
    return {
      total: drawings.value.length,
      completed: completedDrawings.value.length,
      pending: pendingDrawings.value.length,
      failed: failedDrawings.value.length,
      favorites: drawings.value.filter(d => d.isFavorite).length,
      public: drawings.value.filter(d => d.isPublic).length,
    }
  }

  // 重置状态
  const resetState = () => {
    drawings.value = []
    currentDrawing.value = null
    loading.value = false
    progress.value = 0
    settings.value = {
      style: 'realistic',
      size: '512x512',
      quality: 'standard',
      steps: 20,
      guidance: 7.5,
    }
  }

  return {
    // 状态
    drawings,
    currentDrawing,
    loading,
    progress,
    settings,
    
    // 计算属性
    hasDrawings,
    completedDrawings,
    pendingDrawings,
    failedDrawings,
    
    // 方法
    generateImage,
    regenerateImage,
    deleteDrawing,
    toggleFavorite,
    shareDrawing,
    downloadImage,
    updateSettings,
    setCurrentDrawing,
    saveDrawingHistory,
    loadDrawingHistory,
    clearDrawingHistory,
    getDrawingStats,
    resetState,
  }
})
