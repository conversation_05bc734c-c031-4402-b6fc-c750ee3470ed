// 全局样式文件 - 使用现代@use语法
// 所有@use规则必须在其他规则之前
@use './variables.scss' as *;
@use './mixins.scss' as *;
@use './gentle-reset.scss';
@use './common.scss';
@use './element-plus.scss';
@use './element-fix.scss';
@use './mobile.scss';
@use './chat-mobile.scss';

// TailwindCSS 基础样式
@tailwind base;
@tailwind components;
@tailwind utilities;

// 全局字体设置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 全局强制模型头像显示为纯SVG图标 */
.model-avatar,
.model-avatar *,
.model-avatar .avatar-content,
.model-avatar .model-icon,
.model-avatar svg,
[class*="model-avatar"] {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 确保模型选择器中的头像也是透明的 */
.model-selector .model-avatar,
.model-selector .model-avatar *,
.model-card .model-avatar,
.model-card .model-avatar *,
.current-model .model-avatar,
.current-model .model-avatar * {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 确保模型选择器中的SVG图标足够大 */
.model-selector .model-avatar svg,
.model-card .model-avatar svg {
  min-width: 64px !important;
  min-height: 64px !important;
  width: 100% !important;
  height: 100% !important;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 居中布局
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 隐藏可能的调试边框和开发工具样式
* {
  outline: none !important;
}

// 确保没有调试相关的红色边框
[style*="border: 1px solid red"],
[style*="border: 1px solid #ff0000"],
[style*="border: 2px solid red"],
[style*="border: 2px solid #ff0000"] {
  border: none !important;
}

// 隐藏可能的Vue DevTools相关样式
.__vue-devtools-highlight {
  display: none !important;
}

// 确保应用容器没有调试样式
#app {
  border: none !important;
  outline: none !important;
}
