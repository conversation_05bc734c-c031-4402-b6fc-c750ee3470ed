// 混合器文件 - 使用现代@use语法
@use './variables.scss' as *;

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-xs) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (max-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (max-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (max-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (max-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 渐变背景
@mixin gradient-bg($start: $ai-gradient-start, $end: $ai-gradient-end, $direction: 135deg) {
  background: linear-gradient($direction, $start, $end);
}

// 卡片样式
@mixin card-style($padding: $spacing-md, $radius: $border-radius-base) {
  background: $bg-color;
  border-radius: $radius;
  box-shadow: $box-shadow-base;
  padding: $padding;
}

// 按钮样式
@mixin button-style($bg: $primary-color, $color: #fff, $hover-bg: $primary-light-1) {
  background-color: $bg;
  color: $color;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: $hover-bg;
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Flex布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 动画
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 阴影
@mixin box-shadow($shadow: $box-shadow-base) {
  box-shadow: $shadow;
}

// 聊天气泡样式
@mixin chat-bubble($bg: $chat-bubble-ai, $color: $text-color-primary, $align: left) {
  background-color: $bg;
  color: $color;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-base;
  max-width: 70%;
  word-wrap: break-word;
  
  @if $align == right {
    margin-left: auto;
    border-bottom-right-radius: 4px;
  } @else {
    margin-right: auto;
    border-bottom-left-radius: 4px;
  }
}
