<script setup>
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import ErrorBoundary from '@/components/common/ErrorBoundary.vue'

// 应用启动时初始化用户状态
onMounted(async () => {
  try {
    console.log('🚀 App.vue: 开始初始化用户状态...')
    
    // 动态导入用户store
    const { useUserStore } = await import('@/stores/user')
    const userStore = useUserStore()
    
    // 初始化用户状态
    await userStore.initUserState()
    
    console.log('✅ App.vue: 用户状态初始化完成', {
      isLoggedIn: userStore.isLoggedIn,
      username: userStore.userName
    })
  } catch (error) {
    console.error('❌ App.vue: 用户状态初始化失败:', error)
    // 不阻止应用启动，继续运行
  }
})
</script>

<template>
  <div id="app">
    <ErrorBoundary>
      <RouterView />
    </ErrorBoundary>
  </div>
</template>

<style>
#app {
  height: 100vh;
  width: 100vw;
}
</style>
