<template>
  <div class="api-test">
    <h1>API测试页面</h1>
    
    <div class="test-section">
      <h2>模型列表测试</h2>
      <button @click="testModels" :disabled="loading">测试获取模型</button>
      <div v-if="modelsResult">
        <h3>结果：</h3>
        <pre>{{ JSON.stringify(modelsResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>简单文本生成测试</h2>
      <input v-model="simplePrompt" placeholder="输入测试提示词" />
      <button @click="testSimpleGeneration" :disabled="loading">测试简单生成</button>
      <div v-if="simpleResult">
        <h3>结果：</h3>
        <pre>{{ JSON.stringify(simpleResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>聊天完成测试</h2>
      <input v-model="chatPrompt" placeholder="输入聊天消息" />
      <select v-model="selectedTestModel">
        <option value="openai">OpenAI</option>
        <option value="mistral">Mistral</option>
        <option value="llama">Llama</option>
        <option value="deepseek">DeepSeek</option>
      </select>
      <button @click="testChatCompletion" :disabled="loading">测试聊天</button>
      <div v-if="chatResult">
        <h3>结果：</h3>
        <pre>{{ JSON.stringify(chatResult, null, 2) }}</pre>
      </div>
    </div>

    <div v-if="error" class="error">
      <h3>错误：</h3>
      <pre>{{ error }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getAIModels } from '@/config/aiModels.js'
import { generateTextSimple, generateChatCompletion } from '@/services/pollinationsApi.js'

const loading = ref(false)
const error = ref('')

// 模型测试
const modelsResult = ref(null)

// 简单生成测试
const simplePrompt = ref('你好，请介绍一下自己')
const simpleResult = ref(null)

// 聊天测试
const chatPrompt = ref('你好，请介绍一下自己')
const selectedTestModel = ref('openai')
const chatResult = ref(null)

const testModels = async () => {
  loading.value = true
  error.value = ''
  try {
    const models = await getAIModels()
    modelsResult.value = models
    console.log('模型列表:', models)
  } catch (err) {
    error.value = err.message
    console.error('模型测试失败:', err)
  } finally {
    loading.value = false
  }
}

const testSimpleGeneration = async () => {
  if (!simplePrompt.value.trim()) return
  
  loading.value = true
  error.value = ''
  try {
    const result = await generateTextSimple(simplePrompt.value)
    simpleResult.value = result
    console.log('简单生成结果:', result)
  } catch (err) {
    error.value = err.message
    console.error('简单生成测试失败:', err)
  } finally {
    loading.value = false
  }
}

const testChatCompletion = async () => {
  if (!chatPrompt.value.trim()) return
  
  loading.value = true
  error.value = ''
  try {
    const messages = [
      { role: 'user', content: chatPrompt.value }
    ]
    const result = await generateChatCompletion(messages, {
      model: selectedTestModel.value,
      temperature: 0.7,
      maxTokens: 500
    })
    chatResult.value = result
    console.log('聊天完成结果:', result)
  } catch (err) {
    error.value = err.message
    console.error('聊天测试失败:', err)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.api-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
}

input, select {
  margin: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  margin: 0.5rem;
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

pre {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.error {
  background: #ffe6e6;
  border: 1px solid #ff9999;
  padding: 1rem;
  border-radius: 4px;
  color: #cc0000;
}
</style>
