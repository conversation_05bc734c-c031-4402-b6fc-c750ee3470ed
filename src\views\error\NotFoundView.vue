<template>
  <div class="not-found-view">
    <div class="error-container">
      <div class="error-content">
        <div class="error-illustration">
          <div class="error-number">404</div>
          <div class="error-icon">
            <el-icon><QuestionFilled /></el-icon>
          </div>
        </div>
        
        <div class="error-text">
          <h1>页面不存在</h1>
          <p>抱歉，您访问的页面不存在或已被移除</p>
        </div>

        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>

        <div class="suggestions">
          <h3>您可以尝试：</h3>
          <ul>
            <li>检查网址是否正确</li>
            <li>返回首页重新导航</li>
            <li>使用搜索功能查找内容</li>
            <li>联系我们获取帮助</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { QuestionFilled, House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/home')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/home')
  }
}
</script>

<style lang="scss" scoped>
.not-found-view {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-light-9, $bg-color-page);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
}

.error-container {
  max-width: 600px;
  text-align: center;
}

.error-content {
  background: $bg-color;
  border-radius: $border-radius-base;
  padding: $spacing-xl;
  box-shadow: $box-shadow-base;

  .error-illustration {
    position: relative;
    margin-bottom: $spacing-xl;

    .error-number {
      font-size: 8rem;
      font-weight: 900;
      color: $primary-light-3;
      line-height: 1;
      margin-bottom: $spacing-md;

      @include respond-to(sm) {
        font-size: 6rem;
      }
    }

    .error-icon {
      .el-icon {
        font-size: 4rem;
        color: $primary-color;
      }
    }
  }

  .error-text {
    margin-bottom: $spacing-xl;

    h1 {
      font-size: 2rem;
      font-weight: 600;
      color: $text-color-primary;
      margin: 0 0 $spacing-md 0;

      @include respond-to(sm) {
        font-size: 1.5rem;
      }
    }

    p {
      font-size: 1.1rem;
      color: $text-color-secondary;
      margin: 0;
      line-height: 1.6;

      @include respond-to(sm) {
        font-size: 1rem;
      }
    }
  }

  .error-actions {
    display: flex;
    gap: $spacing-md;
    justify-content: center;
    margin-bottom: $spacing-xl;

    @include respond-to(sm) {
      flex-direction: column;
      align-items: center;

      .el-button {
        width: 200px;
      }
    }
  }

  .suggestions {
    text-align: left;
    background: $bg-color-secondary;
    border-radius: $border-radius-base;
    padding: $spacing-lg;

    h3 {
      margin: 0 0 $spacing-md 0;
      font-size: 1.1rem;
      color: $text-color-primary;
    }

    ul {
      margin: 0;
      padding-left: $spacing-lg;
      color: $text-color-secondary;

      li {
        margin-bottom: $spacing-xs;
        line-height: 1.6;
      }
    }
  }
}
</style>
