/**
 * Claude API 服务
 * 支持 Claude 3.5 Sonnet, Claude 3.7 Sonnet, Claude 4 Sonnet 等模型
 */

import axios from 'axios'

class ClaudeApi {
  constructor() {
    this.baseURL = 'https://happy-cod-75.deno.dev/v1/chat/completions'
    this.apiKey = 'sk-test-123456'
    this.timeout = 60000
    this.models = [
      {
        id: 'claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet',
        description: 'Claude 3.5 Sonnet - 平衡性能与效率',
        maxTokens: 200000,
        supportsFunctions: true,
        pricing: '第三方 API'
      },
      {
        id: 'claude-3.7-sonnet',
        name: 'Claude 3.7 Sonnet',
        description: 'Claude 3.7 Sonnet - 增强版本',
        maxTokens: 200000,
        supportsFunctions: true,
        pricing: '第三方 API'
      },
      {
        id: 'claude-4-sonnet',
        name: 'Claude 4 Sonnet',
        description: 'Claude 4 Sonnet - 最新最强版本',
        maxTokens: 200000,
        supportsFunctions: true,
        pricing: '第三方 API'
      },
      {
        id: 'deepseek-r1',
        name: 'DeepSeek R1',
        description: 'DeepSeek R1 - 推理优化模型',
        maxTokens: 128000,
        supportsFunctions: true,
        pricing: '第三方 API'
      },
      {
        id: 'openai-gpt-4.1',
        name: 'OpenAI GPT-4.1',
        description: 'OpenAI GPT-4.1 - 最新 GPT 模型',
        maxTokens: 128000,
        supportsFunctions: true,
        pricing: '第三方 API'
      }
    ]
  }

  /**
   * 设置 API Key
   * @param {string} apiKey - Claude API Key
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey
    console.log('✅ Claude API Key 已设置')
  }

  /**
   * 获取当前 API Key
   * @returns {string|null} 当前的 API Key
   */
  getApiKey() {
    return this.apiKey
  }

  /**
   * 创建 HTTP 客户端
   * @returns {Object} axios 实例
   */
  createClient() {
    if (!this.apiKey) {
      throw new Error('Claude API Key 未设置')
    }

    return axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Creative-Platform/1.0'
      }
    })
  }

  /**
   * 获取可用模型列表
   * @returns {Promise<Object>} 模型列表响应
   */
  async getModels() {
    try {
      console.log('📋 获取 Claude 模型列表...')
      
      return {
        success: true,
        data: this.models,
        message: '获取模型列表成功'
      }
    } catch (error) {
      console.error('❌ 获取 Claude 模型列表失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 发送聊天请求
   * @param {Object} params - 请求参数
   * @param {string} params.model - 模型名称
   * @param {Array} params.messages - 消息数组
   * @param {number} params.temperature - 温度参数 (0-1)
   * @param {number} params.max_tokens - 最大token数
   * @param {boolean} params.stream - 是否流式输出
   * @returns {Promise<Object>} 聊天响应
   */
  async createChatCompletion(params) {
    try {
      const client = this.createClient()
      
      console.log('🤖 发送 Claude 聊天请求:', {
        model: params.model,
        messageCount: params.messages?.length,
        temperature: params.temperature,
        maxTokens: params.max_tokens,
        stream: params.stream
      })

      // 构建请求数据
      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 4000,
        stream: params.stream || false
      }

      // 发送请求
      const response = await client.post('', requestData)

      console.log('✅ Claude 聊天请求成功')
      
      return {
        success: true,
        data: response.data,
        message: '聊天请求成功'
      }
    } catch (error) {
      console.error('❌ Claude 聊天请求失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 流式聊天请求
   * @param {Object} params - 请求参数
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   * @returns {Promise<void>}
   */
  async createChatCompletionStream(params, onMessage, onError, onComplete) {
    try {
      const client = this.createClient()
      
      console.log('🌊 发送 Claude 流式聊天请求:', {
        model: params.model,
        messageCount: params.messages?.length
      })

      // 构建请求数据
      const requestData = {
        model: params.model,
        messages: params.messages,
        temperature: params.temperature || 0.7,
        max_tokens: params.max_tokens || 4000,
        stream: true
      }

      // 发送流式请求
      const response = await client.post('', requestData, {
        responseType: 'stream'
      })

      let buffer = ''
      
      response.data.on('data', (chunk) => {
        buffer += chunk.toString()
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.trim() === '') continue
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              onComplete?.()
              return
            }
            
            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                onMessage?.(content)
              }
            } catch (e) {
              console.warn('解析流式数据失败:', e)
            }
          }
        }
      })

      response.data.on('end', () => {
        onComplete?.()
      })

      response.data.on('error', (error) => {
        console.error('❌ Claude 流式请求错误:', error)
        onError?.(error)
      })

    } catch (error) {
      console.error('❌ Claude 流式聊天请求失败:', error)
      onError?.(error)
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          return 'API Key 无效或已过期'
        case 403:
          return '访问被拒绝，请检查 API Key 权限'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return 'Claude 服务器内部错误'
        case 503:
          return 'Claude 服务暂时不可用'
        default:
          return data?.error?.message || data?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    } else if (error.code === 'ENOTFOUND') {
      return '无法连接到 Claude 服务器'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 测试 API 连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      console.log('🔍 测试 Claude API 连接...')

      const testMessages = [
        { role: 'user', content: '你好，请简单回复确认连接正常。' }
      ]

      const result = await this.createChatCompletion({
        model: 'claude-3.5-sonnet',
        messages: testMessages,
        max_tokens: 50,
        temperature: 0.1
      })

      if (result.success) {
        console.log('✅ Claude API 连接测试成功')
        return {
          success: true,
          message: 'Claude API 连接正常',
          response: result.data
        }
      } else {
        return result
      }
    } catch (error) {
      console.error('❌ Claude API 连接测试失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }
}

// 创建单例实例
const claudeApi = new ClaudeApi()

export default claudeApi
