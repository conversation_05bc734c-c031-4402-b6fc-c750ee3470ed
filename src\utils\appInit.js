// 全局应用初始化状态管理器
import { ref } from 'vue'

// 全局初始化状态
const isAppInitialized = ref(false)
const isPiniaReady = ref(false)
const initializationPromise = ref(null)

// 初始化监听器
const initListeners = []

// 设置Pinia准备完成
export function setPiniaReady() {
  isPiniaReady.value = true
  console.log('✅ Pinia 已准备就绪')
  checkAppInitialization()
}

// 设置应用初始化完成
export function setAppInitialized() {
  isAppInitialized.value = true
  console.log('✅ 应用初始化完成')
  
  // 通知所有监听器
  initListeners.forEach(listener => {
    try {
      listener()
    } catch (error) {
      console.warn('初始化监听器执行失败:', error)
    }
  })
  
  // 清空监听器
  initListeners.length = 0
}

// 检查应用初始化状态
function checkAppInitialization() {
  if (isPiniaReady.value && !isAppInitialized.value) {
    // 等待一个tick确保所有异步操作完成
    setTimeout(() => {
      setAppInitialized()
    }, 100)
  }
}

// 等待应用初始化完成
export function waitForAppInitialization() {
  if (isAppInitialized.value) {
    return Promise.resolve()
  }
  
  if (!initializationPromise.value) {
    initializationPromise.value = new Promise((resolve) => {
      if (isAppInitialized.value) {
        resolve()
        return
      }
      
      initListeners.push(resolve)
      
      // 设置超时，防止无限等待
      setTimeout(() => {
        console.warn('应用初始化超时，强制继续')
        resolve()
      }, 5000)
    })
  }
  
  return initializationPromise.value
}

// 安全地初始化store
export async function safeInitializeStore(storeImportFn, storeName = 'store') {
  try {
    // 等待应用初始化完成
    await waitForAppInitialization()
    
    // 再等待一小段时间确保Pinia完全准备好
    await new Promise(resolve => setTimeout(resolve, 50))
    
    // 导入并初始化store
    const storeModule = await storeImportFn()
    const store = storeModule()
    
    console.log(`✅ ${storeName} 初始化成功`)
    return store
  } catch (error) {
    console.warn(`❌ ${storeName} 初始化失败:`, error)
    
    // 重试机制
    await new Promise(resolve => setTimeout(resolve, 200))
    try {
      const storeModule = await storeImportFn()
      const store = storeModule()
      console.log(`✅ ${storeName} 重试初始化成功`)
      return store
    } catch (retryError) {
      console.error(`❌ ${storeName} 重试初始化也失败:`, retryError)
      return null
    }
  }
}

// 获取初始化状态
export function getInitializationStatus() {
  return {
    isAppInitialized: isAppInitialized.value,
    isPiniaReady: isPiniaReady.value
  }
}

// 安全地使用store的组合式函数
export function useSafeStore(storeFunction, defaultValue = null) {
  try {
    // 检查Pinia是否准备好
    if (!isPiniaReady.value) {
      console.warn('Pinia尚未准备好，返回默认值')
      return defaultValue
    }

    // 尝试获取store
    const store = storeFunction()
    return store
  } catch (error) {
    console.warn('Store访问失败，返回默认值:', error)
    return defaultValue
  }
}

// 安全地访问store属性
export function safeStoreAccess(storeFunction, propertyPath, defaultValue = null) {
  try {
    const store = useSafeStore(storeFunction)
    if (!store) return defaultValue

    // 支持嵌套属性访问，如 'user.name'
    const properties = propertyPath.split('.')
    let value = store

    for (const prop of properties) {
      if (value && typeof value === 'object' && prop in value) {
        value = value[prop]
      } else {
        return defaultValue
      }
    }

    return value
  } catch (error) {
    console.warn(`Store属性访问失败 (${propertyPath}):`, error)
    return defaultValue
  }
}

// 安全地调用store方法
export function safeStoreMethod(storeFunction, methodName, ...args) {
  try {
    const store = useSafeStore(storeFunction)
    if (!store) {
      console.warn(`Store不可用，无法调用方法: ${methodName}`)
      return false
    }

    if (typeof store[methodName] === 'function') {
      store[methodName](...args)
      return true
    } else {
      console.warn(`Store方法不存在: ${methodName}`)
      return false
    }
  } catch (error) {
    console.warn(`Store方法调用失败 (${methodName}):`, error)
    return false
  }
}
