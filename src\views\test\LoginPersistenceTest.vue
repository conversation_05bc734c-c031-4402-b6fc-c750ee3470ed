<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">
          🔐 登录状态持久化测试
        </h1>
        
        <!-- 当前登录状态 -->
        <div class="mb-8 p-6 bg-gray-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">当前登录状态</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <span class="font-medium">登录状态:</span>
                <span :class="isLoggedIn ? 'text-green-600' : 'text-red-600'">
                  {{ isLoggedIn ? '✅ 已登录' : '❌ 未登录' }}
                </span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="font-medium">用户名:</span>
                <span class="text-blue-600">{{ userName }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="font-medium">用户角色:</span>
                <span class="text-purple-600">{{ userRole }}</span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <span class="font-medium">Token存在:</span>
                <span :class="hasToken ? 'text-green-600' : 'text-red-600'">
                  {{ hasToken ? '✅ 是' : '❌ 否' }}
                </span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="font-medium">用户信息:</span>
                <span :class="hasUserInfo ? 'text-green-600' : 'text-red-600'">
                  {{ hasUserInfo ? '✅ 存在' : '❌ 不存在' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 本地存储信息 -->
        <div class="mb-8 p-6 bg-blue-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">本地存储信息</h2>
          <div class="space-y-2 text-sm">
            <div>
              <span class="font-medium">Token:</span>
              <code class="ml-2 p-1 bg-white rounded text-xs">
                {{ localToken ? localToken.substring(0, 50) + '...' : '无' }}
              </code>
            </div>
            <div>
              <span class="font-medium">用户信息:</span>
              <pre class="mt-2 p-3 bg-white rounded text-xs overflow-auto">{{ localUserInfo }}</pre>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 justify-center">
          <button
            @click="refreshPage"
            class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            🔄 刷新页面测试
          </button>
          
          <button
            @click="clearStorage"
            class="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            🗑️ 清除本地存储
          </button>
          
          <button
            @click="forceReload"
            class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            ⚡ 强制重新加载
          </button>
          
          <router-link
            to="/login"
            class="px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors inline-block"
          >
            🔑 去登录页面
          </router-link>
        </div>

        <!-- 测试说明 -->
        <div class="mt-8 p-6 bg-yellow-50 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">测试说明</h2>
          <ol class="list-decimal list-inside space-y-2 text-sm">
            <li>首先确保您已经登录</li>
            <li>点击"刷新页面测试"按钮，检查登录状态是否保持</li>
            <li>或者直接按F5刷新浏览器，检查登录状态</li>
            <li>如果登录状态丢失，说明持久化有问题</li>
            <li>如果登录状态保持，说明持久化正常工作</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import { APP_CONFIG } from '@/config'

const userStore = useUserStore()

// 响应式数据
const localToken = ref('')
const localUserInfo = ref('')

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userName = computed(() => userStore.userName)
const userRole = computed(() => userStore.userRole)
const hasToken = computed(() => !!userStore.token)
const hasUserInfo = computed(() => !!userStore.userInfo)

// 加载本地存储信息
const loadLocalStorageInfo = () => {
  localToken.value = getToken() || ''
  
  try {
    const userInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
    localUserInfo.value = userInfo ? JSON.stringify(JSON.parse(userInfo), null, 2) : '无'
  } catch (error) {
    localUserInfo.value = '解析失败: ' + error.message
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 清除本地存储
const clearStorage = () => {
  if (confirm('确定要清除所有本地存储吗？这将退出登录。')) {
    localStorage.clear()
    window.location.reload()
  }
}

// 强制重新加载
const forceReload = () => {
  window.location.href = window.location.href
}

onMounted(() => {
  loadLocalStorageInfo()
  
  // 每秒更新一次本地存储信息
  setInterval(loadLocalStorageInfo, 1000)
})
</script>
