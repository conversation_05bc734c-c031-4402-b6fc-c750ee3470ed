/**
 * Targon API 密钥池配置
 * 包含所有可用的 Targon API 密钥
 */

export const TARGON_API_KEYS = [
  // 第一批密钥 (1-50)
  'sn4_flxher23klyekojixf6p73vwdafr', 'sn4_02iqzz7hiht769dh2rs052pjqexx', 'sn4_jlirb6v4pr8hn0oa4t1re55ucydo',
  'sn4_scnlu110zsabg3dpfr8fvbwsluwt', 'sn4_miewnql2fp5bh1dbtyx490yeuy5g', 'sn4_d4h1iv1nf2g4vhv36hytbp4pr6yj',
  'sn4_f1a74s5mv89z7co7oyzicc7p308c', 'sn4_tvl9lwbp03vl0qekw7hxo6nlwnz2', 'sn4_cydrctzr7o30qlzl00pmxcb6x2og',
  'sn4_eqzvm5j8wrr553vebobesuhvq4h1', 'sn4_kq4ydsazdsbzaswyotkbqkk3ocvd', 'sn4_gbftlfjstsd9216nqbob39a3c154',
  'sn4_heeeh7m3y0kfz6p1vrj701y9zs14', 'sn4_52x9xc10bz6ybi0g73mz4f6t781r', 'sn4_xxk33r1d80z1jvzav0sn7v9tvxv2',
  'sn4_twzs0kh80zlfq3gio56pim5trzik', 'sn4_8go487zkmvkswsf8wf0s7uwdi18a', 'sn4_p9xxrv6i0pqpknpl8j6ao0cc7r0o',
  'sn4_c4qtbz6l7ztgmrpnrazzxglr2cay', 'sn4_m479ta5nadiy4wpajfi2uj6afkoq', 'sn4_lmdhdz0l44jhwyflndc543153lg1',
  'sn4_2dndogsrlrmwr30ogf23dnlgx6j8', 'sn4_ev85f0fn2ge9vn9b2eyd6rl8dslt', 'sn4_3vgrxlayot4hm28mzttso4cwrzvz',
  'sn4_v8naqjz2y51tmr28xpyi00hy37wb', 'sn4_rontcxo4vqdisag6hr8ddg77dld9', 'sn4_fkpl153yn15rd3ypjr88vwuwb6c7',
  'sn4_e32n5ja702pv0lg3etk8jwnpsamh', 'sn4_gdeva84iyj1cuxe349we4i9b98yv', 'sn4_37bg3mhlnf89el6f0dxf1afuesl7',
  'sn4_xzhtw1v8xboli04vqg4n9m4chmdo', 'sn4_r143peeskkn6qzllb2u6fbju7ymy', 'sn4_aom4jiucoubztrg5e1hwy0ihhswl',
  'sn4_a37iu44q7eymbvg2cyk6guljqjn0', 'sn4_jd4c9zsawd1lrfwue4gp9id9gcak', 'sn4_n81432fgwsq9bl8nxzf8k15ykgqf',
  'sn4_vitaqw70uexf9ynho8zf9l0b0eu5', 'sn4_skibtsbe2wbnamkb9vqfzz5ckh8e', 'sn4_dbk4zb5e4xl1ms8kvces7sy9a54f',
  'sn4_ruyg6c8jb241uh9j4n1hl4hf04w4', 'sn4_si0m3svly6gm3lh627r8ec1axkqh', 'sn4_yru0auhl1qljyqhszw3hmvxhf7k3',
  'sn4_6jiw602m1swgq94w88pf8r8hm2pg', 'sn4_vyiedhphunlvsftt3mospfvvzjhe', 'sn4_uv5ikpl2vh38zduvo3df3djr9obb',
  'sn4_azavhi4yblwk66aalseq0y08m3j1', 'sn4_o4cixbdgaxwdcdv1wicqjclhk9by', 'sn4_ntffpv4hyykaaowqjt9msfurhi84',
  'sn4_f8rgc74bcl1q7v6gnprihfu2o9dy', 'sn4_1cf6a1iyqqcxwliaw2n7pljsjozh', 'sn4_0ork5j8oh5pwa8lf65tt22fx1mq9',
  'sn4_8mxiwk11is1slby6law77w8oefpd', 'sn4_ilbx5pf58mu6i5knf3kef5x63df2', 'sn4_hnq3ok16wkrmc6lzrx9fkaloot2y',

  // 第二批密钥 (51-100)
  'sn4_gr3tdbuzjzt6jovm1gldhy0o00cq', 'sn4_rd0vk5dx15wk2j34ldwci4gihx37', 'sn4_8xp7yvqykrtl36s8kefgg8b8p11w',
  'sn4_j1du9czkealntgvrgeqikov23b9i', 'sn4_z5psf7rhb27yre26t5ymd2h5jtpx', 'sn4_bevh49hwo3vcbauly4h2x8alr19w',
  'sn4_s9excsmz8y21t40kkf7mbjrldldu', 'sn4_9mrqorcbe9yzqhjjuzsk9o5g0ycd', 'sn4_7x71cpjkpklq1cdy4frq1raeubpg',
  'sn4_wsn5h6gbc18y0e16wterm62e400a', 'sn4_wv8qli7q4j6icnju93hoktauj00r', 'sn4_23fqv8691pzht30g1hym8v4o0q02',
  'sn4_dmbx8zeqfmubd7msv6a0c9l89b9a', 'sn4_mdlw4tyrp9alp535mofrsgia5i72', 'sn4_id2v60ji156mrlo7qj96b95t859v',
  'sn4_dpw10d4vyeg7dsjsytorjok369r9', 'sn4_w65a2mj189ta3mo52tq36932flkl', 'sn4_npa29gg3joun18gabx1o490j5ooi',
  'sn4_1sem6mfuv4k90wi5a9o4pan3jm40', 'sn4_1noh5vdyy1q9sjy7yx2swzi0qnwc', 'sn4_pcf2ms0xx5e7dmab3rpyl1t9i4yi',
  'sn4_dok4a6613e8unhk03qvrnx6mgjjo', 'sn4_20dxhrpnncm2ywri2f58uk3mmvbl', 'sn4_hggw9odx77ggvu3tgn480f8nelht',
  'sn4_z4aj56mhkxru8us5x28reawvcqt5', 'sn4_54p0plih1999pk93x8hyczq37wmo', 'sn4_zihys8efn3wdmlgw6vehuodvimdx',
  'sn4_7psfkifd8pc54p21xg7klooywexy', 'sn4_4yus8sn48w4f396ml98auzkzqn3p', 'sn4_h1f8mmt26r9ioe1patvrfqs9tzi5',
  'sn4_6w1dlx4k2p43bzg5wsrhzloi3aht', 'sn4_rjko1d2tc09hdalo5y5az7xd8j3v', 'sn4_few6be9h8mh6hmk0geoyafuon9un',
  'sn4_iwovy25b3tppp2y06qjqxcyyh41c', 'sn4_lcjo1384ig659dxvnekjptnyj59s', 'sn4_7nj3y3euzoaowpqcsiyxwyefsuiz',
  'sn4_3f2ijyhfti9nvpx31rc7ms4pyir5', 'sn4_ueml5ake4bx0off8zw21yyj07li7', 'sn4_gktxco2nnkagi80t4dr66ox0gxym',
  'sn4_kt0q3s2ahvcl4qml7elnfe9705sc', 'sn4_36ygqd0i7bxc1r8fthqa2sp3rhxh', 'sn4_lpx8xnuvyvvheiyyppcailmdeb27',
  'sn4_t2crae1tcwyvtpw7nozax15fqkb0', 'sn4_oc4iws9fm4y7j0ero6ioojpjskb6', 'sn4_14tsiudj4nsjniqypbjbawhcadtc',
  'sn4_2lswnd9aq9k3fg3nlz6vwz0fi1u8', 'sn4_quubuk45hhn6zey3d1exjbexgtqr', 'sn4_hgsl46376pv0s2lwe3qcj3k4uabx',
  'sn4_9acdkffeflovo0rr0reyjsqdyk3p', 'sn4_fyrrhi1lyrebj629xwgonm5q7nj8', 'sn4_gwpp699smiuboqujj6tzw0f4uaim',
  'sn4_ossen4doelyhak85b6bldn7nd6kn', 'sn4_nfwqf8emmvsncjuqy2fcok4gcn6e', 'sn4_y2w34m788z29aov53lrvngnloi5v',

  // 第三批密钥 (101-150)
  'sn4_ei36z0omwpr7y9blor6ssseg5eot', 'sn4_2yb87u28sm58w86qctsct640mavy', 'sn4_d7d1gxzzgr6e5e8yi33udj42qfe9',
  'sn4_anxex7u61giguc4yu1il9gm81f71', 'sn4_nap0lz8d316zw26vpdw9ua8c8ux6', 'sn4_cza44i07x0d59dnhqepmxkq4yx0d',
  'sn4_nr1b7rtnoc2nn7mges8pz880l6kg', 'sn4_dp076ab371zyunt3vtkcdrcid3z8', 'sn4_ae482gzk3fagndf2181tpozo2x5w',
  'sn4_x0nhx15zwyx9ralb1ruwfqauosqb', 'sn4_mq273kxeyepi1ntwz2orcq8pref3', 'sn4_ap9sj8rqusug173tnmi0c5if95s9',
  'sn4_znhtxcxqntljdhfxppxj1frjfl0r', 'sn4_n331773jkdfeaw3r38k31ht40thf', 'sn4_3g0iuuzxds643nrpdv6qbr7wk2oa',
  'sn4_f7i3fcfpuftq95ago3ekzlsaqqt3', 'sn4_e9to3esqrtv0zozl9xr5umm3ax7p', 'sn4_2d5eoqmme51qdzbzrkk3dk2y4kly',
  'sn4_m3oecprxi0kwqsc9erm5pxqky7ce', 'sn4_mugyiaafdpe9u1o5gtgxdq3tpr57', 'sn4_w92pyqdos0oklwbyl62i17ro4q8e',
  'sn4_hjvkfo0m1ee7q2uxpsq19cnjs382', 'sn4_d40reqhupfbr636dlfxen9cscgs1', 'sn4_ri7vyfnq7i0d3z7lekwnesj3zfkv',
  'sn4_gmgctdp9o95euca30phmfm0bbsze', 'sn4_p4i3v3vtr9agvzq6y7thdthxe9ie', 'sn4_4gzpkgz6fwucw7w4gcy3xrnnn8h4',
  'sn4_h71wweoxv0oay0suhsnvx8fm8voq', 'sn4_zoisstzh4e8fdesfenby66gkys77', 'sn4_o92yib6qvo6f7o1w65w9m1ikwldx',
  'sn4_ueaqf05yo6o4ld142ttpdi6ewdwy', 'sn4_1f7f1b7qgqjupn4ywef45hjes7ds', 'sn4_olvct7xwboph3xfik7gy58ku5xr2',
  'sn4_kjj5xdf6ba1okoeswvjy8yhbrbwv', 'sn4_p1dg4k8ulxchxm4d61zk6v3qlef5', 'sn4_1oam0ripfb2oovubmvog4dg30flr',
  'sn4_5alyb4797m753b93nwaw3gq4tcv9', 'sn4_d2bsgubifvqg2jp5zqo4d1891t6g', 'sn4_rwh70lem7fm3y1mgor57zg7bcqrs',
  'sn4_l3g0ve3fs0e9fdihyuger0hooe2p', 'sn4_helk4jvwrrgdbr19j74h4mevrid2', 'sn4_v3ohg2if3upiifyrof8uw4uropq4',
  'sn4_gn3f4wrhpmowdksorq2rx1p6gyev', 'sn4_dr6tso2zi6jdh9u8ifhbktgq7xvg', 'sn4_37b9g536dyzrf18m1uixprlk30kc',
  'sn4_vygnip98b9evfkwuzks4l9e5ql05', 'sn4_o7bx0wi6fjppglm4jjor9iuvwjt8', 'sn4_5ry265eqp6o83tysp0u2lcovn8jw',
  'sn4_ki7imwy3svnzoxpha2opvhjr36ld', 'sn4_aba1vcc795sv5wpmz5il88m7zle5', 'sn4_smee6xnf5xg0oa32it120fl2egkm',
  'sn4_fbohcfz10f46fz8dmp1y8k6pk5rw', 'sn4_jdis9ohmmavt3oi8d35dig5vqfis', 'sn4_brvpud6slp1ynnesb4gfq2q3cg2m'
]

// 密钥池统计信息
export const KEY_POOL_STATS = {
  totalKeys: TARGON_API_KEYS.length,
  keyFormat: 'sn4_*',
  provider: 'Targon',
  lastUpdated: '2025-01-02',
  description: '高质量 Targon API 密钥池，支持自动轮换和负载均衡'
}

// 密钥池配置
export const KEY_POOL_CONFIG = {
  rotationInterval: 5 * 60 * 1000, // 5分钟轮换一次
  maxErrorCount: 3, // 最大错误次数
  healthCheckInterval: 10 * 60 * 1000, // 10分钟健康检查一次
  enableLoadBalancing: true, // 启用负载均衡
  enableAutoRecovery: true // 启用自动恢复
}

export default TARGON_API_KEYS
