# 故障排除指南

## 初始化失败问题解决方案

### 🔍 问题诊断

如果应用初始化失败，请按以下步骤进行诊断：

1. **打开浏览器开发者工具**
   - 按 F12 或右键选择"检查"
   - 查看 Console 标签页中的错误信息

2. **访问健康检查页面**
   - 访问 `http://localhost:3001/health`
   - 查看系统健康状态

3. **访问测试页面**
   - 访问 `http://localhost:3001/test`
   - 运行各项功能测试

### 🛠️ 常见问题及解决方案

#### 1. 依赖缺失错误
**错误信息**: `Cannot resolve module 'lodash-es'` 或类似依赖错误

**解决方案**:
```bash
# 重新安装依赖
npm install

# 或者清除缓存后重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 2. 本地存储权限问题
**错误信息**: `localStorage is not available` 或存储相关错误

**解决方案**:
- 检查浏览器是否启用了本地存储
- 确保不在无痕模式下运行
- 清除浏览器缓存和存储数据

#### 3. 网络连接问题
**错误信息**: 网络请求失败或超时

**解决方案**:
- 检查网络连接
- 确认API服务器是否正常运行
- 检查防火墙设置

#### 4. 浏览器兼容性问题
**错误信息**: 语法错误或功能不支持

**解决方案**:
- 升级到最新版本的现代浏览器
- 推荐使用 Chrome 90+、Firefox 88+、Safari 14+、Edge 90+

#### 5. Store初始化失败
**错误信息**: Store相关错误

**解决方案**:
```javascript
// 手动重置Store状态
localStorage.clear()
sessionStorage.clear()
// 然后刷新页面
```

### 🔧 手动初始化步骤

如果自动初始化失败，可以尝试手动初始化：

1. **清除所有缓存**:
```javascript
// 在浏览器控制台执行
localStorage.clear()
sessionStorage.clear()
location.reload()
```

2. **重置应用状态**:
```javascript
// 在浏览器控制台执行
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'

const appStore = useAppStore()
const userStore = useUserStore()

appStore.$reset()
userStore.$reset()
```

### 📊 性能优化建议

1. **减少初始化时间**:
   - 启用浏览器缓存
   - 使用CDN加速
   - 压缩静态资源

2. **内存优化**:
   - 定期清理无用数据
   - 避免内存泄漏
   - 合理使用缓存

### 🚨 紧急恢复方案

如果应用完全无法启动：

1. **完全重置**:
```bash
# 停止开发服务器
Ctrl + C

# 清除所有缓存和依赖
rm -rf node_modules
rm -rf .vite
rm package-lock.json

# 重新安装
npm install

# 重新启动
npm run dev
```

2. **浏览器重置**:
   - 清除所有浏览器数据
   - 禁用所有浏览器扩展
   - 使用无痕模式测试

### 📞 获取帮助

如果以上方案都无法解决问题：

1. **收集错误信息**:
   - 浏览器控制台错误
   - 网络请求失败信息
   - 系统环境信息

2. **导出诊断报告**:
   - 访问 `/health` 页面
   - 点击"导出报告"按钮
   - 将报告文件发送给技术支持

3. **联系技术支持**:
   - 提供详细的错误描述
   - 附上诊断报告
   - 说明复现步骤

### 🔍 调试技巧

1. **启用详细日志**:
```javascript
// 在控制台设置调试模式
localStorage.setItem('debug', 'true')
location.reload()
```

2. **监控性能**:
```javascript
// 查看初始化性能
performance.getEntriesByType('measure')
```

3. **检查内存使用**:
```javascript
// 查看内存使用情况
console.log(performance.memory)
```

### 📝 预防措施

1. **定期更新依赖**
2. **监控应用性能**
3. **备份重要数据**
4. **测试不同浏览器环境**
5. **保持代码质量**

---

**注意**: 如果问题持续存在，请及时联系开发团队获取支持。
