<template>
  <div
    class="conversation-item"
    :class="{ active: isActive }"
    :data-title="conversation.title"
    :data-initial="conversation.title.charAt(0).toUpperCase()"
    @click="$emit('click')"
  >
    <div class="conversation-avatar">
      <div class="avatar-bg">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    
    <div class="conversation-content" v-if="!collapsed">
      <div class="conversation-title">{{ conversation.title }}</div>
      <div class="conversation-meta">
        <span class="conversation-time">{{ formatTime(conversation.updatedAt) }}</span>
        <span class="message-count">{{ getMessageCount(conversation.id) }}条消息</span>
      </div>
    </div>
    
    <div class="conversation-actions" v-if="!collapsed">
      <button 
        @click.stop="$emit('command', { action: 'rename', id: conversation.id })" 
        class="action-btn"
        title="重命名"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <button 
        @click.stop="$emit('command', { action: 'delete', id: conversation.id })" 
        class="action-btn delete"
        title="删除"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
          <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14zM10 11v6M14 11v6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- 收起状态下的悬浮提示 -->
    <div v-if="collapsed" class="conversation-tooltip">
      <div class="tooltip-content">
        <div class="tooltip-title">{{ conversation.title }}</div>
        <div class="tooltip-time">{{ formatTime(conversation.updatedAt) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { formatRelativeTime } from '@/utils/common'

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  },
  collapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['click', 'command'])

const formatTime = (timestamp) => {
  return formatRelativeTime(timestamp)
}

// 这里应该从store获取消息数量，暂时返回模拟数据
const getMessageCount = (conversationId) => {
  return Math.floor(Math.random() * 20) + 1
}
</script>

<style lang="scss" scoped>
.conversation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 1px solid transparent;

  &:hover {
    background: rgba(241, 245, 249, 0.8);
    border-color: rgba(226, 232, 240, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .conversation-actions {
      opacity: 1;
    }
  }

  &.active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);

    .conversation-avatar .avatar-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
  }

  .conversation-avatar {
    .avatar-bg {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(241, 245, 249, 0.8);
      border: 1px solid rgba(226, 232, 240, 0.8);
      border-radius: 10px;
      color: #64748b;
      flex-shrink: 0;
      transition: all 0.3s ease;
    }
  }

  .conversation-content {
    flex: 1;
    min-width: 0;

    .conversation-title {
      font-size: 0.9rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0.5rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .conversation-meta {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 0.75rem;

      .conversation-time {
        color: #64748b;
      }

      .message-count {
        color: #667eea;
        padding: 0.125rem 0.5rem;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 12px;
        font-weight: 500;
      }
    }
  }

  .conversation-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.3s ease;

    .action-btn {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(148, 163, 184, 0.1);
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: rgba(148, 163, 184, 0.8);

      &:hover {
        background: rgba(148, 163, 184, 0.2);
        color: white;
      }

      &.delete:hover {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border-color: rgba(239, 68, 68, 0.3);
      }
    }
  }

  // 收起状态样式
  &.collapsed {
    width: 48px;
    height: 48px;
    margin: 0.5rem auto;
    border-radius: 12px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .conversation-avatar {
      .avatar-bg {
        width: 48px;
        height: 48px;
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(148, 163, 184, 0.8);
        transition: all 0.3s ease;
      }
    }

    &.active {
      .conversation-avatar .avatar-bg {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-color: transparent;
        color: white;
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
      }
    }

    &:hover:not(.active) {
      .conversation-avatar .avatar-bg {
        background: rgba(148, 163, 184, 0.2);
        border-color: rgba(148, 163, 184, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .conversation-content,
    .conversation-actions {
      display: none;
    }

    // 悬浮提示
    .conversation-tooltip {
      position: absolute;
      left: 60px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(15, 23, 42, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 8px;
      padding: 0.75rem;
      min-width: 200px;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      z-index: 1000;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);

      .tooltip-content {
        .tooltip-title {
          color: white;
          font-size: 0.9rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        .tooltip-time {
          color: rgba(148, 163, 184, 0.8);
          font-size: 0.75rem;
        }
      }

      &::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
        border: 6px solid transparent;
        border-right-color: rgba(15, 23, 42, 0.95);
      }
    }

    &:hover .conversation-tooltip {
      opacity: 1;
      visibility: visible;
    }
  }
}
</style>
