<template>
  <div class="legal-page">
    <!-- 返回按钮 -->
    <div class="back-button-container">
      <button @click="goBack" class="back-button">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>返回</span>
      </button>
    </div>

    <div class="container">
      <!-- 页面头部 -->
      <div class="legal-header">
        <div class="header-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="page-title">隐私政策</h1>
        <p class="page-subtitle">保护您的隐私是我们的承诺</p>
        <div class="update-info">
          <span class="update-label">最后更新</span>
          <span class="update-date">2025年1月20日</span>
        </div>
      </div>

      <!-- 目录导航 -->
      <div class="table-of-contents">
        <h3 class="toc-title">目录</h3>
        <div class="toc-list">
          <a href="#section-1" class="toc-item" @click="scrollToSection('section-1')">1. 隐私政策概述</a>
          <a href="#section-2" class="toc-item" @click="scrollToSection('section-2')">2. 信息收集</a>
          <a href="#section-3" class="toc-item" @click="scrollToSection('section-3')">3. 信息使用</a>
          <a href="#section-4" class="toc-item" @click="scrollToSection('section-4')">4. 信息共享</a>
          <a href="#section-5" class="toc-item" @click="scrollToSection('section-5')">5. 信息存储与安全</a>
          <a href="#section-6" class="toc-item" @click="scrollToSection('section-6')">6. 用户权利</a>
          <a href="#section-7" class="toc-item" @click="scrollToSection('section-7')">7. Cookie和追踪技术</a>
          <a href="#section-8" class="toc-item" @click="scrollToSection('section-8')">8. 未成年人保护</a>
          <a href="#section-9" class="toc-item" @click="scrollToSection('section-9')">9. 国际数据传输</a>
          <a href="#section-10" class="toc-item" @click="scrollToSection('section-10')">10. 政策更新</a>
          <a href="#section-11" class="toc-item" @click="scrollToSection('section-11')">11. 联系我们</a>
        </div>
      </div>

      <div class="legal-content">
        <section id="section-1" class="legal-section">
          <div class="section-header">
            <h2>1. 隐私政策概述</h2>
            <div class="section-icon">🛡️</div>
          </div>
          <div class="highlight-box privacy-intro">
            <h4>您的隐私，我们的承诺</h4>
            <p>我们深知隐私对您的重要性，致力于为您提供透明、安全的数据保护。</p>
          </div>
          <p>AI创作助手平台（以下简称"我们"）非常重视用户的隐私保护。本隐私政策详细说明了我们如何收集、使用、存储和保护您的个人信息。</p>
          <p>请您仔细阅读本隐私政策，以全面了解我们对您个人信息的处理方式和您享有的权利。</p>

          <div class="privacy-principles">
            <h4>我们的隐私原则</h4>
            <div class="principles-grid">
              <div class="principle-item">
                <div class="principle-icon">🔒</div>
                <span>最小化收集</span>
              </div>
              <div class="principle-item">
                <div class="principle-icon">🎯</div>
                <span>目的明确</span>
              </div>
              <div class="principle-item">
                <div class="principle-icon">⏰</div>
                <span>有限保存</span>
              </div>
              <div class="principle-item">
                <div class="principle-icon">🔐</div>
                <span>安全保护</span>
              </div>
            </div>
          </div>
        </section>

        <section id="section-2" class="legal-section">
          <div class="section-header">
            <h2>2. 信息收集</h2>
            <div class="section-icon">📊</div>
          </div>

          <div class="info-collection">
            <h3>2.1 我们收集的信息类型</h3>
            <div class="info-types-grid">
              <div class="info-type-card">
                <div class="info-icon">👤</div>
                <h4>账户信息</h4>
                <ul>
                  <li>用户名</li>
                  <li>邮箱地址</li>
                  <li>密码（加密存储）</li>
                </ul>
              </div>
              <div class="info-type-card">
                <div class="info-icon">🎨</div>
                <h4>个人资料</h4>
                <ul>
                  <li>头像</li>
                  <li>昵称</li>
                  <li>个人简介</li>
                </ul>
              </div>
              <div class="info-type-card">
                <div class="info-icon">📈</div>
                <h4>使用数据</h4>
                <ul>
                  <li>登录时间</li>
                  <li>使用功能</li>
                  <li>创作内容</li>
                </ul>
              </div>
              <div class="info-type-card">
                <div class="info-icon">💻</div>
                <h4>设备信息</h4>
                <ul>
                  <li>IP地址</li>
                  <li>浏览器类型</li>
                  <li>操作系统</li>
                </ul>
              </div>
              <div class="info-type-card">
                <div class="info-icon">🤖</div>
                <h4>交互数据</h4>
                <ul>
                  <li>AI对话记录</li>
                  <li>生成的图像</li>
                  <li>使用偏好</li>
                </ul>
              </div>
            </div>

            <h3>2.2 信息收集方式</h3>
            <div class="collection-methods">
              <div class="method-item">
                <div class="method-icon">✋</div>
                <div class="method-content">
                  <h4>主动提供</h4>
                  <p>您在注册、设置个人资料时主动提供的信息</p>
                </div>
              </div>
              <div class="method-item">
                <div class="method-icon">🔄</div>
                <div class="method-content">
                  <h4>自动收集</h4>
                  <p>您使用服务时自动收集的技术数据和使用统计</p>
                </div>
              </div>
              <div class="method-item">
                <div class="method-icon">🔗</div>
                <div class="method-content">
                  <h4>第三方服务</h4>
                  <p>通过社交登录等第三方服务获得的公开信息</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section class="legal-section">
          <h2>3. 信息使用</h2>
          <p>我们使用收集的信息用于以下目的：</p>
          <ul>
            <li>提供和改进我们的服务</li>
            <li>个性化用户体验</li>
            <li>处理用户请求和客户支持</li>
            <li>发送服务通知和更新</li>
            <li>防止欺诈和滥用</li>
            <li>遵守法律法规要求</li>
            <li>进行数据分析和服务优化</li>
          </ul>
        </section>

        <section class="legal-section">
          <h2>4. 信息共享</h2>
          <p>我们承诺不会出售、出租或以其他方式向第三方披露您的个人信息，除非：</p>
          <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求</li>
            <li>保护我们的合法权益</li>
            <li>与可信的服务提供商合作（在严格的保密协议下）</li>
            <li>紧急情况下保护用户或公众安全</li>
          </ul>
        </section>

        <section class="legal-section">
          <h2>5. 信息存储与安全</h2>
          <h3>5.1 数据存储</h3>
          <ul>
            <li>数据存储在安全的云服务器上</li>
            <li>采用行业标准的加密技术</li>
            <li>定期备份以防数据丢失</li>
            <li>限制访问权限，仅授权人员可访问</li>
          </ul>

          <h3>5.2 安全措施</h3>
          <ul>
            <li>SSL/TLS加密传输</li>
            <li>密码加密存储</li>
            <li>定期安全审计</li>
            <li>入侵检测系统</li>
            <li>员工安全培训</li>
          </ul>
        </section>

        <section class="legal-section">
          <h2>6. 用户权利</h2>
          <p>您对自己的个人信息享有以下权利：</p>
          <ul>
            <li><strong>访问权：</strong>查看我们收集的您的个人信息</li>
            <li><strong>更正权：</strong>更新或修正不准确的信息</li>
            <li><strong>删除权：</strong>要求删除您的个人信息</li>
            <li><strong>限制权：</strong>限制我们处理您的信息</li>
            <li><strong>携带权：</strong>获取您的数据副本</li>
            <li><strong>反对权：</strong>反对某些数据处理活动</li>
          </ul>
        </section>

        <section class="legal-section">
          <h2>7. Cookie和追踪技术</h2>
          <p>我们使用Cookie和类似技术来：</p>
          <ul>
            <li>记住您的登录状态</li>
            <li>保存您的偏好设置</li>
            <li>分析网站使用情况</li>
            <li>提供个性化内容</li>
          </ul>
          <p>您可以通过浏览器设置管理Cookie，但这可能影响某些功能的使用。</p>
        </section>

        <section class="legal-section">
          <h2>8. 未成年人保护</h2>
          <p>我们的服务主要面向成年用户。如果您未满18岁，请在监护人同意下使用我们的服务。</p>
          <p>我们不会故意收集未满13岁儿童的个人信息。如发现此类情况，我们将立即删除相关信息。</p>
        </section>

        <section class="legal-section">
          <h2>9. 国际数据传输</h2>
          <p>您的信息可能会被传输到您所在国家/地区以外的地方进行处理。我们会确保这些传输符合适用的数据保护法律。</p>
        </section>

        <section class="legal-section">
          <h2>10. 政策更新</h2>
          <p>我们可能会不时更新本隐私政策。重大变更时，我们会通过邮件或平台通知您。</p>
          <p>继续使用我们的服务即表示您接受更新后的隐私政策。</p>
        </section>

        <section class="legal-section">
          <h2>11. 联系我们</h2>
          <p>如您对本隐私政策有任何疑问或需要行使您的权利，请联系我们：</p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>客服热线：400-123-4567</li>
            <li>地址：北京市朝阳区xxx大厦xxx室</li>
            <li>在线客服：平台内客服系统</li>
          </ul>
        </section>
      </div>

      <div class="legal-footer">
        <div class="action-buttons">
          <button @click="goBack" class="btn-secondary">返回</button>
          <button @click="acceptAndContinue" class="btn-primary">我已阅读并同意</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'

const router = useRouter()
const route = useRoute()

const goBack = () => {
  if (route.query.from) {
    router.push(route.query.from)
  } else {
    router.go(-1)
  }
}

const acceptAndContinue = () => {
  ElMessage.success('感谢您阅读隐私政策')
  goBack()
}

const scrollToSection = async (sectionId) => {
  await nextTick()
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.legal-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.back-button-container {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 100;

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50px;
    color: $text-color-primary;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      background: white;
      transform: translateX(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    svg {
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: translateX(-2px);
    }
  }
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 6rem $spacing-lg 4rem;

  @media (max-width: 768px) {
    padding: 5rem $spacing-md 3rem;
  }
}

.legal-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .header-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 20px;
    margin-bottom: 1.5rem;
    color: white;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
  }

  .page-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: $text-color-primary;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    font-size: 1.2rem;
    color: $text-color-secondary;
    margin-bottom: 2rem;
  }

  .update-info {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 50px;
    font-size: 0.9rem;

    .update-label {
      color: $text-color-secondary;
    }

    .update-date {
      color: #059669;
      font-weight: 600;
    }
  }
}

.table-of-contents {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);

  .toc-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: $text-color-primary;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: '🔒';
      font-size: 1.2rem;
    }
  }

  .toc-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .toc-item {
    display: block;
    padding: 0.75rem 1rem;
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.1);
    border-radius: 12px;
    color: $text-color-primary;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: rgba(16, 185, 129, 0.1);
      border-color: rgba(16, 185, 129, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
    }
  }
}

.legal-content {
  line-height: 1.8;
  color: $text-color-primary;

  .legal-section {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    scroll-margin-top: 100px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 2px solid rgba(16, 185, 129, 0.1);

      h2 {
        font-size: 1.8rem;
        font-weight: 700;
        color: $text-color-primary;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .section-icon {
        font-size: 1.5rem;
        padding: 0.5rem;
        background: rgba(16, 185, 129, 0.1);
        border-radius: 12px;
      }
    }

    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: $text-color-primary;
      margin: 2rem 0 1rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: $text-color-primary;
      margin: 1rem 0 0.5rem 0;
    }

    p {
      margin-bottom: 1rem;
      text-align: justify;
      line-height: 1.7;
    }

    ul {
      margin: 1rem 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        position: relative;

        &::marker {
          color: #059669;
        }

        strong {
          color: $text-color-primary;
          font-weight: 600;
        }
      }
    }
  }
}

// 隐私政策特有组件样式
.highlight-box {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;

  &.privacy-intro {
    text-align: center;

    h4 {
      color: #059669;
      margin-bottom: 0.5rem;
    }
  }
}

.privacy-principles {
  margin: 2rem 0;

  h4 {
    color: #059669;
    margin-bottom: 1rem;
    text-align: center;
  }

  .principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;

    .principle-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem;
      background: rgba(16, 185, 129, 0.05);
      border-radius: 12px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(16, 185, 129, 0.1);
        transform: translateY(-2px);
      }

      .principle-icon {
        font-size: 1.5rem;
      }

      span {
        font-size: 0.9rem;
        font-weight: 600;
        color: $text-color-primary;
      }
    }
  }
}

.info-collection {
  .info-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;

    .info-type-card {
      background: rgba(16, 185, 129, 0.05);
      border: 1px solid rgba(16, 185, 129, 0.1);
      border-radius: 12px;
      padding: 1.5rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(16, 185, 129, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
      }

      .info-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        text-align: center;
        display: block;
      }

      h4 {
        color: #059669;
        margin-bottom: 1rem;
        text-align: center;
      }

      ul {
        margin: 0;
        padding-left: 1rem;

        li {
          font-size: 0.9rem;
          color: $text-color-secondary;
          margin-bottom: 0.25rem;
        }
      }
    }
  }

  .collection-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;

    .method-item {
      display: flex;
      gap: 1rem;
      padding: 1.5rem;
      background: rgba(16, 185, 129, 0.05);
      border: 1px solid rgba(16, 185, 129, 0.1);
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(16, 185, 129, 0.1);
        transform: translateX(4px);
      }

      .method-icon {
        font-size: 1.5rem;
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .method-content {
        flex: 1;

        h4 {
          margin: 0 0 0.25rem 0;
          font-size: 1rem;
          color: $text-color-primary;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          color: $text-color-secondary;
          line-height: 1.5;
        }
      }
    }
  }
}

.legal-footer {
  margin-top: $spacing-xl;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color-light;

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: $spacing-md;

    @media (max-width: 480px) {
      flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
      padding: 12px 32px;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      font-size: 1rem;
    }

    .btn-secondary {
      background: rgba(0, 0, 0, 0.05);
      color: $text-color-secondary;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .btn-primary {
      background: $gradient-primary;
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

@media (max-width: 768px) {
  .legal-header .page-title {
    font-size: 2rem;
  }

  .legal-content .legal-section h2 {
    font-size: 1.3rem;
  }
}
</style>
