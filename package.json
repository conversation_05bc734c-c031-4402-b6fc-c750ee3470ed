{"name": "ai-creative-platform", "version": "1.0.0", "description": "AI创作助手平台 - 集AI聊天、AI绘画、内容创作和社交分享于一体的综合性创作平台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "NODE_ENV=production vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.5.2", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "lodash-es": "^4.17.21", "marked": "^11.1.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "sass": "^1.69.7", "tailwindcss": "^3.4.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1"}, "keywords": ["vue3", "vite", "ai", "creative", "platform", "chat", "drawing"], "author": "AI Creative Team", "license": "MIT"}