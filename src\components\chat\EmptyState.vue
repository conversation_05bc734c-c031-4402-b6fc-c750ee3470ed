<template>
  <div class="empty-state">
    <div class="empty-content">
      <div class="welcome-section">
        <h2>欢迎使用 AI 助手</h2>
        <p>我可以帮助您解答问题、创作内容、分析数据等。请选择下方的建议问题开始对话，或直接输入您的问题。</p>
      </div>

      <div class="suggested-questions">
        <button
          v-for="(question, index) in suggestedQuestions"
          :key="question"
          @click="$emit('send-message', question)"
          class="suggestion-btn"
        >
          {{ question }}
        </button>
      </div>

      <!-- 功能介绍 -->
      <div class="features-intro">
        <div class="feature-item">
          <div class="feature-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="feature-text">
            <h4>智能对话</h4>
            <p>支持多轮对话，理解上下文</p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="feature-text">
            <h4>文档处理</h4>
            <p>支持上传文档进行分析</p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" stroke-width="2"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="feature-text">
            <h4>语音交互</h4>
            <p>支持语音输入和语音回复</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  suggestedQuestions: {
    type: Array,
    default: () => [
      '帮我写一篇关于人工智能的文章',
      '解释一下量子计算的原理',
      '推荐几本值得阅读的书籍',
      '如何提高工作效率？'
    ]
  }
})

defineEmits(['send-message'])
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
  padding: 2rem;

  .empty-content {
    max-width: 700px;
    text-align: center;

    .welcome-section {
      margin-bottom: 3rem;

      h2 {
        font-size: 2.25rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0 0 1.5rem 0;
        letter-spacing: -0.025em;
      }

      p {
        font-size: 1.125rem;
        color: #64748b;
        margin: 0;
        line-height: 1.7;
        max-width: 600px;
        margin: 0 auto;
      }
    }

    .suggested-questions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 1.25rem;
      max-width: 800px;
      margin: 0 auto;

      .suggestion-btn {
        padding: 1.25rem 1.5rem;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-align: left;
        font-size: 0.95rem;
        color: #1f2937;
        line-height: 1.6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
          transition: left 0.5s;
        }

        &:hover {
          background: #f8fafc;
          border-color: #667eea;
          transform: translateY(-3px);
          box-shadow: 0 12px 32px rgba(102, 126, 234, 0.15);
          color: #667eea;

          &::before {
            left: 100%;
          }
        }
      }
    }

  }
}
</style>
