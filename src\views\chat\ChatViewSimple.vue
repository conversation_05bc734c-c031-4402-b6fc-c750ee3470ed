<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 主布局 -->
    <div class="flex h-screen">
      <!-- 侧边栏 -->
      <aside class="bg-white border-r border-gray-200 shadow-lg w-80">
        <!-- 侧边栏头部 -->
        <div class="p-4 border-b border-gray-100">
          <h1 class="text-xl font-bold text-gray-900">AI 智能助手</h1>
        </div>

        <!-- 新建对话按钮 -->
        <div class="p-4">
          <button
            @click="createNewConversation"
            class="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2"/>
            </svg>
            <span>新建对话</span>
          </button>
        </div>

        <!-- AI模型选择器 -->
        <div class="px-4 pb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">AI模型</label>
          <select v-model="selectedModel" class="w-full p-3 border border-gray-300 rounded-lg">
            <option value="gemini-2.5-flash">Gemini 2.5 Flash (推荐)</option>
            <option value="gemini-2.5-flash-lite">Gemini 2.5 Flash Lite (极速)</option>
            <option value="gemini-2.5-pro">Gemini 2.5 Pro (最强)</option>
            <option value="openai">OpenAI GPT-4o Mini</option>
            <option value="mistral">Mistral Small 3</option>
            <option value="llama">Llama 3.3 70B</option>
            <option value="qwen-coder">Qwen 2.5 Coder (编程)</option>
          </select>
        </div>

        <!-- 对话列表 -->
        <div class="flex-1 overflow-y-auto px-4">
          <div class="text-sm font-medium text-gray-500 mb-3">对话历史</div>
          <div class="space-y-2">
            <div
              v-for="conversation in conversations"
              :key="conversation.id"
              class="group relative"
            >
              <button
                @click="switchConversation(conversation.id)"
                class="w-full text-left p-3 rounded-lg transition-all"
                :class="currentConversationId === conversation.id 
                  ? 'bg-blue-50 border border-blue-200 text-blue-900' 
                  : 'hover:bg-gray-50 border border-transparent'"
              >
                <div class="font-medium truncate">{{ conversation.title }}</div>
                <div class="text-sm text-gray-500 truncate">{{ conversation.lastMessage }}</div>
              </button>
            </div>
          </div>
        </div>
      </aside>

      <!-- 主聊天区域 -->
      <main class="flex-1 flex flex-col">
        <!-- 聊天头部 -->
        <header class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <span class="text-white font-semibold">AI</span>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900">智能助手</h2>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>在线</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- 消息列表 -->
        <div class="flex-1 overflow-y-auto p-4" ref="messagesContainer">
          <!-- 欢迎消息 -->
          <div v-if="messages.length === 0" class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" class="text-white">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">欢迎使用 AI助手</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">我是您的智能助手，可以帮您解答问题、分析数据、创作内容。请随时开始对话！</p>
          </div>

          <!-- 消息列表 -->
          <div class="space-y-6">
            <div
              v-for="message in messages"
              :key="message.id"
              class="flex"
              :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
            >
              <div class="flex items-start space-x-3 max-w-4xl">
                <!-- 头像 -->
                <div
                  v-if="message.role === 'assistant'"
                  class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-white text-sm font-semibold">AI</span>
                </div>

                <!-- 消息内容 -->
                <div
                  class="px-4 py-3 rounded-2xl max-w-3xl"
                  :class="message.role === 'user' 
                    ? 'bg-blue-500 text-white ml-auto' 
                    : 'bg-white border border-gray-200 text-gray-900'"
                >
                  <div class="whitespace-pre-wrap">{{ message.content }}</div>
                </div>

                <!-- 用户头像 -->
                <div
                  v-if="message.role === 'user'"
                  class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-gray-600 text-sm font-semibold">U</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="flex justify-start">
            <div class="flex items-start space-x-3 max-w-4xl">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-semibold">AI</span>
              </div>
              <div class="bg-white border border-gray-200 rounded-2xl px-4 py-3">
                <div class="flex items-center space-x-2">
                  <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                  </div>
                  <span class="text-sm text-gray-500">AI正在思考...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <footer class="bg-white border-t border-gray-200 p-4">
          <div class="flex items-end space-x-3">
            <!-- 输入框 -->
            <div class="flex-1 relative">
              <textarea
                v-model="inputMessage"
                @keydown.enter.exact.prevent="handleSend"
                @keydown.enter.shift.exact="inputMessage += '\n'"
                placeholder="输入您的问题... (Enter发送，Shift+Enter换行)"
                rows="1"
                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                style="min-height: 44px; max-height: 120px"
              ></textarea>
            </div>

            <!-- 发送按钮 -->
            <button
              @click="handleSend"
              :disabled="!inputMessage.trim() || isLoading"
              class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" class="animate-spin">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
                <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" fill="currentColor"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </footer>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { generateChatCompletion } from '@/services/chatService.js'

// 响应式数据
const selectedModel = ref('gemini-2.5-flash')
const currentConversationId = ref(1)
const inputMessage = ref('')
const isLoading = ref(false)
const messagesContainer = ref(null)

// 对话数据
const conversations = ref([
  {
    id: 1,
    title: '新对话',
    lastMessage: '开始新的对话...',
    messages: []
  }
])

// 当前对话
const currentConversation = computed(() => {
  return conversations.value.find(c => c.id === currentConversationId.value)
})

// 当前消息列表
const messages = computed(() => {
  return currentConversation.value?.messages || []
})

// 方法
const createNewConversation = () => {
  const newConversation = {
    id: Date.now(),
    title: '新对话',
    lastMessage: '开始新的对话...',
    messages: []
  }
  conversations.value.unshift(newConversation)
  currentConversationId.value = newConversation.id
  ElMessage.success('已创建新对话')
}

const switchConversation = (id) => {
  currentConversationId.value = id
}

const handleSend = () => {
  if (!inputMessage.value.trim() || isLoading.value) return
  sendMessage(inputMessage.value)
  inputMessage.value = ''
}

const sendMessage = async (content) => {
  // 添加用户消息
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: content,
    timestamp: new Date()
  }
  
  if (currentConversation.value) {
    currentConversation.value.messages.push(userMessage)
    
    // 更新对话标题和最后消息
    if (currentConversation.value.title === '新对话') {
      currentConversation.value.title = content.slice(0, 30) + (content.length > 30 ? '...' : '')
    }
    currentConversation.value.lastMessage = content.slice(0, 50) + (content.length > 50 ? '...' : '')
  }

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始加载
  isLoading.value = true

  try {
    // 准备聊天历史
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))

    // 调用AI API
    const result = await generateChatCompletion(chatMessages, {
      model: selectedModel.value || 'openai',
      temperature: 0.7,
      maxTokens: 2000
    })

    if (result.success) {
      const aiMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: result.content,
        timestamp: new Date(),
        model: selectedModel.value
      }

      if (currentConversation.value) {
        currentConversation.value.messages.push(aiMessage)
        currentConversation.value.lastMessage = aiMessage.content.slice(0, 50) + (aiMessage.content.length > 50 ? '...' : '')
      }

      ElMessage.success('回复成功')
    } else {
      throw new Error(result.error || '生成回复失败')
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error(`发送失败: ${error.message || '请重试'}`)

    // 添加错误消息
    const errorMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: '抱歉，我现在无法回复您的消息。请稍后重试。',
      timestamp: new Date(),
      model: selectedModel.value
    }

    if (currentConversation.value) {
      currentConversation.value.messages.push(errorMessage)
    }
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()
  }
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 生命周期
onMounted(() => {
  console.log('ChatViewSimple 组件已挂载')
})
</script>

<style lang="scss" scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本域自适应高度 */
textarea {
  field-sizing: content;
}
</style>
