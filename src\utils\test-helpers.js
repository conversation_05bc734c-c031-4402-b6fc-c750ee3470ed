// 测试辅助函数
export const testHelpers = {
  // 模拟延迟
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 生成随机ID
  generateId: () => Math.random().toString(36).substr(2, 9),
  
  // 格式化日期
  formatDate: (date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  },
  
  // 验证邮箱格式
  validateEmail: (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },
  
  // 截断文本
  truncateText: (text, maxLength = 100) => {
    if (text.length <= maxLength) return text
    return text.substr(0, maxLength) + '...'
  }
}

export default testHelpers
