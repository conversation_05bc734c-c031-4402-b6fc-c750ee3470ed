<template>
  <div class="basic-test">
    <div class="container">
      <h1>基础测试页面</h1>
      <p>这是一个最基本的测试页面，用于验证应用是否正常运行。</p>
      
      <div class="test-section">
        <h2>页面状态</h2>
        <div class="status-grid">
          <div class="status-item success">
            <div class="status-icon">✅</div>
            <div class="status-text">页面加载成功</div>
          </div>
          <div class="status-item info">
            <div class="status-icon">⏰</div>
            <div class="status-text">{{ currentTime }}</div>
          </div>
          <div class="status-item primary">
            <div class="status-icon">🔢</div>
            <div class="status-text">计数: {{ count }}</div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>交互测试</h2>
        <div class="button-group">
          <button @click="increment" class="test-button primary">
            增加计数
          </button>
          <button @click="reset" class="test-button secondary">
            重置计数
          </button>
          <button @click="showMessage" class="test-button success">
            显示消息
          </button>
        </div>
      </div>

      <div class="test-section">
        <h2>环境信息</h2>
        <div class="info-list">
          <div class="info-item">
            <span class="label">开发模式:</span>
            <span class="value">{{ isDev ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前路由:</span>
            <span class="value">{{ currentRoute }}</span>
          </div>
          <div class="info-item">
            <span class="label">浏览器:</span>
            <span class="value">{{ userAgent }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 响应式数据
const count = ref(0)
const currentTime = ref('')

// 计算属性
const isDev = computed(() => import.meta.env.DEV)
const currentRoute = computed(() => route.path)
const userAgent = computed(() => navigator.userAgent.split(' ')[0])

// 方法
const increment = () => {
  count.value++
}

const reset = () => {
  count.value = 0
}

const showMessage = () => {
  ElMessage.success('测试消息显示成功！')
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString()
}

// 生命周期
let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  console.log('BasicTest 组件已挂载')
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  console.log('BasicTest 组件已卸载')
})
</script>

<style scoped>
.basic-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

h2 {
  color: #34495e;
  margin-bottom: 1rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
}

.status-item.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-item.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-item.primary {
  background: #cce5ff;
  color: #004085;
  border: 1px solid #b3d7ff;
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.test-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.test-button.primary {
  background: #3498db;
  color: white;
}

.test-button.secondary {
  background: #95a5a6;
  color: white;
}

.test-button.success {
  background: #27ae60;
  color: white;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.label {
  font-weight: 600;
  color: #495057;
}

.value {
  color: #6c757d;
  font-family: monospace;
}

@media (max-width: 768px) {
  .basic-test {
    padding: 1rem;
  }
  
  .container {
    padding: 1rem;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .info-item {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
