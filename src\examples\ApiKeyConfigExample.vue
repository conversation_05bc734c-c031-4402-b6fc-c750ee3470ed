<template>
  <div class="api-key-example">
    <h2>API密钥配置集成示例</h2>
    
    <!-- 示例1: 基础集成 -->
    <el-card class="example-card">
      <template #header>
        <h3>示例1: 基础集成</h3>
      </template>
      
      <p>直接使用ApiKeyConfig组件：</p>
      
      <div class="code-example">
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;ApiKeyConfig @provider-configured="onProviderConfigured" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import ApiKeyConfig from '@/components/ApiKeyConfig.vue'

const onProviderConfigured = (provider) => {
  console.log('提供商配置完成:', provider)
}
&lt;/script&gt;</code></pre>
      </div>
      
      <!-- 实际组件 -->
      <div class="example-demo">
        <ApiKeyConfig @provider-configured="onProviderConfigured" />
      </div>
    </el-card>

    <!-- 示例2: 带状态管理 -->
    <el-card class="example-card">
      <template #header>
        <h3>示例2: 带状态管理</h3>
      </template>
      
      <p>集成状态管理和本地存储：</p>
      
      <div class="status-display">
        <el-alert
          :title="`当前配置状态: ${configStatus}`"
          :type="configStatus === '已配置' ? 'success' : 'warning'"
          :closable="false"
          show-icon
        />
        
        <div class="provider-list" v-if="configuredProviders.length > 0">
          <h4>已配置的提供商:</h4>
          <el-tag 
            v-for="provider in configuredProviders" 
            :key="provider.id"
            :type="provider.status === 'active' ? 'success' : 'info'"
            class="provider-tag"
          >
            {{ provider.name }}
          </el-tag>
        </div>
      </div>
      
      <div class="actions">
        <el-button @click="loadConfig">加载配置</el-button>
        <el-button @click="saveConfig" type="primary">保存配置</el-button>
        <el-button @click="clearConfig" type="danger">清除配置</el-button>
      </div>
    </el-card>

    <!-- 示例3: 聊天集成 -->
    <el-card class="example-card">
      <template #header>
        <h3>示例3: 聊天功能集成</h3>
      </template>
      
      <p>在聊天功能中检查和使用API配置：</p>
      
      <div class="chat-example">
        <div class="chat-status">
          <el-alert
            v-if="!hasValidApiKey"
            title="请先配置API密钥"
            description="需要配置至少一个AI服务提供商才能使用聊天功能"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <el-button 
                type="primary" 
                size="small" 
                @click="showApiConfig = true"
              >
                立即配置
              </el-button>
            </template>
          </el-alert>
          
          <el-alert
            v-else
            title="API配置正常"
            :description="`当前可用提供商: ${availableProviders.join(', ')}`"
            type="success"
            :closable="false"
            show-icon
          />
        </div>
        
        <div class="chat-input">
          <el-input
            v-model="chatMessage"
            placeholder="输入您的消息..."
            :disabled="!hasValidApiKey"
            @keyup.enter="sendMessage"
          >
            <template #append>
              <el-button 
                @click="sendMessage"
                :disabled="!hasValidApiKey || !chatMessage.trim()"
                type="primary"
              >
                发送
              </el-button>
            </template>
          </el-input>
        </div>
        
        <div class="chat-messages" v-if="chatMessages.length > 0">
          <div 
            v-for="(message, index) in chatMessages" 
            :key="index"
            class="chat-message"
            :class="message.type"
          >
            <strong>{{ message.sender }}:</strong> {{ message.content }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="showApiConfig"
      title="API密钥配置"
      width="80%"
      :close-on-click-modal="false"
    >
      <ApiKeyConfig @provider-configured="onProviderConfigured" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ApiKeyConfig from '@/components/ApiKeyConfig.vue'

// 响应式数据
const showApiConfig = ref(false)
const configuredProviders = ref([])
const chatMessage = ref('')
const chatMessages = ref([])

// 计算属性
const configStatus = computed(() => {
  return configuredProviders.value.length > 0 ? '已配置' : '未配置'
})

const hasValidApiKey = computed(() => {
  return configuredProviders.value.some(p => p.configured && p.status === 'active')
})

const availableProviders = computed(() => {
  return configuredProviders.value
    .filter(p => p.configured && p.status === 'active')
    .map(p => p.name)
})

// 方法
const onProviderConfigured = (provider) => {
  console.log('提供商配置完成:', provider)
  
  // 更新配置状态
  const existingIndex = configuredProviders.value.findIndex(p => p.id === provider.id)
  if (existingIndex >= 0) {
    configuredProviders.value[existingIndex] = provider
  } else {
    configuredProviders.value.push(provider)
  }
  
  // 保存到本地存储
  saveConfig()
  
  ElMessage.success(`${provider.name} 配置成功！`)
  showApiConfig.value = false
}

const loadConfig = () => {
  try {
    const saved = localStorage.getItem('api-providers-config')
    if (saved) {
      configuredProviders.value = JSON.parse(saved)
      ElMessage.success('配置加载成功')
    } else {
      ElMessage.info('没有找到保存的配置')
    }
  } catch (error) {
    ElMessage.error('配置加载失败: ' + error.message)
  }
}

const saveConfig = () => {
  try {
    localStorage.setItem('api-providers-config', JSON.stringify(configuredProviders.value))
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败: ' + error.message)
  }
}

const clearConfig = () => {
  configuredProviders.value = []
  localStorage.removeItem('api-providers-config')
  ElMessage.success('配置已清除')
}

const sendMessage = async () => {
  if (!chatMessage.value.trim()) return
  
  const userMessage = {
    type: 'user',
    sender: '用户',
    content: chatMessage.value
  }
  
  chatMessages.value.push(userMessage)
  
  // 模拟AI回复
  const aiMessage = {
    type: 'ai',
    sender: 'AI助手',
    content: '这是一个模拟回复。在实际应用中，这里会调用配置的API服务。'
  }
  
  // 延迟显示AI回复
  setTimeout(() => {
    chatMessages.value.push(aiMessage)
  }, 1000)
  
  chatMessage.value = ''
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.api-key-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.api-key-example h2 {
  text-align: center;
  color: #303133;
  margin-bottom: 30px;
}

.example-card {
  margin-bottom: 30px;
  border-radius: 8px;
}

.example-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.example-card h3 {
  margin: 0;
  color: #303133;
}

.code-example {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
}

.code-example pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.code-example code {
  color: #525252;
}

.example-demo {
  margin-top: 20px;
  padding: 20px;
  border: 2px dashed #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.status-display {
  margin: 20px 0;
}

.provider-list {
  margin-top: 16px;
}

.provider-list h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.provider-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.actions .el-button {
  margin: 0 8px;
}

.chat-example {
  margin-top: 20px;
}

.chat-status {
  margin-bottom: 16px;
}

.chat-input {
  margin-bottom: 16px;
}

.chat-messages {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.chat-message {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  line-height: 1.5;
}

.chat-message.user {
  background: #e3f2fd;
  text-align: right;
}

.chat-message.ai {
  background: #f3e5f5;
  text-align: left;
}

.chat-message strong {
  color: #303133;
}

@media (max-width: 768px) {
  .api-key-example {
    padding: 10px;
  }
  
  .actions .el-button {
    width: 100%;
    margin: 4px 0;
  }
  
  .code-example {
    font-size: 12px;
  }
}
</style>
