// 聊天相关API
import { request } from '@/utils/request'

/**
 * 发送消息
 */
export function sendMessageApi(data) {
  return request.post('/chat/send', data)
}

/**
 * 获取聊天历史
 */
export function getChatHistoryApi(params) {
  return request.get('/chat/history', params)
}

/**
 * 获取对话列表
 */
export function getConversationsApi(params) {
  return request.get('/chat/conversations', params)
}

/**
 * 创建新对话
 */
export function createConversationApi(data) {
  return request.post('/chat/conversations', data)
}

/**
 * 更新对话信息
 */
export function updateConversationApi(id, data) {
  return request.put(`/chat/conversations/${id}`, data)
}

/**
 * 删除对话
 */
export function deleteChatApi(conversationId) {
  return request.delete(`/chat/conversations/${conversationId}`)
}

/**
 * 获取对话详情
 */
export function getConversationDetailApi(id) {
  return request.get(`/chat/conversations/${id}`)
}

/**
 * 获取消息列表
 */
export function getMessagesApi(conversationId, params) {
  return request.get(`/chat/conversations/${conversationId}/messages`, params)
}

/**
 * 删除消息
 */
export function deleteMessageApi(messageId) {
  return request.delete(`/chat/messages/${messageId}`)
}

/**
 * 重新生成回复
 */
export function regenerateResponseApi(messageId) {
  return request.post(`/chat/messages/${messageId}/regenerate`)
}

/**
 * 点赞/取消点赞消息
 */
export function likeMessageApi(messageId) {
  return request.post(`/chat/messages/${messageId}/like`)
}

/**
 * 点踩/取消点踩消息
 */
export function dislikeMessageApi(messageId) {
  return request.post(`/chat/messages/${messageId}/dislike`)
}

/**
 * 收藏/取消收藏消息
 */
export function favoriteMessageApi(messageId) {
  return request.post(`/chat/messages/${messageId}/favorite`)
}

/**
 * 分享对话
 */
export function shareConversationApi(conversationId, data) {
  return request.post(`/chat/conversations/${conversationId}/share`, data)
}

/**
 * 导出对话
 */
export function exportConversationApi(conversationId, format = 'json') {
  return request.download(`/chat/conversations/${conversationId}/export`, { format })
}

/**
 * 获取AI模型列表
 */
export function getAiModelsApi() {
  return request.get('/chat/models')
}

/**
 * 获取模型详情
 */
export function getModelDetailApi(modelId) {
  return request.get(`/chat/models/${modelId}`)
}

/**
 * 获取预设提示词
 */
export function getPromptsApi(params) {
  return request.get('/chat/prompts', params)
}

/**
 * 创建自定义提示词
 */
export function createPromptApi(data) {
  return request.post('/chat/prompts', data)
}

/**
 * 更新提示词
 */
export function updatePromptApi(id, data) {
  return request.put(`/chat/prompts/${id}`, data)
}

/**
 * 删除提示词
 */
export function deletePromptApi(id) {
  return request.delete(`/chat/prompts/${id}`)
}

/**
 * 获取聊天统计
 */
export function getChatStatsApi() {
  return request.get('/chat/stats')
}

/**
 * 获取使用配额
 */
export function getUsageQuotaApi() {
  return request.get('/chat/quota')
}

/**
 * 流式聊天（SSE）
 */
export function streamChatApi(data, onMessage, onError, onComplete) {
  const eventSource = new EventSource(`${request.defaults.baseURL}/chat/stream`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    },
  })

  // 发送消息数据
  eventSource.addEventListener('open', () => {
    eventSource.postMessage(JSON.stringify(data))
  })

  // 接收消息
  eventSource.addEventListener('message', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage(data)
    } catch (error) {
      onError && onError(error)
    }
  })

  // 处理错误
  eventSource.addEventListener('error', (error) => {
    onError && onError(error)
    eventSource.close()
  })

  // 连接完成
  eventSource.addEventListener('complete', () => {
    onComplete && onComplete()
    eventSource.close()
  })

  return eventSource
}

/**
 * WebSocket聊天连接
 */
export function createChatWebSocket(onMessage, onError, onOpen, onClose) {
  const wsUrl = `${request.defaults.baseURL.replace('http', 'ws')}/chat/ws`
  const token = localStorage.getItem('token')
  
  const ws = new WebSocket(`${wsUrl}?token=${token}`)

  ws.onopen = (event) => {
    console.log('WebSocket连接已建立')
    onOpen && onOpen(event)
  }

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage(data)
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
      onError && onError(error)
    }
  }

  ws.onerror = (error) => {
    console.error('WebSocket错误:', error)
    onError && onError(error)
  }

  ws.onclose = (event) => {
    console.log('WebSocket连接已关闭')
    onClose && onClose(event)
  }

  return ws
}

/**
 * 上传聊天文件
 */
export function uploadChatFileApi(formData) {
  return request.upload('/chat/upload', formData)
}

/**
 * 语音转文字
 */
export function speechToTextApi(formData) {
  return request.upload('/chat/speech-to-text', formData)
}

/**
 * 文字转语音
 */
export function textToSpeechApi(data) {
  return request.post('/chat/text-to-speech', data, {
    responseType: 'blob'
  })
}
