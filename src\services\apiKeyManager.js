// API密钥管理器 - 支持轮询和健康检查

class ApiKeyManager {
  constructor() {
    this.keys = this.loadApiKeys()
    this.currentKeyIndex = 0
    this.keyStats = new Map() // 记录每个密钥的统计信息
    this.healthCheckInterval = null
    this.isHealthCheckRunning = false

    // 初始化密钥统计
    this.keys.forEach((key, index) => {
      this.keyStats.set(index, {
        key: key,
        isActive: true,
        failureCount: 0,
        lastUsed: null,
        lastSuccess: null,
        lastFailure: null,
        totalRequests: 0,
        successfulRequests: 0,
        responseTime: 0
      })
    })

    // 只在生产环境或明确启用时启动健康检查
    if (import.meta.env.PROD || import.meta.env.VITE_OPENROUTER_KEY_HEALTH_CHECK_ENABLED === 'true') {
      this.startHealthCheck()
    }
  }
  
  // 从环境变量加载API密钥
  loadApiKeys() {
    const keys = []
    
    // 加载所有配置的密钥
    for (let i = 1; i <= 10; i++) {
      const key = import.meta.env[`VITE_OPENROUTER_API_KEY_${i}`]
      if (key && key.trim()) {
        keys.push(key.trim())
      }
    }
    
    if (keys.length === 0) {
      console.warn('未找到有效的OpenRouter API密钥')
      // 如果没有环境变量，使用默认密钥（开发环境）
      keys.push('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')
    }
    
    console.log(`加载了 ${keys.length} 个API密钥`)
    return keys
  }
  
  // 获取当前可用的API密钥
  getCurrentKey() {
    if (this.keys.length === 0) {
      throw new Error('没有可用的API密钥')
    }
    
    // 如果当前密钥不可用，尝试找到下一个可用的
    let attempts = 0
    while (attempts < this.keys.length) {
      const keyIndex = (this.currentKeyIndex + attempts) % this.keys.length
      const keyStats = this.keyStats.get(keyIndex)
      
      if (keyStats && keyStats.isActive) {
        this.currentKeyIndex = keyIndex
        keyStats.lastUsed = new Date()
        keyStats.totalRequests++
        return keyStats.key
      }
      
      attempts++
    }
    
    // 如果所有密钥都不可用，重置所有密钥状态并使用第一个
    console.warn('所有API密钥都不可用，重置状态')
    this.resetAllKeys()
    return this.keys[0]
  }
  
  // 轮换到下一个密钥
  rotateKey() {
    if (this.keys.length <= 1) return
    
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keys.length
    console.log(`轮换到密钥 #${this.currentKeyIndex + 1}`)
  }
  
  // 标记当前密钥为失败
  markCurrentKeyFailed(error = null) {
    const keyStats = this.keyStats.get(this.currentKeyIndex)
    if (keyStats) {
      keyStats.failureCount++
      keyStats.lastFailure = new Date()

      // 记录错误信息（如果提供）
      if (error) {
        console.warn(`密钥 #${this.currentKeyIndex + 1} 失败:`, error.message || error)
      }

      const threshold = parseInt(import.meta.env.VITE_OPENROUTER_KEY_FAILURE_THRESHOLD) || 3

      // 如果失败次数超过阈值，暂时禁用该密钥
      if (keyStats.failureCount >= threshold) {
        keyStats.isActive = false
        console.warn(`密钥 #${this.currentKeyIndex + 1} 失败次数过多，暂时禁用`)

        // 只在浏览器环境中显示消息
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.warning(`API密钥 #${this.currentKeyIndex + 1} 暂时不可用，已切换到备用密钥`)
        }

        // 轮换到下一个密钥
        this.rotateKey()
      }
    }
  }
  
  // 标记当前密钥为成功
  markCurrentKeySuccess(responseTime = 0) {
    const keyStats = this.keyStats.get(this.currentKeyIndex)
    if (keyStats) {
      keyStats.successfulRequests++
      keyStats.lastSuccess = new Date()
      keyStats.responseTime = responseTime
      
      // 重置失败计数
      if (keyStats.failureCount > 0) {
        keyStats.failureCount = Math.max(0, keyStats.failureCount - 1)
      }
    }
  }
  
  // 重置所有密钥状态
  resetAllKeys() {
    this.keyStats.forEach((stats) => {
      stats.isActive = true
      stats.failureCount = 0
    })
  }
  
  // 获取密钥统计信息
  getKeyStats() {
    const stats = []
    this.keyStats.forEach((keyStats, index) => {
      stats.push({
        index: index + 1,
        isActive: keyStats.isActive,
        isCurrent: index === this.currentKeyIndex,
        failureCount: keyStats.failureCount,
        totalRequests: keyStats.totalRequests,
        successfulRequests: keyStats.successfulRequests,
        successRate: keyStats.totalRequests > 0 
          ? ((keyStats.successfulRequests / keyStats.totalRequests) * 100).toFixed(1)
          : '0.0',
        lastUsed: keyStats.lastUsed,
        lastSuccess: keyStats.lastSuccess,
        lastFailure: keyStats.lastFailure,
        responseTime: keyStats.responseTime,
        keyPreview: keyStats.key.substring(0, 20) + '...'
      })
    })
    return stats
  }
  
  // 健康检查
  async performHealthCheck() {
    if (this.isHealthCheckRunning) return

    this.isHealthCheckRunning = true
    console.log('开始API密钥健康检查...')

    try {
      for (let i = 0; i < this.keys.length; i++) {
        const keyStats = this.keyStats.get(i)
        if (!keyStats) continue

        try {
          // 创建AbortController用于超时控制
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 10000)

          // 发送简单的健康检查请求
          const baseUrl = import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1'
          const response = await fetch(`${baseUrl}/models`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${keyStats.key}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': window.location.origin,
              'X-Title': 'AI Creative Platform'
            },
            signal: controller.signal
          })

          clearTimeout(timeoutId)

          if (response.ok) {
            // 密钥健康，重新激活
            if (!keyStats.isActive) {
              keyStats.isActive = true
              keyStats.failureCount = 0
              console.log(`密钥 #${i + 1} 健康检查通过，重新激活`)
            }
          } else {
            throw new Error(`HTTP ${response.status}`)
          }
        } catch (error) {
          console.warn(`密钥 #${i + 1} 健康检查失败:`, error.message)
          keyStats.failureCount++

          // 如果健康检查失败次数过多，禁用密钥
          const threshold = parseInt(import.meta.env.VITE_OPENROUTER_KEY_FAILURE_THRESHOLD) || 3
          if (keyStats.failureCount >= threshold) {
            keyStats.isActive = false
          }
        }

        // 避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    } catch (globalError) {
      console.error('健康检查过程中发生错误:', globalError)
    } finally {
      this.isHealthCheckRunning = false
    }
  }
  
  // 启动定期健康检查
  startHealthCheck() {
    const interval = parseInt(import.meta.env.VITE_OPENROUTER_KEY_HEALTH_CHECK_INTERVAL) || 300000 // 5分钟

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck()
    }, interval)

    // 延迟执行首次健康检查，避免启动时的网络请求
    setTimeout(() => {
      if (typeof window !== 'undefined' && window.navigator.onLine) {
        this.performHealthCheck()
      }
    }, 30000) // 30秒后执行
  }
  
  // 停止健康检查
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }
  
  // 测试指定密钥
  async testKey(index) {
    const keyStats = this.keyStats.get(index)
    if (!keyStats) {
      throw new Error(`密钥索引 ${index} 不存在`)
    }

    const startTime = Date.now()

    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

      // 发送真实的API测试请求
      const baseUrl = import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1'
      const response = await fetch(`${baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${keyStats.key}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Creative Platform'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324:free', // 使用免费模型进行测试
          messages: [
            {
              role: 'user',
              content: 'Hello! This is a test message. Please respond with "Test successful".'
            }
          ],
          max_tokens: 10,
          temperature: 0.1
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime

      if (response.ok) {
        const data = await response.json()

        // 更新成功统计
        keyStats.totalRequests++
        keyStats.successfulRequests++
        keyStats.lastSuccess = new Date()
        keyStats.responseTime = Math.round((keyStats.responseTime + responseTime) / 2)

        // 如果密钥之前被禁用，重新激活
        if (!keyStats.isActive) {
          keyStats.isActive = true
          keyStats.failureCount = 0
        }

        return {
          success: true,
          responseTime: responseTime,
          message: '测试成功',
          response: data.choices?.[0]?.message?.content || '测试响应'
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      const responseTime = Date.now() - startTime

      // 更新失败统计
      keyStats.totalRequests++
      keyStats.failureCount++
      keyStats.lastFailure = new Date()

      // 如果失败次数过多，禁用密钥
      const threshold = parseInt(import.meta.env.VITE_OPENROUTER_KEY_FAILURE_THRESHOLD) || 3
      if (keyStats.failureCount >= threshold) {
        keyStats.isActive = false
      }

      return {
        success: false,
        responseTime: responseTime,
        message: error.name === 'AbortError' ? '请求超时' : error.message,
        error: error.message
      }
    }
  }

  // 手动添加密钥
  addKey(apiKey) {
    if (!apiKey || !apiKey.trim()) {
      throw new Error('API密钥不能为空')
    }

    const key = apiKey.trim()
    if (this.keys.includes(key)) {
      throw new Error('该API密钥已存在')
    }

    this.keys.push(key)
    const newIndex = this.keys.length - 1

    this.keyStats.set(newIndex, {
      key: key,
      isActive: true,
      failureCount: 0,
      lastUsed: null,
      lastSuccess: null,
      lastFailure: null,
      totalRequests: 0,
      successfulRequests: 0,
      responseTime: 0
    })

    console.log(`添加新API密钥 #${newIndex + 1}`)
    return newIndex
  }
  
  // 移除密钥
  removeKey(index) {
    if (index < 0 || index >= this.keys.length) {
      throw new Error('无效的密钥索引')
    }
    
    if (this.keys.length <= 1) {
      throw new Error('至少需要保留一个API密钥')
    }
    
    this.keys.splice(index, 1)
    this.keyStats.delete(index)
    
    // 重新构建keyStats映射
    const newKeyStats = new Map()
    this.keys.forEach((key, newIndex) => {
      const oldStats = Array.from(this.keyStats.values()).find(stats => stats.key === key)
      if (oldStats) {
        newKeyStats.set(newIndex, oldStats)
      }
    })
    this.keyStats = newKeyStats
    
    // 调整当前密钥索引
    if (this.currentKeyIndex >= index) {
      this.currentKeyIndex = Math.max(0, this.currentKeyIndex - 1)
    }
    
    console.log(`移除API密钥 #${index + 1}`)
  }
  
  // 获取配置信息
  getConfig() {
    return {
      baseUrl: import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
      timeout: parseInt(import.meta.env.VITE_OPENROUTER_TIMEOUT) || 60000,
      maxRetries: parseInt(import.meta.env.VITE_OPENROUTER_MAX_RETRIES) || 3,
      retryDelay: parseInt(import.meta.env.VITE_OPENROUTER_RETRY_DELAY) || 1000,
      keyRotationEnabled: import.meta.env.VITE_OPENROUTER_KEY_ROTATION_ENABLED === 'true',
      healthCheckInterval: parseInt(import.meta.env.VITE_OPENROUTER_KEY_HEALTH_CHECK_INTERVAL) || 300000,
      failureThreshold: parseInt(import.meta.env.VITE_OPENROUTER_KEY_FAILURE_THRESHOLD) || 3,
      totalKeys: this.keys.length,
      activeKeys: Array.from(this.keyStats.values()).filter(stats => stats.isActive).length
    }
  }
}

// 延迟初始化的全局实例
let _apiKeyManager = null

export const getApiKeyManager = () => {
  if (!_apiKeyManager) {
    _apiKeyManager = new ApiKeyManager()
  }
  return _apiKeyManager
}

// 为了向后兼容，提供一个getter
export const apiKeyManager = {
  get instance() {
    return getApiKeyManager()
  },
  getCurrentKey() {
    return getApiKeyManager().getCurrentKey()
  },
  rotateKey() {
    return getApiKeyManager().rotateKey()
  },
  markCurrentKeyFailed(error) {
    return getApiKeyManager().markCurrentKeyFailed(error)
  },
  markCurrentKeySuccess(responseTime) {
    return getApiKeyManager().markCurrentKeySuccess(responseTime)
  },
  performHealthCheck() {
    return getApiKeyManager().performHealthCheck()
  },
  getKeyStats() {
    return getApiKeyManager().getKeyStats()
  },
  getConfig() {
    return getApiKeyManager().getConfig()
  },
  addKey(apiKey) {
    return getApiKeyManager().addKey(apiKey)
  },
  removeKey(index) {
    return getApiKeyManager().removeKey(index)
  },
  resetAllKeys() {
    return getApiKeyManager().resetAllKeys()
  },
  testKey(index) {
    return getApiKeyManager().testKey(index)
  }
}

// 导出类以便测试
export { ApiKeyManager }
