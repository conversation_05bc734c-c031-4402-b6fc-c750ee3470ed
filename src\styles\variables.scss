// 现代化主题色彩变量 - 高端商业风格

// 调试模式控制
$debug-mode: false !default;

$primary-color: #6366f1; // 现代紫色 - 科技感
$secondary-color: #8b5cf6; // 深紫色
$accent-color: #06b6d4; // 青色 - 活力
$success-color: #10b981; // 现代绿色
$warning-color: #f59e0b; // 现代橙色
$danger-color: #ef4444; // 现代红色
$info-color: #3b82f6; // 现代蓝色

// 高级渐变色彩系统
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
$gradient-premium: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
$gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
$gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

// 辅助色彩
$primary-light-1: #53a8ff;
$primary-light-2: #66b1ff;
$primary-light-3: #79bbff;
$primary-light-4: #8cc5ff;
$primary-light-5: #a0cfff;
$primary-light-6: #b3d8ff;
$primary-light-7: #c6e2ff;
$primary-light-8: #d9ecff;
$primary-light-9: #ecf5ff;

// 现代化文字颜色系统
$text-color-primary: #0f172a; // 深蓝灰 - 更好的可读性
$text-color-regular: #475569; // 中灰
$text-color-secondary: #64748b; // 浅灰
$text-color-muted: #94a3b8; // 更浅灰
$text-color-placeholder: #cbd5e1; // 占位符
$text-color-white: #ffffff;
$text-color-inverse: #ffffff; // 反色文字

// 现代化背景色系统
$bg-color: #ffffff;
$bg-color-page: #f8fafc; // 极浅灰背景
$bg-color-secondary: #f1f5f9; // 次要背景
$bg-color-tertiary: #e2e8f0; // 第三级背景
$bg-color-dark: #0f172a; // 深色背景
$bg-color-card: #ffffff; // 卡片背景
$bg-color-glass: rgba(255, 255, 255, 0.1); // 毛玻璃效果
$bg-color-overlay: rgba(0, 0, 0, 0.5); // 遮罩层

// 边框颜色
$border-color: #e2e8f0; // 主要边框色
$border-color-base: #DCDFE6;
$border-color-light: #f1f5f9; // 浅色边框
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;
$border-color-dark: #334155;

// 现代化阴影系统
$box-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$box-shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$box-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$box-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
$box-shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3); // 发光效果

// 圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 布局
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// AI创作平台特色色彩系统
$ai-gradient-start: #667eea;
$ai-gradient-end: #764ba2;
$ai-gradient-accent: #f093fb;

// 聊天界面色彩
$chat-bubble-user: $gradient-primary;
$chat-bubble-ai: #f8fafc;
$chat-bubble-system: #e2e8f0;
$chat-input-bg: #ffffff;
$chat-input-border: #e2e8f0;

// 绘画界面色彩
$drawing-canvas-bg: #fafafa;
$drawing-toolbar-bg: #ffffff;
$drawing-panel-bg: #f8fafc;

// 作品展示色彩
$gallery-card-bg: #ffffff;
$gallery-card-shadow: $box-shadow-md;
$gallery-card-hover-shadow: $box-shadow-lg;

// 付费功能色彩
$premium-gradient: $gradient-premium;
$premium-badge: #fbbf24;
$premium-border: #f59e0b;

// 状态色彩
$status-online: #10b981;
$status-offline: #6b7280;
$status-busy: #f59e0b;
$status-away: #8b5cf6;
