/**
 * 智谱 AI (GLM) 官方 API 服务
 * 支持 GLM-4.5 和 GLM-4.5-Air 模型
 */

import axios from 'axios'

class ZhipuApi {
  constructor() {
    this.baseURL = 'https://open.bigmodel.cn/api/paas/v4/'
    this.apiKey = '50ef236d65d94aa78151a759c26d6a72.HborYaoAhrEDKnkD'
    this.timeout = 60000
    this.models = [
      {
        id: 'glm-4.5-air',
        name: 'GLM-4.5-Air',
        description: '智谱 GLM-4.5-Air，轻量级高效模型',
        maxTokens: 8192,
        supportsFunctions: true,
        pricing: '官方 API'
      },
      {
        id: 'glm-4.5',
        name: 'GLM-4.5',
        description: '智谱 GLM-4.5，标准版本模型',
        maxTokens: 8192,
        supportsFunctions: true,
        pricing: '官方 API'
      }
    ]
  }

  /**
   * 设置 API Key
   * @param {string} apiKey - 智谱 API Key
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey
    console.log('✅ 智谱 API Key 已设置')
  }

  /**
   * 获取当前 API Key
   * @returns {string|null} 当前的 API Key
   */
  getApiKey() {
    return this.apiKey
  }

  /**
   * 生成 JWT Token
   * @returns {string} JWT Token
   */
  generateJwtToken() {
    if (!this.apiKey) {
      throw new Error('智谱 API Key 未设置')
    }

    // 智谱 API Key 格式: id.secret
    const [id, secret] = this.apiKey.split('.')
    if (!id || !secret) {
      throw new Error('智谱 API Key 格式错误，应为 id.secret 格式')
    }

    // 简单的 JWT Token 生成（用于智谱 API）
    const header = {
      alg: 'HS256',
      sign_type: 'SIGN'
    }

    const payload = {
      api_key: id,
      exp: Math.floor(Date.now() / 1000) + 3600, // 1小时过期
      timestamp: Math.floor(Date.now() / 1000)
    }

    // Base64 编码
    const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '')
    const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '')

    // 简化的签名（实际应用中需要使用 HMAC-SHA256）
    const signature = btoa(`${encodedHeader}.${encodedPayload}.${secret}`).replace(/=/g, '')

    return `${encodedHeader}.${encodedPayload}.${signature}`
  }

  /**
   * 创建 HTTP 客户端
   * @returns {Object} axios 实例
   */
  createClient() {
    if (!this.apiKey) {
      throw new Error('智谱 API Key 未设置')
    }

    try {
      const jwtToken = this.generateJwtToken()

      return axios.create({
        baseURL: this.baseURL,
        timeout: this.timeout,
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        }
      })
    } catch (error) {
      console.error('❌ 创建智谱 API 客户端失败:', error)
      // 如果 JWT 生成失败，尝试直接使用 API Key
      return axios.create({
        baseURL: this.baseURL,
        timeout: this.timeout,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      })
    }
  }

  /**
   * 获取可用模型列表
   * @returns {Promise<Object>} 模型列表响应
   */
  async getModels() {
    try {
      console.log('📋 获取智谱模型列表...')
      
      return {
        success: true,
        data: this.models,
        message: '获取模型列表成功'
      }
    } catch (error) {
      console.error('❌ 获取智谱模型列表失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 发送聊天请求
   * @param {Object} params - 请求参数
   * @param {string} params.model - 模型名称 (glm-4.5 或 glm-4.5-air)
   * @param {Array} params.messages - 消息数组
   * @param {number} params.temperature - 温度参数 (0-1)
   * @param {number} params.max_tokens - 最大token数
   * @param {boolean} params.stream - 是否流式输出
   * @returns {Promise<Object>} 响应结果
   */
  async chat(params) {
    try {
      if (!this.apiKey) {
        throw new Error('智谱 API Key 未设置')
      }

      console.log('💬 发送智谱聊天请求:', {
        model: params.model,
        messageCount: params.messages?.length,
        stream: params.stream
      })

      const client = this.createClient()

      const requestData = {
        model: params.model || 'glm-4.5-air',
        messages: params.messages,
        temperature: Math.min(Math.max(params.temperature || 0.7, 0), 1),
        max_tokens: params.max_tokens || 2048,
        stream: params.stream || false
      }

      const response = await client.post('chat/completions', requestData)

      console.log('✅ 智谱聊天请求成功')
      return {
        success: true,
        data: response.data
      }
    } catch (error) {
      console.error('❌ 智谱聊天请求失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * 流式聊天请求
   * @param {Object} params - 请求参数
   * @param {Function} onMessage - 消息回调函数
   * @param {Function} onError - 错误回调函数
   * @param {Function} onComplete - 完成回调函数
   */
  async streamChat(params, onMessage, onError, onComplete) {
    try {
      if (!this.apiKey) {
        throw new Error('智谱 API Key 未设置')
      }

      console.log('🌊 开始智谱流式聊天...')

      const client = this.createClient()

      const requestData = {
        model: params.model || 'glm-4.5-air',
        messages: params.messages,
        temperature: Math.min(Math.max(params.temperature || 0.7, 0), 1),
        max_tokens: params.max_tokens || 2048,
        stream: true
      }

      const response = await client.post('chat/completions', requestData, {
        responseType: 'stream'
      })

      let buffer = ''
      
      response.data.on('data', (chunk) => {
        buffer += chunk.toString()
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.trim() === '') continue
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data === '[DONE]') {
              onComplete && onComplete()
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                onMessage && onMessage(content)
              }
            } catch (parseError) {
              console.warn('解析流式数据失败:', parseError)
            }
          }
        }
      })

      response.data.on('end', () => {
        console.log('✅ 智谱流式聊天完成')
        onComplete && onComplete()
      })

      response.data.on('error', (error) => {
        console.error('❌ 智谱流式聊天错误:', error)
        onError && onError(error)
      })

    } catch (error) {
      console.error('❌ 智谱流式聊天失败:', error)
      onError && onError(error)
    }
  }

  /**
   * 测试 API 连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      console.log('🔍 测试智谱 API 连接...')

      const testResult = await this.chat({
        model: 'glm-4.5-air',
        messages: [
          { role: 'user', content: '你好，请简单介绍一下自己。' }
        ],
        max_tokens: 100
      })

      if (testResult.success) {
        console.log('✅ 智谱 API 连接测试成功')
        return {
          success: true,
          message: '智谱 API 连接正常',
          data: testResult.data
        }
      } else {
        return {
          success: false,
          message: '智谱 API 连接测试失败',
          error: testResult.error
        }
      }
    } catch (error) {
      console.error('❌ 智谱 API 连接测试失败:', error)
      return {
        success: false,
        message: '智谱 API 连接测试异常',
        error: error.message
      }
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          return 'API Key 无效或已过期'
        case 403:
          return '访问被拒绝，请检查 API Key 权限'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return '智谱服务器内部错误'
        case 502:
        case 503:
        case 504:
          return '智谱服务暂时不可用'
        default:
          return data?.error?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接'
    } else if (error.code === 'ENOTFOUND') {
      return '无法连接到智谱服务器'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 获取 API 状态信息
   * @returns {Object} API 状态
   */
  getStatus() {
    return {
      provider: 'Zhipu AI',
      baseURL: this.baseURL,
      hasApiKey: !!this.apiKey,
      models: this.models.length,
      timeout: this.timeout
    }
  }
}

// 创建单例实例
const zhipuApi = new ZhipuApi()

export default zhipuApi
