// 聊天界面移动端优化样式
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 移动端聊天布局优化
.chat-mobile-optimized {
  // 基础移动端样式
  @include respond-to(md) {
    .chat-container {
      height: 100vh;
      overflow: hidden;
    }

    .chat-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
      width: 85vw;
      max-width: 320px;
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
      box-shadow: 2px 0 15px rgba(0, 0, 0, 0.15);

      &.visible {
        transform: translateX(0);
      }
    }

    .chat-main {
      width: 100%;
      margin-left: 0;
    }

    .mobile-menu-btn {
      display: flex !important;
    }
  }

  // 小屏幕优化
  @include respond-to(sm) {
    .chat-sidebar {
      width: 90vw;
      max-width: 300px;
    }

    .chat-header {
      padding: 0.75rem 1rem;
      
      .header-content {
        gap: 0.5rem;
        
        .chat-title {
          font-size: 1rem;
          line-height: 1.2;
        }
        
        .model-badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
          white-space: nowrap;
        }
      }
    }

    .message-list {
      padding: 0.5rem;
    }

    .message-bubble {
      max-width: 92%;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
    }

    .chat-input {
      padding: 0.75rem;
    }
  }

  // 超小屏幕优化
  @include respond-to(xs) {
    .chat-sidebar {
      width: 95vw;
      max-width: 280px;
    }

    .mobile-menu-btn {
      width: 36px;
      height: 36px;
      padding: 0.375rem;
    }

    .chat-header {
      padding: 0.5rem 0.75rem;
      
      .header-content {
        gap: 0.375rem;
        
        .chat-title {
          font-size: 0.9rem;
        }
        
        .model-badge {
          font-size: 0.65rem;
          padding: 0.2rem 0.4rem;
        }
      }
    }

    .message-bubble {
      max-width: 95%;
      padding: 0.625rem 0.875rem;
      font-size: 0.8rem;
    }

    .chat-input {
      padding: 0.5rem;
    }
  }
}

// 移动端侧边栏遮罩
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;

  &.active {
    opacity: 1;
    visibility: visible;
  }

  @include respond-to(md) {
    display: block;
  }

  @media (min-width: 769px) {
    display: none;
  }
}

// 移动端输入框优化
.mobile-input-optimized {
  @include respond-to(md) {
    .chat-input-container {
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 100;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .input-field {
      font-size: 16px; // 防止iOS缩放
      min-height: 44px; // iOS最小触摸目标
    }

    .input-buttons {
      gap: 0.5rem;
      
      button {
        min-width: 44px;
        min-height: 44px;
      }
    }
  }

  // iOS Safari 适配
  @supports (-webkit-touch-callout: none) {
    .chat-input-container {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

// 移动端消息列表优化
.mobile-messages-optimized {
  @include respond-to(md) {
    .message-list {
      padding: 0.5rem;
      
      .message-item {
        margin-bottom: 1rem;
      }
      
      .message-avatar {
        width: 32px;
        height: 32px;
      }
      
      .message-content {
        .message-text {
          line-height: 1.5;
        }
        
        .message-actions {
          gap: 0.25rem;
          
          button {
            min-width: 32px;
            min-height: 32px;
            padding: 0.25rem;
          }
        }
      }
    }
  }

  @include respond-to(sm) {
    .message-list {
      padding: 0.375rem;
      
      .message-item {
        margin-bottom: 0.75rem;
      }
      
      .message-avatar {
        width: 28px;
        height: 28px;
      }
    }
  }
}

// 移动端工具栏优化
.mobile-toolbar-optimized {
  @include respond-to(md) {
    .chat-toolbar {
      padding: 0.5rem;
      gap: 0.5rem;
      flex-wrap: wrap;
      
      .toolbar-button {
        min-width: 44px;
        min-height: 44px;
        padding: 0.5rem;
        font-size: 0.875rem;
      }
    }
  }

  @include respond-to(sm) {
    .chat-toolbar {
      padding: 0.375rem;
      gap: 0.375rem;
      
      .toolbar-button {
        min-width: 40px;
        min-height: 40px;
        padding: 0.375rem;
        font-size: 0.8rem;
      }
    }
  }
}

// 移动端滚动优化
.mobile-scroll-optimized {
  @include respond-to(md) {
    .scrollable-area {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }
    
    .scroll-to-bottom {
      position: fixed;
      bottom: 100px;
      right: 1rem;
      z-index: 50;
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}

// 移动端动画优化
@media (prefers-reduced-motion: reduce) {
  .chat-mobile-optimized {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 横屏模式优化
@media (orientation: landscape) and (max-height: 500px) {
  .chat-mobile-optimized {
    .chat-header {
      height: 48px;
      padding: 0.5rem 1rem;
    }
    
    .chat-input-container {
      padding: 0.5rem;
    }
    
    .message-list {
      padding: 0.25rem 0.5rem;
    }
  }
}
