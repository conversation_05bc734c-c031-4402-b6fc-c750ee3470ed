/**
 * 登录状态管理工具
 * 确保登录状态在页面刷新、标签页切换等情况下正确持久化
 */

import { APP_CONFIG } from '@/config'
import { getToken, setToken, removeToken, isTokenExpired } from '@/utils/auth'

// 登录状态存储键
const LOGIN_STATE_KEY = 'ai_creative_login_state'
const LOGIN_TIMESTAMP_KEY = 'ai_creative_login_timestamp'
const LAST_ACTIVITY_KEY = 'ai_creative_last_activity'

/**
 * 保存登录状态
 */
export function saveLoginState(token, userInfo, options = {}) {
  try {
    console.log('💾 保存登录状态...')
    
    const loginState = {
      token,
      userInfo,
      timestamp: Date.now(),
      rememberMe: options.rememberMe || false,
      expiresAt: options.expiresAt || (Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天
      version: '1.0'
    }
    
    // 保存token
    setToken(token)
    
    // 保存用户信息
    localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(userInfo))
    
    // 保存登录状态
    localStorage.setItem(LOGIN_STATE_KEY, JSON.stringify(loginState))
    
    // 保存登录时间戳
    localStorage.setItem(LOGIN_TIMESTAMP_KEY, Date.now().toString())
    
    // 更新最后活动时间
    updateLastActivity()
    
    console.log('✅ 登录状态保存成功:', {
      username: userInfo.username,
      role: userInfo.role,
      rememberMe: options.rememberMe,
      tokenPreview: token.substring(0, 20) + '...'
    })
    
    return true
  } catch (error) {
    console.error('❌ 保存登录状态失败:', error)
    return false
  }
}

/**
 * 恢复登录状态
 */
export function restoreLoginState() {
  try {
    console.log('🔄 恢复登录状态...')
    
    // 获取保存的登录状态
    const savedStateStr = localStorage.getItem(LOGIN_STATE_KEY)
    const savedToken = getToken()
    const savedUserInfoStr = localStorage.getItem(APP_CONFIG.storage.userKey)
    
    if (!savedToken || !savedUserInfoStr) {
      console.log('ℹ️ 没有找到保存的登录信息')
      return null
    }
    
    // 解析保存的数据
    let savedState = null
    let savedUserInfo = null
    
    try {
      if (savedStateStr) {
        savedState = JSON.parse(savedStateStr)
      }
      savedUserInfo = JSON.parse(savedUserInfoStr)
    } catch (parseError) {
      console.warn('⚠️ 解析保存的登录数据失败:', parseError)
      clearLoginState()
      return null
    }
    
    // 检查token是否过期
    if (isTokenExpired(savedToken)) {
      console.log('⚠️ Token已过期，清除登录状态')
      clearLoginState()
      return null
    }
    
    // 检查登录状态是否过期（如果有保存的状态）
    if (savedState && savedState.expiresAt && Date.now() > savedState.expiresAt) {
      console.log('⚠️ 登录状态已过期，清除登录状态')
      clearLoginState()
      return null
    }
    
    // 更新最后活动时间
    updateLastActivity()
    
    console.log('✅ 登录状态恢复成功:', {
      username: savedUserInfo.username,
      role: savedUserInfo.role,
      loginTime: savedState?.timestamp ? new Date(savedState.timestamp).toLocaleString() : '未知'
    })
    
    return {
      token: savedToken,
      userInfo: savedUserInfo,
      loginState: savedState
    }
  } catch (error) {
    console.error('❌ 恢复登录状态失败:', error)
    clearLoginState()
    return null
  }
}

/**
 * 清除登录状态
 */
export function clearLoginState() {
  try {
    console.log('🧹 清除登录状态...')
    
    // 清除token
    removeToken()
    
    // 清除用户信息
    localStorage.removeItem(APP_CONFIG.storage.userKey)
    
    // 清除登录状态
    localStorage.removeItem(LOGIN_STATE_KEY)
    
    // 清除时间戳
    localStorage.removeItem(LOGIN_TIMESTAMP_KEY)
    localStorage.removeItem(LAST_ACTIVITY_KEY)
    
    // 清除其他相关数据
    localStorage.removeItem('login_failed_attempts')
    localStorage.removeItem('ai_creative_remember_me')
    
    console.log('✅ 登录状态清除完成')
    return true
  } catch (error) {
    console.error('❌ 清除登录状态失败:', error)
    return false
  }
}

/**
 * 检查登录状态是否有效
 */
export function isLoginStateValid() {
  try {
    const token = getToken()
    const userInfoStr = localStorage.getItem(APP_CONFIG.storage.userKey)

    if (!token || !userInfoStr) {
      console.log('ℹ️ 缺少token或用户信息')
      return false
    }

    // 检查token是否过期
    if (isTokenExpired(token)) {
      console.log('⚠️ Token已过期')
      return false
    }

    // 检查用户信息是否有效
    try {
      const userInfo = JSON.parse(userInfoStr)
      if (!userInfo.username || !userInfo.id) {
        console.log('⚠️ 用户信息不完整')
        return false
      }
    } catch (error) {
      console.log('⚠️ 用户信息解析失败:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('检查登录状态失败:', error)
    return false
  }
}

/**
 * 强制同步登录状态 - 确保所有存储的一致性
 */
export function syncLoginState() {
  try {
    console.log('🔄 开始同步登录状态...')

    const token = getToken()
    const userInfoStr = localStorage.getItem(APP_CONFIG.storage.userKey)
    const loginStateStr = localStorage.getItem(LOGIN_STATE_KEY)

    // 如果没有token，清除所有相关数据
    if (!token) {
      console.log('ℹ️ 没有token，清除所有登录相关数据')
      clearLoginState()
      return false
    }

    // 如果token过期，清除所有相关数据
    if (isTokenExpired(token)) {
      console.log('⚠️ Token已过期，清除所有登录相关数据')
      clearLoginState()
      return false
    }

    // 如果没有用户信息，清除token
    if (!userInfoStr) {
      console.log('⚠️ 没有用户信息，清除token')
      removeToken()
      clearLoginState()
      return false
    }

    // 验证用户信息格式
    try {
      const userInfo = JSON.parse(userInfoStr)
      if (!userInfo.username || !userInfo.id) {
        console.log('⚠️ 用户信息格式无效，清除登录状态')
        clearLoginState()
        return false
      }
    } catch (error) {
      console.log('⚠️ 用户信息解析失败，清除登录状态')
      clearLoginState()
      return false
    }

    console.log('✅ 登录状态同步完成，状态有效')
    return true
  } catch (error) {
    console.error('❌ 同步登录状态失败:', error)
    clearLoginState()
    return false
  }
}

/**
 * 更新最后活动时间
 */
export function updateLastActivity() {
  try {
    localStorage.setItem(LAST_ACTIVITY_KEY, Date.now().toString())
  } catch (error) {
    console.warn('更新最后活动时间失败:', error)
  }
}

/**
 * 获取登录持续时间
 */
export function getLoginDuration() {
  try {
    const loginTimestamp = localStorage.getItem(LOGIN_TIMESTAMP_KEY)
    if (!loginTimestamp) {
      return null
    }
    
    const loginTime = parseInt(loginTimestamp)
    const currentTime = Date.now()
    const duration = currentTime - loginTime
    
    return {
      milliseconds: duration,
      seconds: Math.floor(duration / 1000),
      minutes: Math.floor(duration / (1000 * 60)),
      hours: Math.floor(duration / (1000 * 60 * 60)),
      days: Math.floor(duration / (1000 * 60 * 60 * 24)),
      loginTime: new Date(loginTime),
      currentTime: new Date(currentTime)
    }
  } catch (error) {
    console.error('获取登录持续时间失败:', error)
    return null
  }
}

/**
 * 获取登录状态摘要
 */
export function getLoginStateSummary() {
  try {
    const token = getToken()
    const userInfoStr = localStorage.getItem(APP_CONFIG.storage.userKey)
    const loginStateStr = localStorage.getItem(LOGIN_STATE_KEY)
    const duration = getLoginDuration()
    
    let userInfo = null
    let loginState = null
    
    try {
      if (userInfoStr) userInfo = JSON.parse(userInfoStr)
      if (loginStateStr) loginState = JSON.parse(loginStateStr)
    } catch (error) {
      // 忽略解析错误
    }
    
    return {
      isLoggedIn: isLoginStateValid(),
      hasToken: !!token,
      hasUserInfo: !!userInfo,
      tokenExpired: token ? isTokenExpired(token) : null,
      username: userInfo?.username || null,
      role: userInfo?.role || null,
      loginDuration: duration,
      rememberMe: loginState?.rememberMe || false,
      version: loginState?.version || null,
      lastActivity: localStorage.getItem(LAST_ACTIVITY_KEY) ? 
        new Date(parseInt(localStorage.getItem(LAST_ACTIVITY_KEY))) : null
    }
  } catch (error) {
    console.error('获取登录状态摘要失败:', error)
    return {
      isLoggedIn: false,
      hasToken: false,
      hasUserInfo: false,
      error: error.message
    }
  }
}

/**
 * 监听存储变化（用于多标签页同步）
 */
export function setupStorageListener(callback) {
  const handleStorageChange = (event) => {
    if (event.key === APP_CONFIG.storage.tokenKey || 
        event.key === APP_CONFIG.storage.userKey ||
        event.key === LOGIN_STATE_KEY) {
      console.log('🔄 检测到登录状态变化，同步状态...')
      if (callback && typeof callback === 'function') {
        callback(event)
      }
    }
  }
  
  window.addEventListener('storage', handleStorageChange)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('storage', handleStorageChange)
  }
}

/**
 * 自动清理过期的登录状态
 */
export function cleanupExpiredLoginState() {
  try {
    if (!isLoginStateValid()) {
      console.log('🧹 检测到过期的登录状态，自动清理...')
      clearLoginState()
      return true
    }
    return false
  } catch (error) {
    console.error('清理过期登录状态失败:', error)
    return false
  }
}

/**
 * 页面加载时的登录状态恢复
 * 这个函数应该在应用启动时调用，确保登录状态的正确恢复
 */
export function initializeLoginStateOnPageLoad() {
  try {
    console.log('🔄 页面加载：开始初始化登录状态...')

    // 首先同步状态
    const isValid = syncLoginState()

    if (isValid) {
      // 状态有效，尝试恢复
      const restored = restoreLoginState()
      if (restored) {
        console.log('✅ 页面加载：登录状态恢复成功')
        return restored
      } else {
        console.log('⚠️ 页面加载：登录状态恢复失败')
        return null
      }
    } else {
      console.log('ℹ️ 页面加载：没有有效的登录状态')
      return null
    }
  } catch (error) {
    console.error('❌ 页面加载：初始化登录状态失败:', error)
    // 出错时清理状态
    clearLoginState()
    return null
  }
}

// 导出默认对象
export default {
  save: saveLoginState,
  restore: restoreLoginState,
  clear: clearLoginState,
  isValid: isLoginStateValid,
  updateActivity: updateLastActivity,
  getDuration: getLoginDuration,
  getSummary: getLoginStateSummary,
  setupListener: setupStorageListener,
  cleanup: cleanupExpiredLoginState
}
