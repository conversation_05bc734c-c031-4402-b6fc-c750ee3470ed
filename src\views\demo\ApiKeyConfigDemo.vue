<template>
  <div class="api-key-demo">
    <div class="demo-header">
      <h1>API密钥配置演示</h1>
      <p>这是一个类似Cherry Studio的API密钥配置界面演示</p>
    </div>

    <!-- 功能特性介绍 -->
    <el-row :gutter="20" class="features">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card">
          <div class="feature-icon">
            <el-icon color="#409EFF"><Key /></el-icon>
          </div>
          <h3>多提供商支持</h3>
          <p>支持OpenAI、Anthropic、OpenRouter等多种AI服务提供商</p>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card">
          <div class="feature-icon">
            <el-icon color="#67C23A"><Lock /></el-icon>
          </div>
          <h3>安全存储</h3>
          <p>API密钥仅存储在本地浏览器，确保数据安全</p>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card">
          <div class="feature-icon">
            <el-icon color="#E6A23C"><Connection /></el-icon>
          </div>
          <h3>连接测试</h3>
          <p>一键测试API连接状态，确保配置正确</p>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="feature-card">
          <div class="feature-icon">
            <el-icon color="#F56C6C"><Setting /></el-icon>
          </div>
          <h3>灵活配置</h3>
          <p>支持自定义API地址、模型选择、超时设置等</p>
        </el-card>
      </el-col>
    </el-row>

    <!-- Targon API 特色介绍 -->
    <el-card class="targon-highlight">
      <template #header>
        <h2><el-icon><Star /></el-icon> Targon API - 新增支持</h2>
      </template>

      <div class="targon-content">
        <div class="targon-info">
          <h3>🎉 现已支持 Targon API</h3>
          <p>Targon API 提供多种主流AI模型的统一接口，包括：</p>

          <el-row :gutter="16" class="model-grid">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="model-category">
                <h4>🧠 DeepSeek 系列</h4>
                <ul>
                  <li>DeepSeek-V3 (最新版)</li>
                  <li>DeepSeek-V3-0324</li>
                  <li>DeepSeek-R1 (推理专用)</li>
                  <li>DeepSeek-R1-0528</li>
                </ul>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <div class="model-category">
                <h4>🌙 Moonshot 系列</h4>
                <ul>
                  <li>Kimi-K2-Instruct</li>
                </ul>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <div class="model-category">
                <h4>🔥 Qwen 系列</h4>
                <ul>
                  <li>Qwen3-Coder-480B (代码专用)</li>
                  <li>Qwen3-235B-Instruct</li>
                </ul>
              </div>
            </el-col>
          </el-row>

          <div class="targon-features">
            <el-tag type="success" class="feature-tag">高性能</el-tag>
            <el-tag type="primary" class="feature-tag">多模型</el-tag>
            <el-tag type="warning" class="feature-tag">统一接口</el-tag>
            <el-tag type="info" class="feature-tag">稳定可靠</el-tag>
          </div>

          <div class="api-info">
            <p><strong>API 地址：</strong> <code>https://api.targon.com/v1</code></p>
            <p><strong>兼容性：</strong> OpenAI API 格式</p>
          </div>

          <div class="targon-actions">
            <el-button
              type="primary"
              size="large"
              @click="goToTargonDemo"
            >
              <el-icon><Tools /></el-icon>
              体验 Targon API
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="usage-instructions">
      <template #header>
        <h2><el-icon><Document /></el-icon> 使用说明</h2>
      </template>
      
      <div class="instructions-content">
        <el-steps :active="4" align-center>
          <el-step title="选择提供商" description="从预设列表中选择AI服务提供商" />
          <el-step title="输入密钥" description="填入您的API密钥和相关配置" />
          <el-step title="测试连接" description="验证配置是否能正常工作" />
          <el-step title="开始使用" description="在聊天和绘画功能中使用AI服务" />
        </el-steps>

        <div class="quick-start">
          <h3>快速开始</h3>
          <ol>
            <li>点击下方的"打开配置页面"按钮</li>
            <li>选择一个AI服务提供商（推荐OpenRouter免费版）</li>
            <li>输入您的API密钥</li>
            <li>点击"测试连接"确保配置正确</li>
            <li>保存配置并开始使用</li>
          </ol>
        </div>

        <div class="demo-actions">
          <el-button 
            type="primary" 
            size="large" 
            :icon="Key"
            @click="openConfigPage"
          >
            打开配置页面
          </el-button>
          
          <el-button 
            type="success" 
            size="large" 
            :icon="ChatDotRound"
            @click="goToChat"
          >
            前往聊天
          </el-button>
          
          <el-button 
            type="warning" 
            size="large" 
            :icon="Brush"
            @click="goToDrawing"
          >
            前往绘画
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 预设配置展示 -->
    <el-card class="preset-showcase">
      <template #header>
        <h2><el-icon><Star /></el-icon> 推荐配置</h2>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="24" :md="8">
          <div class="preset-item">
            <div class="preset-header">
              <div class="preset-logo">OR</div>
              <div class="preset-info">
                <h4>OpenRouter</h4>
                <el-tag type="success" size="small">推荐</el-tag>
              </div>
            </div>
            <p class="preset-desc">
              提供多种免费AI模型，无需付费即可体验GPT、Claude等模型
            </p>
            <div class="preset-features">
              <el-tag size="small">免费模型</el-tag>
              <el-tag size="small">多种选择</el-tag>
              <el-tag size="small">易于使用</el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :md="8">
          <div class="preset-item">
            <div class="preset-header">
              <div class="preset-logo">AI</div>
              <div class="preset-info">
                <h4>OpenAI</h4>
                <el-tag type="primary" size="small">官方</el-tag>
              </div>
            </div>
            <p class="preset-desc">
              OpenAI官方API服务，提供最新的GPT模型，稳定可靠
            </p>
            <div class="preset-features">
              <el-tag size="small">官方服务</el-tag>
              <el-tag size="small">最新模型</el-tag>
              <el-tag size="small">稳定可靠</el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :md="8">
          <div class="preset-item">
            <div class="preset-header">
              <div class="preset-logo">C</div>
              <div class="preset-info">
                <h4>Anthropic</h4>
                <el-tag type="info" size="small">专业</el-tag>
              </div>
            </div>
            <p class="preset-desc">
              Claude AI模型服务，擅长复杂对话和文本分析任务
            </p>
            <div class="preset-features">
              <el-tag size="small">Claude模型</el-tag>
              <el-tag size="small">对话专家</el-tag>
              <el-tag size="small">文本分析</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 技术特性 -->
    <el-card class="tech-features">
      <template #header>
        <h2><el-icon><Tools /></el-icon> 技术特性</h2>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="24" :md="12">
          <div class="tech-list">
            <h4>界面特性</h4>
            <ul>
              <li>响应式设计，支持移动端</li>
              <li>现代化UI，类似Cherry Studio风格</li>
              <li>直观的配置流程</li>
              <li>实时状态反馈</li>
            </ul>
          </div>
        </el-col>
        
        <el-col :xs="24" :md="12">
          <div class="tech-list">
            <h4>功能特性</h4>
            <ul>
              <li>多提供商统一管理</li>
              <li>API连接状态检测</li>
              <li>自定义模型配置</li>
              <li>批量测试功能</li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Key, Lock, Connection, Setting, Document, Star, Tools,
  ChatDotRound, Brush
} from '@element-plus/icons-vue'

const router = useRouter()

const openConfigPage = () => {
  router.push({ name: 'ApiKeyManagement' })
}

const goToChat = () => {
  ElMessage.info('请先配置API密钥后再使用聊天功能')
  router.push({ name: 'Chat' })
}

const goToDrawing = () => {
  ElMessage.info('请先配置API密钥后再使用绘画功能')
  router.push({ name: 'Drawing' })
}

const goToTargonDemo = () => {
  router.push({ name: 'TargonApiDemo' })
}

// 设置页面标题
document.title = 'API密钥配置演示 - AI创意平台'
</script>

<style scoped>
.api-key-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.demo-header p {
  font-size: 18px;
  color: #606266;
  margin: 0;
}

.features {
  margin-bottom: 40px;
}

.feature-card {
  text-align: center;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.feature-card p {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.usage-instructions {
  margin-bottom: 40px;
}

.usage-instructions :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.usage-instructions h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.instructions-content {
  padding: 20px 0;
}

.quick-start {
  margin: 30px 0;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.quick-start h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.quick-start ol {
  margin: 0;
  padding-left: 20px;
}

.quick-start li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

.demo-actions {
  text-align: center;
  margin-top: 30px;
}

.demo-actions .el-button {
  margin: 0 8px 8px 0;
}

.preset-showcase {
  margin-bottom: 40px;
}

.preset-showcase :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preset-showcase h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preset-item {
  padding: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  height: 100%;
  transition: all 0.3s ease;
}

.preset-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.preset-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.preset-logo {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: #409eff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
}

.preset-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.preset-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.preset-features {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* Targon API 特色样式 */
.targon-highlight {
  margin: 20px 0;
  border: 2px solid #67c23a;
  border-radius: 12px;
  overflow: hidden;
}

.targon-highlight :deep(.el-card__header) {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  padding: 20px;
}

.targon-highlight :deep(.el-card__header) h2 {
  color: white;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.targon-content {
  padding: 20px;
}

.targon-info h3 {
  color: #67c23a;
  font-size: 18px;
  margin: 0 0 16px 0;
}

.model-grid {
  margin: 20px 0;
}

.model-category {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  height: 100%;
  margin-bottom: 16px;
}

.model-category h4 {
  color: #303133;
  font-size: 16px;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.model-category ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.model-category li {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 4px;
}

.targon-features {
  margin: 20px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  font-size: 12px;
  padding: 4px 8px;
}

.api-info {
  background: #f0f9ff;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  margin-top: 20px;
}

.api-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #303133;
}

.api-info code {
  background: #e1f5fe;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #1976d2;
}

.targon-actions {
  margin-top: 20px;
  text-align: center;
}

.targon-actions .el-button {
  padding: 12px 24px;
  font-size: 16px;
}

.tech-features :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.tech-features h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tech-list h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.tech-list ul {
  margin: 0;
  padding-left: 20px;
}

.tech-list li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .api-key-demo {
    padding: 10px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
  
  .demo-header p {
    font-size: 16px;
  }
  
  .feature-card {
    height: auto;
    padding: 20px;
  }
  
  .demo-actions .el-button {
    width: 100%;
    margin: 0 0 12px 0;
  }
  
  .preset-item {
    margin-bottom: 20px;
  }
}
</style>
