<template>
  <div class="targon-demo">
    <div class="demo-header">
      <h1>Targon API 演示</h1>
      <p>体验 Targon API 的强大功能</p>
    </div>

    <!-- API 配置区域 -->
    <el-card class="config-section">
      <template #header>
        <h2><el-icon><Key /></el-icon> API 配置</h2>
      </template>
      
      <el-form :model="config" label-width="120px">
        <el-form-item label="API 密钥">
          <el-input 
            v-model="config.apiKey" 
            type="password" 
            placeholder="请输入您的 Targon API 密钥"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="API 地址">
          <el-input 
            v-model="config.baseUrl" 
            placeholder="https://api.targon.com/v1"
          />
        </el-form-item>
        
        <el-form-item label="选择模型">
          <el-select
            v-model="config.model"
            placeholder="请选择模型"
            style="width: 100%"
            :loading="loadingModels"
            filterable
          >
            <el-option
              v-for="model in availableModels"
              :key="model.id"
              :label="model.name || model.id"
              :value="model.id"
            >
              <div class="model-option">
                <span class="model-name">{{ model.name || model.id }}</span>
                <span class="model-owner">{{ model.owned_by || getModelOwner(model.id) }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="testing" 
            @click="testConnection"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          
          <el-button
            type="success"
            :disabled="!config.apiKey"
            :loading="loadingModels"
            @click="loadModels"
          >
            <el-icon><Refresh /></el-icon>
            {{ availableModels.length > 0 ? '刷新模型列表' : '获取模型列表' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 连接状态 -->
      <div v-if="connectionStatus" class="status-display">
        <el-alert 
          :type="connectionStatus.success ? 'success' : 'error'"
          :title="connectionStatus.message"
          :description="connectionStatus.details"
          show-icon
          :closable="false"
        />
      </div>
    </el-card>

    <!-- 聊天测试区域 -->
    <el-card class="chat-section">
      <template #header>
        <h2><el-icon><ChatDotRound /></el-icon> 聊天测试</h2>
      </template>
      
      <div class="chat-container">
        <div class="messages" ref="messagesContainer">
          <div 
            v-for="(message, index) in messages" 
            :key="index"
            :class="['message', message.role]"
          >
            <div class="message-content">
              <div class="message-role">{{ message.role === 'user' ? '用户' : 'AI' }}</div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>
        </div>
        
        <div class="input-area">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <el-button 
              type="primary" 
              :loading="chatting"
              :disabled="!config.apiKey || !config.model || !userInput.trim()"
              @click="sendMessage"
            >
              发送 (Ctrl+Enter)
            </el-button>
            <el-button @click="clearMessages">清空对话</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 模型信息展示 -->
    <el-card v-if="availableModels.length > 0" class="models-section">
      <template #header>
        <h2><el-icon><Document /></el-icon> 可用模型 ({{ availableModels.length }})</h2>
      </template>

      <div class="models-grid">
        <div
          v-for="model in availableModels"
          :key="model.id"
          class="model-card"
          :class="{ active: config.model === model.id }"
          @click="selectModel(model.id)"
        >
          <h4>{{ model.name || model.id }}</h4>
          <p>{{ model.owned_by || getModelOwner(model.id) }}</p>
          <div class="model-meta">
            <el-tag size="small" type="info">{{ model.id }}</el-tag>
            <el-tag v-if="model.created" size="small">{{ formatDate(model.created) }}</el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Key, Connection, ChatDotRound, Document, Refresh } from '@element-plus/icons-vue'
import targonApi from '@/services/targonApi'

// 响应式数据
const config = reactive({
  apiKey: '',
  baseUrl: 'https://api.targon.com/v1',
  model: ''
})

const testing = ref(false)
const chatting = ref(false)
const loadingModels = ref(false)
const connectionStatus = ref(null)
const messages = ref([])
const userInput = ref('')
const availableModels = ref([])
const messagesContainer = ref(null)

// 测试连接
const testConnection = async () => {
  if (!config.apiKey) {
    ElMessage.warning('请先输入API密钥')
    return
  }
  
  testing.value = true
  connectionStatus.value = null
  
  try {
    // 配置API服务
    targonApi.setApiKey(config.apiKey)
    targonApi.setBaseURL(config.baseUrl)
    
    // 测试连接
    const result = await targonApi.testConnection()
    
    connectionStatus.value = {
      success: result.success,
      message: result.message,
      details: result.success 
        ? `连接成功！检测到 ${result.data?.modelsCount || 0} 个可用模型`
        : result.error?.message || '连接失败'
    }
    
    if (result.success) {
      ElMessage.success('连接测试成功！')
    } else {
      ElMessage.error('连接测试失败')
    }
  } catch (error) {
    connectionStatus.value = {
      success: false,
      message: '连接失败',
      details: error.message
    }
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

// 获取模型列表
const loadModels = async () => {
  if (!config.apiKey) {
    ElMessage.warning('请先输入API密钥')
    return
  }

  loadingModels.value = true

  try {
    targonApi.setApiKey(config.apiKey)
    targonApi.setBaseURL(config.baseUrl)

    const result = await targonApi.getModels()

    if (result.success) {
      availableModels.value = result.data.map(model => ({
        id: model.id,
        name: model.id,
        owned_by: model.owned_by || getModelOwner(model.id),
        created: model.created,
        object: model.object
      }))

      // 如果当前没有选择模型，自动选择第一个
      if (!config.model && availableModels.value.length > 0) {
        config.model = availableModels.value[0].id
      }

      ElMessage.success(`成功获取 ${result.data.length} 个模型`)
    } else {
      ElMessage.error('获取模型列表失败: ' + result.message)
      availableModels.value = []
    }
  } catch (error) {
    ElMessage.error('获取模型列表失败: ' + error.message)
    availableModels.value = []
  } finally {
    loadingModels.value = false
  }
}

// 选择模型
const selectModel = (modelId) => {
  config.model = modelId
  ElMessage.success(`已选择模型: ${modelId}`)
}

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim()) return
  
  const message = userInput.value.trim()
  userInput.value = ''
  
  // 添加用户消息
  messages.value.push({
    role: 'user',
    content: message
  })
  
  chatting.value = true
  
  try {
    targonApi.setApiKey(config.apiKey)
    targonApi.setBaseURL(config.baseUrl)
    
    const result = await targonApi.chat({
      model: config.model,
      messages: messages.value,
      temperature: 0.7,
      max_tokens: 2048
    })
    
    if (result.success) {
      const aiResponse = result.data.choices[0].message.content
      messages.value.push({
        role: 'assistant',
        content: aiResponse
      })
      
      // 滚动到底部
      await nextTick()
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    } else {
      ElMessage.error('发送消息失败: ' + result.message)
    }
  } catch (error) {
    ElMessage.error('发送消息失败')
  } finally {
    chatting.value = false
  }
}

// 清空对话
const clearMessages = () => {
  messages.value = []
  ElMessage.success('对话已清空')
}

// 获取模型所有者
const getModelOwner = (modelId) => {
  if (modelId.includes('deepseek')) return 'DeepSeek'
  if (modelId.includes('moonshot') || modelId.includes('kimi')) return 'Moonshot AI'
  if (modelId.includes('qwen') || modelId.includes('Qwen')) return 'Alibaba'
  if (modelId.includes('gpt')) return 'OpenAI'
  if (modelId.includes('claude')) return 'Anthropic'
  return modelId.split('/')[0] || 'Unknown'
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleDateString()
}

// 保存配置到 localStorage
const saveConfig = () => {
  localStorage.setItem('targon_api_key', config.apiKey)
  localStorage.setItem('targon_base_url', config.baseUrl)
  localStorage.setItem('targon_selected_model', config.model)
}

// 从 localStorage 加载配置
const loadConfig = () => {
  const savedApiKey = localStorage.getItem('targon_api_key')
  const savedBaseUrl = localStorage.getItem('targon_base_url')
  const savedModel = localStorage.getItem('targon_selected_model')

  if (savedApiKey) config.apiKey = savedApiKey
  if (savedBaseUrl) config.baseUrl = savedBaseUrl
  if (savedModel) config.model = savedModel
}

// 监听API密钥变化，自动获取模型列表
watch(() => config.apiKey, (newKey) => {
  if (newKey && newKey.length > 10) {
    saveConfig()
    // 延迟一下再获取，避免频繁请求
    setTimeout(() => {
      loadModels()
    }, 500)
  } else {
    availableModels.value = []
    config.model = ''
  }
})

// 监听其他配置变化
watch(() => config.baseUrl, saveConfig)
watch(() => config.model, saveConfig)

// 组件挂载时的初始化
onMounted(() => {
  // 加载保存的配置
  loadConfig()

  // 如果有保存的API密钥，自动加载模型
  if (config.apiKey && config.apiKey.length > 10) {
    loadModels()
  }

  console.log('Targon API Demo 组件已加载')
})
</script>

<style scoped>
.targon-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  font-size: 32px;
  color: #303133;
  margin: 0 0 10px 0;
}

.demo-header p {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.config-section,
.chat-section,
.models-section {
  margin-bottom: 20px;
}

.status-display {
  margin-top: 20px;
}

.chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 15px;
}

.message {
  margin-bottom: 15px;
}

.message-content {
  max-width: 80%;
}

.message.user .message-content {
  margin-left: auto;
  text-align: right;
}

.message-role {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.message-text {
  padding: 10px 15px;
  border-radius: 10px;
  line-height: 1.5;
}

.message.user .message-text {
  background: #409eff;
  color: white;
}

.message.assistant .message-text {
  background: #f5f7fa;
  color: #303133;
}

.input-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.model-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.model-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.model-card.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.model-card h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.model-card p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.model-meta {
  display: flex;
  gap: 8px;
}

/* 模型选择下拉框样式 */
.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.model-name {
  font-weight: 500;
  color: #303133;
}

.model-owner {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>
