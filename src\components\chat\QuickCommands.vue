<template>
  <div class="quick-commands">
    <div class="commands-header">
      <span class="commands-title">快捷指令</span>
      <button @click="$emit('close')" class="close-commands">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
          <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
    </div>
    <div class="commands-list">
      <button
        v-for="command in commands"
        :key="command.id"
        @click="$emit('insert-command', command.text)"
        class="command-btn"
      >
        <div class="command-icon">{{ command.icon }}</div>
        <div class="command-content">
          <span class="command-label">{{ command.label }}</span>
          <span class="command-desc">{{ command.desc }}</span>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  commands: {
    type: Array,
    default: () => []
  }
})

defineEmits(['insert-command', 'close'])
</script>

<style lang="scss" scoped>
.quick-commands {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  margin: 1rem 2rem 0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;

  .commands-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);

    .commands-title {
      font-size: 0.9rem;
      font-weight: 600;
      color: white;
    }

    .close-commands {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: rgba(148, 163, 184, 0.8);

      &:hover {
        background: rgba(148, 163, 184, 0.1);
        color: white;
      }
    }
  }

  .commands-list {
    padding: 0.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;

    .command-btn {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: transparent;
      border: 1px solid rgba(148, 163, 184, 0.1);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;

      &:hover {
        background: rgba(99, 102, 241, 0.1);
        border-color: rgba(99, 102, 241, 0.3);
        transform: translateY(-1px);
      }

      .command-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(99, 102, 241, 0.1);
        border-radius: 10px;
        font-size: 1.2rem;
        flex-shrink: 0;
      }

      .command-content {
        flex: 1;
        min-width: 0;

        .command-label {
          display: block;
          font-size: 0.9rem;
          font-weight: 600;
          color: white;
          margin-bottom: 0.25rem;
        }

        .command-desc {
          display: block;
          font-size: 0.75rem;
          color: rgba(148, 163, 184, 0.7);
          line-height: 1.3;
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
