// API配置管理
export const API_CONFIG = {
  // OpenRouter API配置
  openrouter: {
    baseUrl: import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
    timeout: parseInt(import.meta.env.VITE_OPENROUTER_TIMEOUT) || 60000,
    maxRetries: parseInt(import.meta.env.VITE_OPENROUTER_MAX_RETRIES) || 3,
    retryDelay: parseInt(import.meta.env.VITE_OPENROUTER_RETRY_DELAY) || 1000,
    
    // 密钥轮询配置
    keyRotationEnabled: import.meta.env.VITE_OPENROUTER_KEY_ROTATION_ENABLED !== 'false',
    healthCheckInterval: parseInt(import.meta.env.VITE_OPENROUTER_KEY_HEALTH_CHECK_INTERVAL) || 300000, // 5分钟
    failureThreshold: parseInt(import.meta.env.VITE_OPENROUTER_KEY_FAILURE_THRESHOLD) || 3,
    
    // 默认模型
    defaultModel: 'deepseek/deepseek-chat-v3-0324:free'
  },
  
  // Pollinations API配置
  pollinations: {
    baseUrl: 'https://text.pollinations.ai',
    openaiUrl: 'https://text.pollinations.ai/openai',
    imageUrl: 'https://image.pollinations.ai/prompt',
    geminiUrl: 'https://apiv2.aliyahzombie.top/v1beta/openai',
    geminiApiKey: '6dc26766-144a-4db7-8375-72423b0d4a10',
    timeout: 30000,
    defaultModel: 'openai'
  },

  // Targon API配置
  targon: {
    baseUrl: 'https://api.targon.com/v1',
    timeout: 60000,
    maxRetries: 3,
    retryDelay: 1000,
    defaultModel: 'deepseek-ai/DeepSeek-V3'
  },

  // Claude API配置
  claude: {
    baseUrl: 'https://happy-cod-75.deno.dev/v1/chat/completions',
    timeout: 60000,
    maxRetries: 3,
    retryDelay: 1000,
    defaultModel: 'claude-3.5-sonnet'
  }
}

// 获取API配置
export function getApiConfig(provider = 'openrouter') {
  return API_CONFIG[provider] || API_CONFIG.openrouter
}

// 验证环境变量配置
export function validateEnvironmentConfig() {
  const issues = []
  
  // 检查OpenRouter密钥
  let hasValidKey = false
  for (let i = 1; i <= 10; i++) {
    const key = import.meta.env[`VITE_OPENROUTER_API_KEY_${i}`]
    if (key && key.trim() && key.startsWith('sk-or-v1-')) {
      hasValidKey = true
      break
    }
  }
  
  if (!hasValidKey) {
    issues.push({
      type: 'warning',
      message: '未找到有效的OpenRouter API密钥，将使用默认密钥'
    })
  }

  // 检查Targon密钥
  const targonKey = localStorage.getItem('targon_api_key')
  if (!targonKey || !targonKey.trim()) {
    issues.push({
      type: 'info',
      message: '未配置Targon API密钥，Targon模型将不可用'
    })
  }

  // 检查Claude密钥
  const claudeKey = localStorage.getItem('claude_api_key')
  if (!claudeKey || !claudeKey.trim()) {
    issues.push({
      type: 'info',
      message: '未配置Claude API密钥，Claude模型将不可用'
    })
  }
  
  // 检查超时配置
  const timeout = parseInt(import.meta.env.VITE_OPENROUTER_TIMEOUT)
  if (timeout && (timeout < 5000 || timeout > 300000)) {
    issues.push({
      type: 'warning',
      message: `API超时时间设置异常: ${timeout}ms，建议设置在5秒到5分钟之间`
    })
  }
  
  // 检查重试配置
  const maxRetries = parseInt(import.meta.env.VITE_OPENROUTER_MAX_RETRIES)
  if (maxRetries && (maxRetries < 1 || maxRetries > 10)) {
    issues.push({
      type: 'warning',
      message: `最大重试次数设置异常: ${maxRetries}，建议设置在1-10之间`
    })
  }
  
  // 检查健康检查间隔
  const healthCheckInterval = parseInt(import.meta.env.VITE_OPENROUTER_KEY_HEALTH_CHECK_INTERVAL)
  if (healthCheckInterval && (healthCheckInterval < 60000 || healthCheckInterval > 3600000)) {
    issues.push({
      type: 'warning',
      message: `健康检查间隔设置异常: ${healthCheckInterval}ms，建议设置在1分钟到1小时之间`
    })
  }
  
  return issues
}

// 获取环境信息
export function getEnvironmentInfo() {
  return {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    baseUrl: import.meta.env.BASE_URL,
    
    // OpenRouter配置状态
    openrouter: {
      hasCustomBaseUrl: !!import.meta.env.VITE_OPENROUTER_BASE_URL,
      hasCustomTimeout: !!import.meta.env.VITE_OPENROUTER_TIMEOUT,
      keyRotationEnabled: import.meta.env.VITE_OPENROUTER_KEY_ROTATION_ENABLED !== 'false',
      configuredKeys: (() => {
        let count = 0
        for (let i = 1; i <= 10; i++) {
          if (import.meta.env[`VITE_OPENROUTER_API_KEY_${i}`]) {
            count++
          }
        }
        return count
      })()
    },

    // Targon配置状态
    targon: {
      hasApiKey: !!localStorage.getItem('targon_api_key'),
      baseUrl: API_CONFIG.targon.baseUrl,
      defaultModel: API_CONFIG.targon.defaultModel
    }
  }
}

// 导出默认配置
export default API_CONFIG
