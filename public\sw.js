// Service Worker for AI Creative Platform
const CACHE_NAME = 'ai-creative-platform-v1.0.1'
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  '/offline.html'
]

// 检查是否为开发环境
const isDevelopment = location.hostname === 'localhost' || location.hostname === '127.0.0.1'

// 安装事件
self.addEventListener('install', (event) => {
  console.log('Service Worker: 安装中...')

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: 缓存静态资源')
        // 逐个添加资源，避免因单个资源失败导致整体失败
        return Promise.allSettled(
          STATIC_CACHE_URLS.map(url =>
            cache.add(url).catch(error => {
              console.warn(`Service Worker: 缓存资源失败 ${url}:`, error)
              return null
            })
          )
        )
      })
      .catch((error) => {
        console.error('Service Worker: 缓存静态资源失败', error)
      })
  )

  // 强制激活新的Service Worker
  self.skipWaiting()
})

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker: 激活中...')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: 删除旧缓存', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  
  // 立即控制所有客户端
  self.clients.claim()
})

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  // 开发环境：不缓存任何内容，直接从网络获取
  if (isDevelopment) {
    return
  }

  // 只处理GET请求
  if (event.request.method !== 'GET') {
    return
  }

  // 跳过非HTTP(S)请求
  if (!event.request.url.startsWith('http')) {
    return
  }

  // 跳过开发服务器的热更新请求
  if (event.request.url.includes('/@vite/') ||
      event.request.url.includes('/__vite_ping') ||
      event.request.url.includes('/node_modules/') ||
      event.request.url.includes('?t=')) {
    return
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // 如果缓存中有，直接返回
        if (response) {
          return response
        }

        // 否则从网络获取
        return fetch(event.request)
          .then((response) => {
            // 检查响应是否有效
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response
            }

            // 克隆响应，因为响应流只能使用一次
            const responseToCache = response.clone()

            // 缓存响应（仅缓存静态资源）
            if (shouldCache(event.request.url)) {
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache).catch(error => {
                    console.warn('Service Worker: 缓存响应失败:', error)
                  })
                })
                .catch(error => {
                  console.warn('Service Worker: 打开缓存失败:', error)
                })
            }

            return response
          })
          .catch((error) => {
            console.warn('Service Worker: 网络请求失败', event.request.url, error)

            // 如果是导航请求且网络失败，返回离线页面
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html').catch(() => {
                // 如果离线页面也不存在，返回一个简单的离线响应
                return new Response(`
                  <!DOCTYPE html>
                  <html>
                    <head><title>离线模式</title></head>
                    <body>
                      <h1>网络连接中断</h1>
                      <p>请检查您的网络连接</p>
                    </body>
                  </html>
                `, {
                  headers: { 'Content-Type': 'text/html' }
                })
              })
            }

            // 对于其他请求，直接抛出错误
            throw error
          })
      })
      .catch((error) => {
        console.warn('Service Worker: 缓存匹配失败', error)
        // 如果缓存匹配失败，尝试直接从网络获取
        return fetch(event.request)
      })
  )
})

// 判断是否应该缓存该URL
function shouldCache(url) {
  // 跳过开发环境的特殊请求
  if (url.includes('/@vite/') ||
      url.includes('/__vite_ping') ||
      url.includes('/node_modules/') ||
      url.includes('?import') ||
      url.includes('?direct') ||
      url.includes('hot-update')) {
    return false
  }

  // 缓存静态资源
  return url.includes('/assets/') ||
         url.includes('/icons/') ||
         url.endsWith('.css') ||
         url.endsWith('.js') ||
         url.endsWith('.svg') ||
         url.endsWith('.png') ||
         url.endsWith('.jpg') ||
         url.endsWith('.jpeg') ||
         url.endsWith('.gif') ||
         url.endsWith('.webp') ||
         url.endsWith('.woff') ||
         url.endsWith('.woff2') ||
         url.endsWith('.ttf')
}

// 监听消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})
