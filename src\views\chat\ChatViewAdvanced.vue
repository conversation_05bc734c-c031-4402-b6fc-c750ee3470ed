<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 主布局 -->
    <div class="flex h-screen">
      <!-- 侧边栏 -->
      <aside 
        class="bg-white border-r border-gray-200 shadow-lg transition-all duration-300"
        :class="sidebarCollapsed ? 'w-16' : 'w-80'"
      >
        <!-- 侧边栏头部 -->
        <div class="p-4 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <button
              @click="toggleSidebar"
              class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="transition-transform" :class="{ 'rotate-180': sidebarCollapsed }">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
            <h1 v-if="!sidebarCollapsed" class="text-xl font-bold text-gray-900">AI 智能助手</h1>
          </div>
        </div>

        <div v-if="!sidebarCollapsed" class="flex flex-col h-full">
          <!-- 新建对话按钮 -->
          <div class="p-4">
            <button
              @click="createNewConversation"
              class="w-full flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all transform hover:scale-105"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>新建对话</span>
            </button>
          </div>

          <!-- AI模型选择器 -->
          <div class="px-4 pb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">AI模型</label>
            <ModelSelector
              v-model="selectedModel"
              @model-changed="handleModelChange"
            />
          </div>

          <!-- 快速功能 -->
          <div class="px-4 pb-4">
            <h3 class="text-sm font-medium text-gray-700 mb-3">快速功能</h3>
            <div class="grid grid-cols-2 gap-2">
              <button
                v-for="feature in quickFeatures"
                :key="feature.id"
                @click="useQuickFeature(feature)"
                class="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm"
              >
                <div class="text-lg mb-1">{{ feature.icon }}</div>
                <div class="font-medium">{{ feature.name }}</div>
              </button>
            </div>
          </div>

          <!-- 对话列表 -->
          <div class="flex-1 overflow-y-auto px-4">
            <div class="text-sm font-medium text-gray-500 mb-3">对话历史</div>
            <div class="space-y-2">
              <div
                v-for="conversation in conversations"
                :key="conversation.id"
                class="group relative"
              >
                <button
                  @click="switchConversation(conversation.id)"
                  class="w-full text-left p-3 rounded-lg transition-all"
                  :class="currentConversationId === conversation.id 
                    ? 'bg-blue-50 border border-blue-200 text-blue-900' 
                    : 'hover:bg-gray-50 border border-transparent'"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                      <div class="font-medium truncate">{{ conversation.title }}</div>
                      <div class="text-sm text-gray-500 truncate">{{ conversation.lastMessage }}</div>
                      <div class="text-xs text-gray-400 mt-1">{{ formatDate(conversation.updatedAt) }}</div>
                    </div>
                    <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        @click.stop="pinConversation(conversation.id)"
                        class="p-1 hover:bg-gray-200 rounded"
                        :class="conversation.pinned ? 'text-yellow-500' : 'text-gray-400'"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M16 4v8l-4 4-4-4V4h8z" :fill="conversation.pinned ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                      <button
                        @click.stop="deleteConversation(conversation.id)"
                        class="p-1 hover:bg-red-100 text-red-400 hover:text-red-600 rounded"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <!-- 用户信息 -->
          <div class="p-4 border-t border-gray-100">
            <div class="flex items-center space-x-3">
              <img
                src="https://picsum.photos/40/40?random=user"
                alt="用户头像"
                class="w-10 h-10 rounded-full"
              />
              <div class="flex-1 min-w-0">
                <div class="font-medium text-gray-900">用户</div>
                <div class="text-sm text-gray-500">高级会员</div>
              </div>
              <button
                @click="showSettings = true"
                class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke="currentColor" stroke-width="2"/>
                  <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </aside>

      <!-- 主聊天区域 -->
      <main class="flex-1 flex flex-col">
        <!-- 聊天头部 -->
        <header class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <span class="text-white font-semibold">{{ currentAssistant?.name?.charAt(0) || 'A' }}</span>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900">{{ currentAssistant?.name || '智能助手' }}</h2>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>在线</span>
                    <span>•</span>
                    <span>{{ currentAssistant?.description }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <!-- 语音输入 -->
              <button
                @click="toggleVoiceInput"
                class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                :class="isListening ? 'text-red-500' : 'text-gray-600'"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 1a3 3 0 00-3 3v8a3 3 0 006 0V4a3 3 0 00-3-3z" :fill="isListening ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2"/>
                  <path d="M19 10v2a7 7 0 01-14 0v-2M12 19v4M8 23h8" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              
              <!-- 导出对话 -->
              <button
                @click="exportConversation"
                class="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              
              <!-- 清空对话 -->
              <button
                @click="clearConversation"
                class="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </div>
        </header>

        <!-- 消息列表 -->
        <div class="flex-1 overflow-y-auto p-4" ref="messagesContainer">
          <!-- 欢迎消息 -->
          <div v-if="messages.length === 0" class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" class="text-white">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">欢迎使用 {{ currentAssistant?.name || 'AI助手' }}</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">{{ currentAssistant?.welcome || '我是您的智能助手，可以帮您解答问题、分析数据、创作内容。请随时开始对话！' }}</p>
            
            <!-- 功能介绍 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8">
              <div
                v-for="capability in currentAssistant?.capabilities || defaultCapabilities"
                :key="capability.id"
                class="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
              >
                <div class="text-3xl mb-3">{{ capability.icon }}</div>
                <h4 class="font-semibold text-gray-900 mb-2">{{ capability.title }}</h4>
                <p class="text-sm text-gray-600">{{ capability.description }}</p>
              </div>
            </div>

            <!-- 建议问题 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
              <button
                v-for="suggestion in currentAssistant?.suggestions || defaultSuggestions"
                :key="suggestion"
                @click="sendMessage(suggestion)"
                class="p-4 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all"
              >
                <div class="text-sm text-gray-900">{{ suggestion }}</div>
              </button>
            </div>
          </div>

          <!-- 消息列表 -->
          <div class="space-y-4">
            <div
              v-for="message in messages"
              :key="message.id"
              class="flex"
              :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
            >
              <div
                class="flex items-start w-full"
                :class="[
                  message.role === 'user'
                    ? 'flex-row-reverse space-x-reverse space-x-3 max-w-4xl ml-auto'
                    : 'space-x-3 max-w-5xl'
                ]"
              >
                <!-- AI头像 -->
                <div
                  v-if="message.role === 'assistant'"
                  class="ai-avatar flex-shrink-0"
                >
                  <div class="avatar-container">
                    <div
                      class="avatar-inner ai-model-avatar"
                      :style="getModelAvatarBackgroundStyle(message.model || 'default')"
                      v-html="getModelAvatarSvg(message.model || 'default')"
                    >
                    </div>
                  </div>
                </div>

                <!-- 用户头像 -->
                <div
                  v-if="message.role === 'user'"
                  class="user-avatar flex-shrink-0"
                >
                  <div class="avatar-container">
                    <div class="avatar-inner">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" class="user-icon">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <!-- 消息内容 -->
                <div
                  class="message-bubble flex-1"
                  :class="message.role === 'user' ? 'user-bubble' : 'ai-bubble'"
                >
                  <!-- 消息文本 -->
                  <div class="message-text" v-html="formatMessage(message.content)"></div>
                  
                  <!-- 消息附件 -->
                  <div v-if="message.attachments && message.attachments.length > 0" class="mt-3 space-y-2">
                    <div
                      v-for="attachment in message.attachments"
                      :key="attachment.id"
                      class="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66L9.64 16.2a2 2 0 01-2.83-2.83l8.49-8.49" stroke="currentColor" stroke-width="2"/>
                      </svg>
                      <span class="text-sm">{{ attachment.name }}</span>
                    </div>
                  </div>

                  <!-- 用户消息时间戳 -->
                  <div v-if="message.role === 'user'" class="mt-2 text-right">
                    <div class="text-xs text-blue-100 flex items-center justify-end space-x-2 opacity-80">
                      <span v-if="message.model" class="px-2 py-0.5 bg-blue-400 bg-opacity-40 rounded-full text-xs">{{ message.model }}</span>
                      <span>{{ formatTime(message.timestamp) }}</span>
                    </div>
                  </div>

                  <!-- 消息操作 -->
                  <div v-if="message.role === 'assistant'" class="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                    <div class="flex items-center space-x-2">
                      <button
                        @click="copyMessage(message.content)"
                        class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M16 4h2a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h2m8 0V2a2 2 0 00-2-2H10a2 2 0 00-2 2v2h8z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                      <button
                        @click="regenerateResponse(message)"
                        class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2"/>
                          <path d="M20.49 9A9 9 0 005.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 013.51 15" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                      <button
                        @click="rateMessage(message, 'like')"
                        class="p-1 hover:bg-gray-100 rounded transition-colors"
                        :class="message.rating === 'like' ? 'text-green-500' : 'text-gray-500 hover:text-gray-700'"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M14 9V5a3 3 0 00-3-3l-4 9v11h11.28a2 2 0 002-1.7l1.38-9a2 2 0 00-2-2.3zM7 22H4a2 2 0 01-2-2v-7a2 2 0 012-2h3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                      <button
                        @click="rateMessage(message, 'dislike')"
                        class="p-1 hover:bg-gray-100 rounded transition-colors"
                        :class="message.rating === 'dislike' ? 'text-red-500' : 'text-gray-500 hover:text-gray-700'"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <path d="M10 15v4a3 3 0 003 3l4-9V2H5.72a2 2 0 00-2 1.7l-1.38 9a2 2 0 002 2.3zm7-13h2.67A2.31 2.31 0 0122 4v7a2.31 2.31 0 01-2.33 2H17" stroke="currentColor" stroke-width="2"/>
                        </svg>
                      </button>
                    </div>
                    <div class="text-xs text-gray-400 flex items-center space-x-2">
                      <span v-if="message.model" class="px-2 py-1 bg-gray-100 rounded-full">{{ message.model }}</span>
                      <span>{{ formatTime(message.timestamp) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 用户头像 -->
                <div
                  v-if="message.role === 'user'"
                  class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <span class="text-gray-600 text-sm font-semibold">U</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="flex justify-start">
            <div class="flex items-start space-x-3 max-w-4xl">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-semibold">{{ currentAssistant?.name?.charAt(0) || 'A' }}</span>
              </div>
              <div class="bg-white border border-gray-200 rounded-2xl px-4 py-3">
                <div class="flex items-center space-x-2">
                  <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                  </div>
                  <span class="text-sm text-gray-500">{{ currentAssistant?.name || 'AI' }}正在思考...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <footer class="bg-white border-t border-gray-200 p-4">
          <!-- 快捷操作栏 -->
          <div v-if="showQuickActions" class="flex items-center space-x-2 mb-3 pb-3 border-b border-gray-100">
            <button
              v-for="action in quickActions"
              :key="action.id"
              @click="useQuickAction(action)"
              class="flex items-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm transition-colors"
            >
              <span>{{ action.icon }}</span>
              <span>{{ action.name }}</span>
            </button>
          </div>

          <div class="flex items-end space-x-3">
            <!-- 附件上传 -->
            <button
              @click="$refs.fileInput.click()"
              class="p-3 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66L9.64 16.2a2 2 0 01-2.83-2.83l8.49-8.49" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
            <input
              ref="fileInput"
              type="file"
              multiple
              class="hidden"
              @change="handleFileUpload"
            />

            <!-- 输入框 -->
            <div class="flex-1 relative">
              <textarea
                v-model="inputMessage"
                @keydown.enter.exact.prevent="handleSend"
                @keydown.enter.shift.exact="inputMessage += '\n'"
                @input="handleInput"
                placeholder="输入您的问题... (Enter发送，Shift+Enter换行)"
                rows="1"
                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                style="min-height: 44px; max-height: 120px"
              ></textarea>
              
              <!-- 快捷操作按钮 -->
              <button
                @click="showQuickActions = !showQuickActions"
                class="absolute right-3 top-3 p-1 hover:bg-gray-100 rounded transition-colors text-gray-500"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>

            <!-- 发送按钮 -->
            <button
              @click="handleSend"
              :disabled="!inputMessage.trim() || isLoading"
              class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              <svg v-if="isLoading" width="20" height="20" viewBox="0 0 24 24" fill="none" class="animate-spin">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
                <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" fill="currentColor"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>

          <!-- 上传的文件预览 -->
          <div v-if="uploadedFiles.length > 0" class="mt-3 flex flex-wrap gap-2">
            <div
              v-for="file in uploadedFiles"
              :key="file.id"
              class="flex items-center space-x-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" stroke="currentColor" stroke-width="2"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span class="text-sm text-blue-700">{{ file.name }}</span>
              <button
                @click="removeFile(file.id)"
                class="text-blue-500 hover:text-blue-700"
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </div>
        </footer>
      </main>
    </div>

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="设置" width="500px">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">主题设置</label>
          <select v-model="theme" class="w-full p-3 border border-gray-300 rounded-lg">
            <option value="light">浅色主题</option>
            <option value="dark">深色主题</option>
            <option value="auto">跟随系统</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">字体大小</label>
          <select v-model="fontSize" class="w-full p-3 border border-gray-300 rounded-lg">
            <option value="small">小</option>
            <option value="medium">中</option>
            <option value="large">大</option>
          </select>
        </div>
        
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700">启用语音输入</label>
          <input v-model="enableVoice" type="checkbox" class="rounded">
        </div>
        
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700">自动保存对话</label>
          <input v-model="autoSave" type="checkbox" class="rounded">
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import ModelSelector from '@/components/chat/ModelSelector.vue'
import { generateChatCompletion, generateChatCompletionStream } from '@/services/chatService.js'
import { marked } from 'marked'
import { getModelIcon, getModelColor } from '@/components/chat/ModelIconsNew.js'
import hljs from 'highlight.js'
import 'highlight.js/styles/vs2015.css' // VS Code 深色主题

// 响应式数据
const sidebarCollapsed = ref(false)
const selectedAssistant = ref('general')
const selectedModel = ref('gemini-2.5-flash')
const currentConversationId = ref(1)
const inputMessage = ref('')
const isLoading = ref(false)
const isListening = ref(false)
const showQuickActions = ref(false)
const showSettings = ref(false)
const uploadedFiles = ref([])
const messagesContainer = ref(null)

// 设置
const theme = ref('light')
const fontSize = ref('medium')
const enableVoice = ref(true)
const autoSave = ref(true)

// AI助手配置
const aiAssistants = ref([
  {
    id: 'general',
    name: '通用助手',
    description: '全能AI助手',
    welcome: '我是您的智能助手，可以帮您解答问题、分析数据、创作内容。',
    capabilities: [
      { id: 1, icon: '💬', title: '智能对话', description: '自然流畅的对话交流' },
      { id: 2, icon: '📊', title: '数据分析', description: '专业的数据分析和可视化' },
      { id: 3, icon: '✍️', title: '内容创作', description: '高质量的文案和创意内容' }
    ],
    suggestions: [
      '帮我分析一下最新的市场趋势',
      '写一份产品介绍文案',
      '解释一下人工智能的发展历程',
      '推荐一些提高工作效率的方法'
    ]
  },
  {
    id: 'coding',
    name: '编程助手',
    description: '专业代码助手',
    welcome: '我是您的编程助手，可以帮您解决代码问题、优化算法、学习新技术。',
    capabilities: [
      { id: 1, icon: '💻', title: '代码编写', description: '高质量代码生成和优化' },
      { id: 2, icon: '🐛', title: '调试帮助', description: '快速定位和解决bug' },
      { id: 3, icon: '📚', title: '技术学习', description: '编程知识和最佳实践' }
    ],
    suggestions: [
      '帮我写一个Python爬虫程序',
      '解释一下React Hooks的使用',
      '优化这段SQL查询语句',
      '推荐一些前端开发工具'
    ]
  },
  {
    id: 'creative',
    name: '创意助手',
    description: '创意内容专家',
    welcome: '我是您的创意助手，专注于创意写作、营销文案、品牌策划等创意工作。',
    capabilities: [
      { id: 1, icon: '🎨', title: '创意写作', description: '原创故事和创意内容' },
      { id: 2, icon: '📢', title: '营销文案', description: '吸引人的营销和广告文案' },
      { id: 3, icon: '🎯', title: '品牌策划', description: '品牌定位和策略建议' }
    ],
    suggestions: [
      '为新产品写一个创意广告文案',
      '帮我构思一个短故事情节',
      '设计一个品牌slogan',
      '写一份社交媒体营销策略'
    ]
  }
])

// 默认功能和建议
const defaultCapabilities = ref([
  { id: 1, icon: '💬', title: '智能对话', description: '自然流畅的对话交流' },
  { id: 2, icon: '📊', title: '数据分析', description: '专业的数据分析和可视化' },
  { id: 3, icon: '✍️', title: '内容创作', description: '高质量的文案和创意内容' }
])

const defaultSuggestions = ref([
  '帮我分析一下最新的市场趋势',
  '写一份产品介绍文案',
  '解释一下人工智能的发展历程',
  '推荐一些提高工作效率的方法'
])

// 快速功能
const quickFeatures = ref([
  { id: 1, name: '翻译', icon: '🌐' },
  { id: 2, name: '总结', icon: '📝' },
  { id: 3, name: '写作', icon: '✍️' },
  { id: 4, name: '分析', icon: '📊' }
])

// 快捷操作
const quickActions = ref([
  { id: 1, name: '总结上文', icon: '📋' },
  { id: 2, name: '继续写作', icon: '✏️' },
  { id: 3, name: '翻译内容', icon: '🌍' },
  { id: 4, name: '优化表达', icon: '✨' }
])

// 对话数据
const conversations = ref([
  {
    id: 1,
    title: '新对话',
    lastMessage: '开始新的对话...',
    updatedAt: new Date(),
    pinned: false,
    messages: []
  }
])

const messages = ref([
  // 添加一些测试消息来展示不同的AI模型图标
  {
    id: 1,
    role: 'assistant',
    content: '你好！我是Claude，Anthropic开发的AI助手。我可以帮助您进行各种任务，包括写作、分析、编程等。',
    timestamp: new Date(),
    model: 'claude'
  },
  {
    id: 2,
    role: 'assistant',
    content: '我是GPT-4，OpenAI的最新语言模型。我具有强大的推理能力和广泛的知识基础。',
    timestamp: new Date(),
    model: 'gpt-4'
  },
  {
    id: 3,
    role: 'assistant',
    content: '我是Gemini，Google开发的多模态AI模型。我可以处理文本、图像和代码。',
    timestamp: new Date(),
    model: 'gemini'
  },
  {
    id: 4,
    role: 'assistant',
    content: '我是Mistral AI，专注于高效的语言处理和推理任务。',
    timestamp: new Date(),
    model: 'mistral'
  },
  {
    id: 5,
    role: 'assistant',
    content: '我是Qwen，阿里巴巴开发的大语言模型，擅长中文理解和生成。',
    timestamp: new Date(),
    model: 'qwen'
  },
  {
    id: 6,
    role: 'assistant',
    content: '我是Llama，Meta开发的开源大语言模型。',
    timestamp: new Date(),
    model: 'llama'
  }
])

// 计算属性
const currentConversation = computed(() => {
  return conversations.value.find(c => c.id === currentConversationId.value)
})

const currentAssistant = computed(() => {
  return aiAssistants.value.find(a => a.id === selectedAssistant.value)
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const createNewConversation = () => {
  const newConversation = {
    id: Date.now(),
    title: '新对话',
    lastMessage: '开始新的对话...',
    updatedAt: new Date(),
    pinned: false,
    messages: []
  }
  conversations.value.unshift(newConversation)
  switchConversation(newConversation.id)
}

const switchConversation = (id) => {
  currentConversationId.value = id
  const conversation = conversations.value.find(c => c.id === id)
  if (conversation) {
    messages.value = [...conversation.messages]
  }
  scrollToBottom()
}

const switchAssistant = () => {
  ElMessage.success(`已切换到${currentAssistant.value.name}`)
}

const handleModelChange = (model) => {
  console.log('模型已切换:', model)
  // 更新当前对话的模型信息
  if (currentConversation.value) {
    currentConversation.value.model = model.id
    currentConversation.value.modelName = model.name
  }

  // 发送系统消息通知模型切换
  const systemMessage = {
    id: Date.now(),
    type: 'system',
    content: `已切换到 ${model.name}`,
    timestamp: new Date(),
    model: model.id
  }

  messages.value.push(systemMessage)
  scrollToBottom()
}

const pinConversation = (id) => {
  const conversation = conversations.value.find(c => c.id === id)
  if (conversation) {
    conversation.pinned = !conversation.pinned
    ElMessage.success(conversation.pinned ? '已置顶' : '已取消置顶')
  }
}

const deleteConversation = (id) => {
  const index = conversations.value.findIndex(c => c.id === id)
  if (index !== -1) {
    conversations.value.splice(index, 1)
    if (currentConversationId.value === id && conversations.value.length > 0) {
      switchConversation(conversations.value[0].id)
    }
    ElMessage.success('对话已删除')
  }
}

const useQuickFeature = (feature) => {
  inputMessage.value = `请帮我${feature.name}:`
  ElMessage.success(`已选择${feature.name}功能`)
}

const useQuickAction = (action) => {
  // 根据不同的快捷操作执行相应的逻辑
  switch (action.id) {
    case 1: // 总结上文
      if (messages.value.length > 0) {
        sendMessage('请总结一下我们刚才的对话内容')
      }
      break
    case 2: // 继续写作
      sendMessage('请继续完善上面的内容')
      break
    case 3: // 翻译内容
      sendMessage('请将上面的内容翻译成英文')
      break
    case 4: // 优化表达
      sendMessage('请帮我优化一下表达方式')
      break
  }
  showQuickActions.value = false
}

const handleSend = () => {
  if (!inputMessage.value.trim() || isLoading.value) return
  sendMessage(inputMessage.value)
  inputMessage.value = ''
}

const sendMessage = async (content) => {
  // 添加用户消息
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: content,
    timestamp: new Date(),
    attachments: [...uploadedFiles.value],
    model: selectedModel.value
  }
  messages.value.push(userMessage)
  uploadedFiles.value = []

  // 更新对话标题和最后消息
  if (currentConversation.value) {
    if (currentConversation.value.title === '新对话') {
      currentConversation.value.title = content.slice(0, 30) + (content.length > 30 ? '...' : '')
    }
    currentConversation.value.lastMessage = content.slice(0, 50) + (content.length > 50 ? '...' : '')
    currentConversation.value.updatedAt = new Date()
  }

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 显示加载状态
  isLoading.value = true

  try {
    // 准备消息历史
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))

    // 调用真实的AI API
    const result = await generateChatCompletion(chatMessages, {
      model: selectedModel.value || 'openai',
      temperature: 0.7,
      maxTokens: 2000
    })

    if (result.success) {
      const aiMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: result.content,
        timestamp: new Date(),
        model: selectedModel.value,
        usage: result.usage
      }
      messages.value.push(aiMessage)

      // 保存到对话历史
      if (currentConversation.value) {
        currentConversation.value.messages = [...messages.value]
        currentConversation.value.lastMessage = aiMessage.content.slice(0, 50) + (aiMessage.content.length > 50 ? '...' : '')
        currentConversation.value.updatedAt = new Date()
      }

      // 显示成功通知
      ElNotification({
        title: 'AI回复',
        message: `使用模型: ${selectedModel.value}`,
        type: 'success',
        duration: 2000,
        position: 'bottom-right'
      })
    } else {
      throw new Error(result.error || '生成回复失败')
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error(`发送失败: ${error.message || '请重试'}`)

    // 添加错误消息
    const errorMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: '抱歉，我现在无法回复您的消息。请稍后重试。',
      timestamp: new Date(),
      model: selectedModel.value,
      isError: true
    }
    messages.value.push(errorMessage)
  } finally {
    isLoading.value = false
  }

  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

// generateMockResponse函数已删除，现在使用真实的AI API

const handleInput = () => {
  // 自动调整文本域高度
  const textarea = event.target
  textarea.style.height = 'auto'
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
}

const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    uploadedFiles.value.push({
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    })
  })
  ElMessage.success(`已添加 ${files.length} 个文件`)
}

const removeFile = (fileId) => {
  const index = uploadedFiles.value.findIndex(f => f.id === fileId)
  if (index !== -1) {
    uploadedFiles.value.splice(index, 1)
  }
}

const toggleVoiceInput = () => {
  if (!enableVoice.value) {
    ElMessage.warning('请先在设置中启用语音输入功能')
    return
  }
  
  isListening.value = !isListening.value
  if (isListening.value) {
    ElMessage.info('开始语音输入...')
    // 这里可以集成语音识别API
    setTimeout(() => {
      isListening.value = false
      ElMessage.success('语音输入结束')
    }, 3000)
  }
}

const exportConversation = () => {
  const conversation = currentConversation.value
  if (conversation && conversation.messages.length > 0) {
    // 模拟导出功能
    ElMessage.success('对话已导出到下载文件夹')
  } else {
    ElMessage.warning('当前对话为空，无法导出')
  }
}

const clearConversation = () => {
  messages.value = []
  if (currentConversation.value) {
    currentConversation.value.messages = []
    currentConversation.value.title = '新对话'
    currentConversation.value.lastMessage = '开始新的对话...'
  }
  ElMessage.success('对话已清空')
}

const copyMessage = (content) => {
  navigator.clipboard.writeText(content)
  ElMessage.success('内容已复制到剪贴板')
}

const regenerateResponse = (message) => {
  const index = messages.value.findIndex(m => m.id === message.id)
  if (index !== -1) {
    // 重新生成回复
    isLoading.value = true
    setTimeout(() => {
      messages.value[index].content = generateMockResponse('重新生成的回复')
      isLoading.value = false
      ElMessage.success('回复已重新生成')
    }, 1000)
  }
}

const rateMessage = (message, rating) => {
  message.rating = message.rating === rating ? null : rating
  ElMessage.success(rating === 'like' ? '感谢您的反馈！' : '我们会继续改进')
}

const formatMessage = (content) => {
  if (!content) return ''

  try {
    // 使用 marked 进行完整的 Markdown 渲染
    let html = marked(content)

    // 为代码块添加VS Code风格样式和语法高亮
    html = html.replace(
      /<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
      (match, lang, code) => {
        // 使用 highlight.js 进行语法高亮
        let highlightedCode = code
        try {
          if (hljs && hljs.getLanguage(lang)) {
            highlightedCode = hljs.highlight(code, { language: lang }).value
          } else {
            highlightedCode = hljs.highlightAuto(code).value
          }
        } catch (e) {
          console.warn('Syntax highlighting failed:', e)
          highlightedCode = code // 如果高亮失败，使用原始代码
        }

        return `
          <div class="vs-code-block">
            <div class="code-header">
              <div class="window-controls">
                <span class="control close"></span>
                <span class="control minimize"></span>
                <span class="control maximize"></span>
              </div>
              <span class="language-label">${lang}</span>
              <button class="copy-btn" onclick="copyCodeToClipboard(this)" title="复制代码">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
            <pre><code class="language-${lang}">${highlightedCode}</code></pre>
          </div>
        `
      }
    )

    // 处理没有语言标识的代码块
    html = html.replace(
      /<pre><code>([\s\S]*?)<\/code><\/pre>/g,
      (match, code) => {
        return `
          <div class="vs-code-block">
            <div class="code-header">
              <div class="window-controls">
                <span class="control close"></span>
                <span class="control minimize"></span>
                <span class="control maximize"></span>
              </div>
              <span class="language-label">代码</span>
              <button class="copy-btn" onclick="copyCodeToClipboard(this)" title="复制代码">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
            <pre><code>${code}</code></pre>
          </div>
        `
      }
    )

    return html
  } catch (error) {
    console.error('Markdown parsing error:', error)
    return content
  }
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDate = (date) => {
  const now = new Date()
  const diff = now - new Date(date)
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return new Date(date).toLocaleDateString()
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 生命周期
onMounted(() => {
  // 配置 marked
  marked.setOptions({
    highlight: function(code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value
        } catch (err) {
          console.error('代码高亮失败:', err)
        }
      }
      return hljs.highlightAuto(code).value
    },
    breaks: true,
    gfm: true
  })

  // 添加全局复制代码函数
  window.copyCodeToClipboard = function(button) {
    const codeBlock = button.closest('.vs-code-block')
    const code = codeBlock.querySelector('code').textContent

    navigator.clipboard.writeText(code).then(() => {
      button.classList.add('copied')
      ElMessage.success('代码已复制到剪贴板')

      setTimeout(() => {
        button.classList.remove('copied')
      }, 2000)
    }).catch(err => {
      console.error('复制失败:', err)
      // 降级方案
      try {
        const textArea = document.createElement('textarea')
        textArea.value = code
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)

        button.classList.add('copied')
        ElMessage.success('代码已复制到剪贴板')
        setTimeout(() => {
          button.classList.remove('copied')
        }, 2000)
      } catch (fallbackErr) {
        ElMessage.error('复制失败，请手动复制')
      }
    })
  }

  // 初始化
  scrollToBottom()
})

// 在setup中定义的函数可以直接在模板中使用
const getModelIconForTemplate = (modelName) => {
  return getModelIcon(modelName || 'default')
}

// 获取模型头像的背景样式（透明背景，只保留阴影）
const getModelAvatarBackgroundStyle = (modelName) => {
  const color = getModelColor(modelName || 'default')

  return {
    backgroundColor: 'transparent',
    border: 'none',
    boxShadow: `0 2px 8px rgba(0, 0, 0, 0.1)`
  }
}

// 获取模型头像的SVG内容（保持原始尺寸）
const getModelAvatarSvg = (modelName) => {
  const icon = getModelIcon(modelName || 'default')

  // 调试信息
  console.log(`Model: ${modelName}, Icon length: ${icon.length}`)

  // 清理SVG内容，保持原始尺寸
  let modifiedSvg = icon.replace(/[\r\n\t]/g, '').trim()

  // 设置固定尺寸，适合40px的容器
  modifiedSvg = modifiedSvg.replace(/width="[^"]*"/g, 'width="32"')
  modifiedSvg = modifiedSvg.replace(/height="[^"]*"/g, 'height="32"')

  // 如果没有width和height属性，添加它们
  if (!modifiedSvg.includes('width=')) {
    modifiedSvg = modifiedSvg.replace('<svg', '<svg width="32" height="32"')
  }

  // 确保有viewBox
  if (!modifiedSvg.includes('viewBox')) {
    modifiedSvg = modifiedSvg.replace('<svg', '<svg viewBox="0 0 24 24"')
  }

  return modifiedSvg
}
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本域自动调整高度 */
textarea {
  field-sizing: content;
}

/* 代码块样式 - 已被新的 VS Code 风格覆盖 */

/* 消息动画 */
.message-enter-active,
.message-leave-active {
  transition: all 0.3s ease;
}

.message-enter-from,
.message-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 行高限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 头像样式 */
.ai-avatar, .user-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  margin-top: 2px;
}

.avatar-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.avatar-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* AI头像样式 - 仅用于非模型头像 */
.ai-avatar .avatar-inner:not(.ai-model-avatar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
}

/* AI模型头像样式 - 纯SVG图标 */
.ai-avatar .ai-model-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* 透明背景 */
}

/* SVG图标样式 */
.ai-avatar .ai-model-avatar svg {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

/* 模型头像hover效果 */
.ai-avatar:hover .ai-model-avatar {
  transform: scale(1.05);
}

/* SVG图标样式 */
.ai-avatar .avatar-inner svg {
  width: 20px;
  height: 20px;
  color: white;
  fill: currentColor;
}

.ai-avatar:hover .avatar-inner:not(.ai-model-avatar) {
  transform: scale(1.03);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.35);
}

/* 用户头像样式 */
.user-avatar .avatar-inner {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 2px 6px rgba(79, 172, 254, 0.25);
}

.user-avatar:hover .avatar-inner {
  transform: scale(1.03);
  box-shadow: 0 3px 10px rgba(79, 172, 254, 0.35);
}

.ai-icon, .user-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息气泡样式 */
.message-bubble {
  position: relative;
  padding: 14px 18px;
  border-radius: 18px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.user-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
  max-width: 85%;
}

.user-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 20px;
  pointer-events: none;
}

.ai-bubble {
  background: rgba(255, 255, 255, 0.98);
  color: #1a202c;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  max-width: 90%;
}

.ai-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  border-radius: 20px;
  pointer-events: none;
}

.message-text {
  position: relative;
  z-index: 1;
  line-height: 1.6;
}

/* 悬停效果 */
.message-bubble:hover {
  transform: translateY(-1px);
}

.user-bubble:hover {
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.35);
}

.ai-bubble:hover {
  box-shadow: 0 6px 28px rgba(0, 0, 0, 0.12);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .message-bubble {
    padding: 12px 16px;
    border-radius: 16px;
  }

  .user-bubble {
    max-width: 90%;
  }

  .ai-bubble {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    padding: 10px 14px;
    border-radius: 14px;
  }

  .user-bubble,
  .ai-bubble {
    max-width: 100%;
  }
}

/* 消息操作按钮优化 */
.message-bubble button {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.message-bubble button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

/* 消息文本优化 */
.message-text {
  font-size: 14px;
  line-height: 1.6;
}

.message-text p {
  margin-bottom: 0.5em;
}

.message-text p:last-child {
  margin-bottom: 0;
}

/* VS Code 风格代码块样式 */
.message-text :deep(.vs-code-block) {
  margin: 16px 0;
  border-radius: 12px;
  overflow: hidden;
  background: #1e1e1e;
  border: 1px solid #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  position: relative;
}

.message-text :deep(.code-header) {
  background: #2d2d30;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #333;
  min-height: 44px;
}

.message-text :deep(.window-controls) {
  display: flex;
  gap: 8px;
  align-items: center;
}

.message-text :deep(.control) {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.message-text :deep(.control.close) {
  background: #ff5f56;
}

.message-text :deep(.control.minimize) {
  background: #ffbd2e;
}

.message-text :deep(.control.maximize) {
  background: #27ca3f;
}

.message-text :deep(.language-label) {
  color: #cccccc;
  font-size: 13px;
  font-weight: 500;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.message-text :deep(.copy-btn) {
  background: transparent;
  border: none;
  color: #858585;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-text :deep(.copy-btn:hover) {
  background: #3c3c3c;
  color: #ffffff;
}

.message-text :deep(.vs-code-block pre) {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
  background: #1e1e1e;
  font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.message-text :deep(.vs-code-block code) {
  color: #d4d4d4;
  background: transparent !important;
  padding: 0 !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 覆盖 highlight.js 默认样式，使用 VS Code 深色主题颜色 */
.message-text :deep(.hljs) {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
}

.message-text :deep(.hljs-keyword) { color: #569cd6 !important; }
.message-text :deep(.hljs-string) { color: #ce9178 !important; }
.message-text :deep(.hljs-number) { color: #b5cea8 !important; }
.message-text :deep(.hljs-comment) { color: #6a9955 !important; font-style: italic; }
.message-text :deep(.hljs-function) { color: #dcdcaa !important; }
.message-text :deep(.hljs-variable) { color: #9cdcfe !important; }
.message-text :deep(.hljs-type) { color: #4ec9b0 !important; }
.message-text :deep(.hljs-class) { color: #4ec9b0 !important; }
.message-text :deep(.hljs-tag) { color: #569cd6 !important; }
.message-text :deep(.hljs-attr) { color: #9cdcfe !important; }
.message-text :deep(.hljs-attribute) { color: #9cdcfe !important; }
.message-text :deep(.hljs-value) { color: #ce9178 !important; }
.message-text :deep(.hljs-operator) { color: #d4d4d4 !important; }
.message-text :deep(.hljs-punctuation) { color: #d4d4d4 !important; }
.message-text :deep(.hljs-built_in) { color: #4ec9b0 !important; }
.message-text :deep(.hljs-literal) { color: #569cd6 !important; }
.message-text :deep(.hljs-title) { color: #dcdcaa !important; }
.message-text :deep(.hljs-params) { color: #9cdcfe !important; }

/* 行内代码样式 */
.message-text :deep(code:not(pre code)) {
  background: rgba(0, 0, 0, 0.1) !important;
  color: #e91e63 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
  font-size: 0.9em !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.user-bubble .message-text :deep(code:not(pre code)) {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* 代码块滚动条样式 */
.message-text :deep(pre::-webkit-scrollbar) {
  height: 8px;
}

.message-text :deep(pre::-webkit-scrollbar-track) {
  background: #2d2d30;
  border-radius: 4px;
}

.message-text :deep(pre::-webkit-scrollbar-thumb) {
  background: #424242;
  border-radius: 4px;
}

.message-text :deep(pre::-webkit-scrollbar-thumb:hover) {
  background: #4a4a4a;
}

/* 复制成功提示 */
.message-text :deep(.copy-btn.copied) {
  color: #27ca3f !important;
}

.message-text :deep(.copy-btn.copied::after) {
  content: '已复制';
  position: absolute;
  top: -30px;
  right: 0;
  background: #333;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; transform: translateY(5px); }
  20%, 80% { opacity: 1; transform: translateY(0); }
}
</style>
