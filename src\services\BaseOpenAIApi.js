// 通用OpenAI兼容API基类
import { ElMessage } from 'element-plus'

// 自定义错误类
export class OpenAICompatibleError extends Error {
  constructor(message, status = 500, details = null) {
    super(message)
    this.name = 'OpenAICompatibleError'
    this.status = status
    this.details = details
  }
}

// 通用OpenAI兼容API基类
export class BaseOpenAIApi {
  constructor(config = {}) {
    this.config = {
      baseUrl: config.baseUrl || 'https://api.openai.com/v1',
      apiKey: config.apiKey || '',
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      defaultModel: config.defaultModel || 'gpt-3.5-turbo',
      headers: config.headers || {},
      ...config
    }
    
    // 模型缓存
    this.modelsCache = null
    this.modelsCacheTime = null
    this.modelsCacheTTL = 5 * 60 * 1000 // 5分钟缓存
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    // 清除模型缓存
    this.clearModelsCache()
  }

  // 清除模型缓存
  clearModelsCache() {
    this.modelsCache = null
    this.modelsCacheTime = null
  }

  // 获取当前API密钥
  getCurrentApiKey() {
    return this.config.apiKey
  }

  // 设置API密钥
  setApiKey(apiKey) {
    this.config.apiKey = apiKey
    this.clearModelsCache()
  }

  // 设置基础URL
  setBaseUrl(baseUrl) {
    this.config.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾的斜杠
    this.clearModelsCache()
  }

  // HTTP请求工具
  async makeRequest(url, options = {}) {
    let lastError = null
    const startTime = Date.now()

    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
          headers: {
            'Authorization': `Bearer ${this.getCurrentApiKey()}`,
            'Content-Type': 'application/json',
            ...this.config.headers,
            ...options.headers
          }
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          let errorData
          try {
            errorData = JSON.parse(errorText)
          } catch {
            errorData = { message: errorText }
          }

          const error = new OpenAICompatibleError(
            errorData.error?.message || `API请求失败: ${response.status} ${response.statusText}`,
            response.status,
            errorData
          )

          // 根据错误类型决定是否重试
          if (this.shouldRetry(response.status, attempt)) {
            lastError = error
            // 等待后重试
            if (attempt < this.config.maxRetries - 1) {
              await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
              continue
            }
          }

          throw error
        }

        return response

      } catch (error) {
        clearTimeout(timeoutId)

        if (error.name === 'AbortError') {
          lastError = new OpenAICompatibleError('请求超时，请稍后重试', 408)
        } else if (error instanceof OpenAICompatibleError) {
          lastError = error
        } else {
          lastError = new OpenAICompatibleError(error.message || '网络请求失败', 500)
        }

        // 如果还有重试机会，继续重试
        if (attempt < this.config.maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
          continue
        }
      }
    }

    // 所有重试都失败了
    throw lastError || new OpenAICompatibleError('请求失败', 500)
  }

  // 判断是否应该重试
  shouldRetry(statusCode, attempt) {
    // 超过最大重试次数
    if (attempt >= this.config.maxRetries - 1) {
      return false
    }

    // 这些状态码值得重试
    const retryableStatusCodes = [
      429, // Too Many Requests - 速率限制
      500, // Internal Server Error
      502, // Bad Gateway
      503, // Service Unavailable
      504  // Gateway Timeout
    ]

    return retryableStatusCodes.includes(statusCode)
  }

  // 获取可用模型列表
  async getAvailableModels(forceRefresh = false) {
    // 检查缓存
    if (!forceRefresh && this.modelsCache && this.modelsCacheTime) {
      const now = Date.now()
      if (now - this.modelsCacheTime < this.modelsCacheTTL) {
        return this.modelsCache
      }
    }

    try {
      const response = await this.makeRequest(`${this.config.baseUrl}/models`)
      const data = await response.json()
      
      let models = []
      if (data.data && Array.isArray(data.data)) {
        models = data.data.map(model => ({
          id: model.id,
          name: model.name || model.id,
          description: model.description || '',
          context_length: model.context_length || model.max_tokens || 4096,
          pricing: model.pricing || {},
          created: model.created || Date.now(),
          owned_by: model.owned_by || 'unknown',
          object: model.object || 'model'
        }))
      }

      // 缓存结果
      this.modelsCache = models
      this.modelsCacheTime = Date.now()
      
      return models
    } catch (error) {
      console.error('获取模型列表失败:', error)
      throw error
    }
  }

  // 生成聊天完成
  async generateChatCompletion(messages, options = {}) {
    try {
      const requestBody = {
        model: options.model || this.config.defaultModel,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2048,
        top_p: options.top_p || 1,
        frequency_penalty: options.frequency_penalty || 0,
        presence_penalty: options.presence_penalty || 0,
        stream: false,
        ...options
      }

      const response = await this.makeRequest(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      })

      const data = await response.json()
      
      if (!data.choices || data.choices.length === 0) {
        throw new OpenAICompatibleError('API返回了空的响应')
      }

      return {
        content: data.choices[0].message.content,
        model: data.model,
        usage: data.usage,
        finish_reason: data.choices[0].finish_reason
      }
    } catch (error) {
      console.error('聊天完成失败:', error)
      throw error
    }
  }

  // 流式生成聊天完成
  async generateChatCompletionStream(messages, options = {}, onChunk) {
    try {
      const requestBody = {
        model: options.model || this.config.defaultModel,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2048,
        top_p: options.top_p || 1,
        frequency_penalty: options.frequency_penalty || 0,
        presence_penalty: options.presence_penalty || 0,
        stream: true,
        ...options
      }

      const response = await this.makeRequest(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      })

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            const trimmed = line.trim()
            if (trimmed === '' || trimmed === 'data: [DONE]') continue
            
            if (trimmed.startsWith('data: ')) {
              try {
                const jsonStr = trimmed.slice(6)
                const data = JSON.parse(jsonStr)
                
                if (data.choices && data.choices[0] && data.choices[0].delta) {
                  const content = data.choices[0].delta.content
                  if (content) {
                    onChunk(content)
                  }
                }
              } catch (parseError) {
                console.warn('解析流数据失败:', parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      console.error('流式聊天失败:', error)
      throw error
    }
  }

  // 测试API连接
  async testConnection() {
    try {
      const models = await this.getAvailableModels(true)
      return {
        success: true,
        modelCount: models.length,
        models: models.slice(0, 5), // 返回前5个模型作为示例
        message: '连接成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.status
      }
    }
  }

  // 错误处理
  handleError(error) {
    if (error instanceof OpenAICompatibleError) {
      switch (error.status) {
        case 401:
          ElMessage.error('API密钥无效，请检查配置')
          break
        case 402:
          ElMessage.error('余额不足，请充值后重试')
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后重试')
          break
        case 500:
          ElMessage.error('服务器内部错误，请稍后重试')
          break
        default:
          ElMessage.error(error.message || '请求失败')
      }
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    console.error('API错误:', error)
  }
}
