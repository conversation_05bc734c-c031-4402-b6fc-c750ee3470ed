<template>
  <div class="performance-test">
    <div class="test-header">
      <h1>🚀 模型选择器性能测试</h1>
      <p>测试滚动性能优化效果</p>
    </div>

    <div class="performance-metrics">
      <div class="metric-card">
        <h3>📊 性能指标</h3>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">FPS:</span>
            <span class="metric-value" :class="{ 'good': fps >= 50, 'warning': fps >= 30 && fps < 50, 'poor': fps < 30 }">
              {{ fps.toFixed(1) }}
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">滚动事件:</span>
            <span class="metric-value">{{ scrollEvents }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">渲染时间:</span>
            <span class="metric-value">{{ renderTime.toFixed(2) }}ms</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">显示模型:</span>
            <span class="metric-value">{{ visibleModels }}/{{ totalModels }}</span>
          </div>
        </div>
      </div>

      <div class="optimization-status">
        <h3>✅ 优化状态</h3>
        <div class="status-list">
          <div class="status-item">
            <span class="status-icon">🎯</span>
            <span>分页加载: 启用</span>
          </div>
          <div class="status-item">
            <span class="status-icon">⚡</span>
            <span>硬件加速: 启用</span>
          </div>
          <div class="status-item">
            <span class="status-icon">🔄</span>
            <span>RAF节流: 启用</span>
          </div>
          <div class="status-item">
            <span class="status-icon">🎨</span>
            <span>CSS Containment: 启用</span>
          </div>
        </div>
      </div>
    </div>

    <div class="test-controls">
      <button @click="startPerformanceTest" :disabled="isTestRunning" class="test-btn">
        {{ isTestRunning ? '测试中...' : '开始性能测试' }}
      </button>
      <button @click="resetMetrics" class="reset-btn">重置指标</button>
      <button @click="toggleSelector" class="toggle-btn">
        {{ showSelector ? '关闭选择器' : '打开选择器' }}
      </button>
    </div>

    <div class="test-results" v-if="testResults.length > 0">
      <h3>📈 测试结果</h3>
      <div class="results-grid">
        <div v-for="(result, index) in testResults" :key="index" class="result-item">
          <div class="result-header">测试 {{ index + 1 }}</div>
          <div class="result-data">
            <span>平均FPS: {{ result.avgFps.toFixed(1) }}</span>
            <span>最低FPS: {{ result.minFps.toFixed(1) }}</span>
            <span>滚动事件: {{ result.scrollEvents }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型选择器 -->
    <div v-if="showSelector" class="selector-container">
      <ModelSelector
        v-model="selectedModel"
        @model-changed="handleModelChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import ModelSelector from '@/components/chat/ModelSelector.vue'

// 响应式数据
const showSelector = ref(false)
const selectedModel = ref('gemini-2.5-flash')
const fps = ref(60)
const scrollEvents = ref(0)
const renderTime = ref(0)
const visibleModels = ref(12)
const totalModels = ref(108)
const isTestRunning = ref(false)
const testResults = ref([])

// 性能监控
let frameCount = 0
let lastTime = performance.now()
let scrollEventCount = 0
let animationId = null

// FPS 计算
const calculateFPS = () => {
  frameCount++
  const currentTime = performance.now()
  
  if (currentTime - lastTime >= 1000) {
    fps.value = frameCount
    frameCount = 0
    lastTime = currentTime
  }
  
  animationId = requestAnimationFrame(calculateFPS)
}

// 滚动事件监听
const handleScroll = () => {
  scrollEventCount++
  scrollEvents.value = scrollEventCount
}

// 性能测试
const startPerformanceTest = async () => {
  if (isTestRunning.value) return
  
  isTestRunning.value = true
  const testDuration = 10000 // 10秒测试
  const startTime = performance.now()
  const initialScrollEvents = scrollEvents.value
  const fpsReadings = []
  
  // 自动滚动测试
  const testInterval = setInterval(() => {
    fpsReadings.push(fps.value)
    
    // 模拟滚动
    const container = document.querySelector('.models-container')
    if (container) {
      container.scrollTop += 50
    }
  }, 100)
  
  // 测试结束
  setTimeout(() => {
    clearInterval(testInterval)
    isTestRunning.value = false
    
    const avgFps = fpsReadings.reduce((a, b) => a + b, 0) / fpsReadings.length
    const minFps = Math.min(...fpsReadings)
    const testScrollEvents = scrollEvents.value - initialScrollEvents
    
    testResults.value.push({
      avgFps,
      minFps,
      scrollEvents: testScrollEvents,
      duration: testDuration / 1000
    })
  }, testDuration)
}

// 重置指标
const resetMetrics = () => {
  scrollEventCount = 0
  scrollEvents.value = 0
  testResults.value = []
}

// 切换选择器
const toggleSelector = () => {
  showSelector.value = !showSelector.value
}

// 模型变更处理
const handleModelChange = (model) => {
  console.log('性能测试 - 模型已切换:', model)
}

// 生命周期
onMounted(() => {
  calculateFPS()
  
  // 监听滚动事件
  document.addEventListener('scroll', handleScroll, { passive: true })
  
  // 模拟渲染时间
  const measureRenderTime = () => {
    const start = performance.now()
    // 模拟一些DOM操作
    requestAnimationFrame(() => {
      renderTime.value = performance.now() - start
    })
  }
  
  setInterval(measureRenderTime, 1000)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  document.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.performance-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    font-size: 1.125rem;
  }
}

.performance-metrics {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  
  h3 {
    margin-bottom: 1.5rem;
    color: #374151;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  
  .metric-label {
    color: #6b7280;
    font-weight: 500;
  }
  
  .metric-value {
    font-weight: 600;
    font-size: 1.125rem;
    
    &.good { color: #10b981; }
    &.warning { color: #f59e0b; }
    &.poor { color: #ef4444; }
  }
}

.optimization-status {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  
  h3 {
    margin-bottom: 1.5rem;
    color: #374151;
  }
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  
  .status-icon {
    font-size: 1.25rem;
  }
}

.test-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.test-btn, .reset-btn, .toggle-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.test-btn {
  background: #3b82f6;
  color: white;
  border: none;
  
  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

.reset-btn {
  background: #6b7280;
  color: white;
  border: none;
  
  &:hover {
    background: #4b5563;
  }
}

.toggle-btn {
  background: #10b981;
  color: white;
  border: none;
  
  &:hover {
    background: #059669;
  }
}

.test-results {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  
  h3 {
    margin-bottom: 1.5rem;
    color: #374151;
  }
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.result-item {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  
  .result-header {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .result-data {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
  }
}

.selector-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

@media (max-width: 768px) {
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .test-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
