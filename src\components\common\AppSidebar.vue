<template>
  <div class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      :collapse="safeSidebarCollapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <!-- 主要功能菜单 -->
      <el-menu-item
        v-for="item in mainMenuItems"
        :key="item.name"
        :index="item.path"
        @click="handleMenuClick(item)"
      >
        <el-icon>
          <component :is="item.icon" />
        </el-icon>
        <template #title>{{ item.title }}</template>
      </el-menu-item>

      <!-- 分割线 -->
      <el-divider v-if="!safeSidebarCollapsed" />

      <!-- 创作工具菜单 -->
      <el-sub-menu index="create" v-if="safeIsLoggedIn">
        <template #title>
          <el-icon><Plus /></el-icon>
          <span>创作工具</span>
        </template>
        <el-menu-item
          v-for="item in createMenuItems"
          :key="item.name"
          :index="item.path"
          @click="handleMenuClick(item)"
        >
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <template #title>{{ item.title }}</template>
        </el-menu-item>
      </el-sub-menu>

      <!-- 个人中心菜单 -->
      <el-sub-menu index="user" v-if="safeIsLoggedIn">
        <template #title>
          <el-icon><User /></el-icon>
          <span>个人中心</span>
        </template>
        <el-menu-item
          v-for="item in userMenuItems"
          :key="item.name"
          :index="item.path"
          @click="handleMenuClick(item)"
        >
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <template #title>{{ item.title }}</template>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>

    <!-- 底部信息 -->
    <div v-if="!safeSidebarCollapsed" class="sidebar-footer">
      <div class="user-stats" v-if="safeIsLoggedIn">
        <div class="stat-item">
          <span class="label">作品数</span>
          <span class="value">{{ userStats.artworkCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">获赞数</span>
          <span class="value">{{ userStats.likeCount || 0 }}</span>
        </div>
      </div>
      
      <div class="app-info">
        <p class="version">v{{ appVersion }}</p>
        <p class="copyright">© 2025 AI创作助手</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { APP_CONFIG } from '@/config'
import { Plus, User } from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useSafeStore, safeStoreAccess } from '@/utils/appInit'

const route = useRoute()
const router = useRouter()

// 安全地获取stores
const appStore = useSafeStore(() => useAppStore(), null)
const userStore = useSafeStore(() => useUserStore(), null)

// 响应式数据
const userStats = ref({
  artworkCount: 0,
  likeCount: 0,
})

// 计算属性
const activeMenu = computed(() => {
  return route.path
})

const appVersion = computed(() => {
  return APP_CONFIG.version
})

// 使用安全的store访问
const safeSidebarCollapsed = computed(() => {
  return safeStoreAccess(() => useAppStore(), 'sidebarCollapsed', false)
})

const safeIsLoggedIn = computed(() => {
  return safeStoreAccess(() => useUserStore(), 'isLoggedIn', false)
})

// 主要功能菜单
const mainMenuItems = [
  {
    name: 'Home',
    path: '/home',
    title: '首页',
    icon: 'House',
  },
  {
    name: 'Gallery',
    path: '/gallery',
    title: '作品展示',
    icon: 'Picture',
  },
]

// 创作工具菜单
const createMenuItems = [
  {
    name: 'Chat',
    path: '/chat',
    title: 'AI聊天',
    icon: 'ChatDotRound',
  },
  {
    name: 'Drawing',
    path: '/drawing',
    title: 'AI绘画',
    icon: 'Brush',
  },
]

// 个人中心菜单
const userMenuItems = [
  {
    name: 'Profile',
    path: '/profile',
    title: '个人资料',
    icon: 'User',
  },
  {
    name: 'MyWorks',
    path: '/profile/works',
    title: '我的作品',
    icon: 'Document',
  },
  {
    name: 'Favorites',
    path: '/profile/favorites',
    title: '我的收藏',
    icon: 'Star',
  },
  {
    name: 'Settings',
    path: '/settings',
    title: '设置',
    icon: 'Setting',
  },
]

// 方法
const handleMenuClick = (item) => {
  // 暂时禁用移动端侧边栏控制
  // TODO: 稍后重新启用

  // 路由跳转
  if (item.path !== route.path) {
    router.push(item.path)
  }
}

// 暂时禁用用户统计加载
// const loadUserStats = async () => {
//   if (safeIsLoggedIn.value) {
//     try {
//       // 这里可以调用API获取用户统计数据
//       // const stats = await getUserStatsApi()
//       // userStats.value = stats.data
//     } catch (error) {
//       console.error('加载用户统计失败:', error)
//     }
//   }
// }

// 生命周期
onMounted(async () => {
  console.log('AppSidebar: 组件已挂载，开始初始化')

  try {
    // 如果用户已登录，加载用户统计数据
    if (safeIsLoggedIn.value) {
      // loadUserStats() // 暂时注释，等API准备好后启用
      console.log('✅ AppSidebar: 用户已登录，可以加载统计数据')
    } else {
      console.log('ℹ️ AppSidebar: 用户未登录，跳过统计数据加载')
    }
  } catch (error) {
    console.warn('⚠️ AppSidebar: 初始化失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: $bg-color;
  padding: 0;
  margin: 0;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  
  .el-menu-item {
    &:hover {
      background-color: $primary-light-9;
      color: $primary-color;
    }
    
    &.is-active {
      background-color: $primary-light-8;
      color: $primary-color;
      border-right: 3px solid $primary-color;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: $primary-color;
      }
    }
  }
  
  .el-sub-menu {
    .el-sub-menu__title {
      &:hover {
        background-color: $primary-light-9;
        color: $primary-color;
      }
    }
    
    .el-menu-item {
      padding-left: 48px !important;
      
      &:hover {
        background-color: $primary-light-9;
      }
      
      &.is-active {
        background-color: $primary-light-8;
        color: $primary-color;
      }
    }
  }
}

.sidebar-footer {
  padding: $spacing-md;
  border-top: 1px solid $border-color-extra-light;
  background-color: $bg-color-light;
  
  .user-stats {
    margin-bottom: $spacing-md;
    
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-xs;
      
      .label {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
      
      .value {
        font-size: $font-size-sm;
        font-weight: 600;
        color: $primary-color;
      }
    }
  }
  
  .app-info {
    text-align: center;
    
    .version {
      font-size: $font-size-xs;
      color: $text-color-placeholder;
      margin: 0 0 $spacing-xs 0;
    }
    
    .copyright {
      font-size: $font-size-xs;
      color: $text-color-placeholder;
      margin: 0;
    }
  }
}

// 折叠状态样式
.sidebar-menu.el-menu--collapse {
  .sidebar-footer {
    display: none;
  }
}

// 响应式设计
@include respond-to(sm) {
  .sidebar-menu {
    .el-menu-item,
    .el-sub-menu .el-menu-item {
      padding-left: 20px !important;
    }
  }
}
</style>
