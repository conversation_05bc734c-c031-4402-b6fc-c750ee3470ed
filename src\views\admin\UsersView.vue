<template>
  <div class="users-view">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理本地存储的用户数据</p>
    </div>

    <div class="users-container">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <div class="stat-number">{{ totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🆕</div>
          <div class="stat-content">
            <div class="stat-number">{{ newUsersToday }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🔥</div>
          <div class="stat-content">
            <div class="stat-number">{{ activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="users-table-container">
        <div class="table-header">
          <h2>用户列表</h2>
          <div class="table-actions">
            <button
              class="btn btn-info"
              @click="refreshUsers"
              :disabled="loading"
            >
              {{ loading ? '刷新中...' : '刷新' }}
            </button>
            <button
              class="btn btn-primary"
              @click="exportUsers"
            >
              导出
            </button>
            <button
              class="btn btn-danger"
              @click="clearAllUsers"
            >
              清空数据
            </button>
          </div>
        </div>

        <el-table
          :data="users"
          v-loading="loading"
          class="users-table"
          stripe
          border
          :scroll-x="true"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="100" />
          <el-table-column prop="username" label="用户名" min-width="120" />
          <el-table-column prop="email" label="邮箱" min-width="200" show-overflow-tooltip />
          <el-table-column prop="role" label="角色" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'" size="small">
                {{ row.role === 'admin' ? '管理员' : '用户' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="注册时间" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginAt" label="最后登录" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
            </template>
          </el-table-column>
          <el-table-column prop="isActive" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isActive ? 'success' : 'danger'" size="small">
                {{ row.isActive ? '活跃' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <button
                  class="btn btn-info btn-small"
                  @click="viewUser(row)"
                >
                  查看
                </button>
                <button
                  class="btn btn-danger btn-small"
                  @click="deleteUser(row)"
                >
                  删除
                </button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog 
      v-model="showUserDialog" 
      title="用户详情" 
      width="600px"
      :before-close="closeUserDialog"
    >
      <div v-if="selectedUser" class="user-detail">
        <div class="user-avatar">
          <img :src="selectedUser.avatar" :alt="selectedUser.username" />
        </div>
        <div class="user-info">
          <h3>{{ selectedUser.username }}</h3>
          <p>{{ selectedUser.email }}</p>
          <div class="user-meta">
            <div class="meta-item">
              <span class="label">用户ID:</span>
              <span class="value">{{ selectedUser.id }}</span>
            </div>
            <div class="meta-item">
              <span class="label">角色:</span>
              <span class="value">{{ selectedUser.role === 'admin' ? '管理员' : '用户' }}</span>
            </div>
            <div class="meta-item">
              <span class="label">注册时间:</span>
              <span class="value">{{ formatDate(selectedUser.createdAt) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">最后登录:</span>
              <span class="value">{{ selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : '从未登录' }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <button class="btn btn-info" @click="closeUserDialog">关闭</button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores'
import { celebrateSuccess } from '@/utils/celebration'

const userStore = useUserStore()

// 响应式数据
const users = ref([])
const loading = ref(false)
const showUserDialog = ref(false)
const selectedUser = ref(null)

// 计算属性
const totalUsers = computed(() => users.value.length)
const newUsersToday = computed(() => {
  const today = new Date().toDateString()
  return users.value.filter(user => 
    new Date(user.createdAt).toDateString() === today
  ).length
})
const activeUsers = computed(() => 
  users.value.filter(user => user.isActive).length
)

// 方法
const refreshUsers = () => {
  loading.value = true
  try {
    users.value = userStore.getLocalUserDatabase()
    ElMessage.success('用户数据刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const exportUsers = () => {
  try {
    const dataStr = JSON.stringify(users.value, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `users_${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
    celebrateSuccess('📤 用户数据导出成功！', true)
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const clearAllUsers = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有用户数据吗？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    localStorage.removeItem('ai_creative_users_db')
    users.value = []
    celebrateSuccess('🗑️ 用户数据已清空！', true)
  } catch {
    // 用户取消操作
  }
}

const viewUser = (user) => {
  selectedUser.value = user
  showUserDialog.value = true
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const updatedUsers = users.value.filter(u => u.id !== user.id)
    localStorage.setItem('ai_creative_users_db', JSON.stringify(updatedUsers))
    users.value = updatedUsers
    ElMessage.success('用户删除成功')
  } catch {
    // 用户取消操作
  }
}

const closeUserDialog = () => {
  showUserDialog.value = false
  selectedUser.value = null
}

const formatDate = (dateString) => {
  if (!dateString) return '无'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshUsers()
})
</script>

<style lang="scss" scoped>
.users-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  
  .stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .stat-content {
    .stat-number {
      font-size: 1.8rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 0.25rem;
    }
    
    .stat-label {
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

.users-table-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
  }
  
  .table-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

/* 自定义按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 50px;
}

.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-info {
  background-color: #909399;
  border-color: #909399;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

.btn-danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #f78989;
  border-color: #f78989;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.user-detail {
  display: flex;
  gap: 1.5rem;
  
  .user-avatar {
    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
    }
  }
  
  .user-info {
    flex: 1;
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #2c3e50;
    }
    
    p {
      margin: 0 0 1rem 0;
      color: #7f8c8d;
    }
    
    .user-meta {
      .meta-item {
        display: flex;
        margin-bottom: 0.5rem;
        
        .label {
          font-weight: 600;
          color: #2c3e50;
          width: 100px;
        }
        
        .value {
          color: #7f8c8d;
        }
      }
    }
  }
}

/* 表格样式优化 */
.users-table {
  .el-table__body-wrapper {
    overflow-x: auto;
  }
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .users-view {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;

    .stat-icon {
      width: 50px;
      height: 50px;
      font-size: 1.5rem;
    }

    .stat-content .stat-number {
      font-size: 1.5rem;
    }
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .table-actions {
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .users-table-container {
    padding: 1rem;
    overflow-x: auto;
  }

  .user-detail {
    flex-direction: column;
    text-align: center;

    .user-avatar {
      align-self: center;
    }
  }

  .btn {
    font-size: 12px;
    padding: 6px 12px;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .users-view {
    padding: 0.5rem;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .table-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn {
    width: 100%;
  }
}
</style>
