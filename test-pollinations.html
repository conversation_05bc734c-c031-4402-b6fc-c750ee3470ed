<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pollinations API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .image-result {
            text-align: center;
            margin-top: 20px;
        }
        .image-result img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Pollinations API 测试</h1>
        <p>测试 Pollinations AI 图像生成功能</p>
        
        <div class="input-group">
            <label for="prompt">图像描述 (Prompt):</label>
            <textarea id="prompt" rows="3" placeholder="例如：一只可爱的小猫坐在花园里">一只可爱的小猫坐在花园里</textarea>
        </div>
        
        <div class="input-group">
            <label for="width">宽度:</label>
            <select id="width">
                <option value="512">512px</option>
                <option value="768">768px</option>
                <option value="1024" selected>1024px</option>
            </select>
        </div>
        
        <div class="input-group">
            <label for="height">高度:</label>
            <select id="height">
                <option value="512">512px</option>
                <option value="768">768px</option>
                <option value="1024" selected>1024px</option>
            </select>
        </div>
        
        <button onclick="generateImage()" id="generateBtn">🎨 生成图像</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function generateImage() {
            const prompt = document.getElementById('prompt').value.trim();
            const width = document.getElementById('width').value;
            const height = document.getElementById('height').value;
            const resultDiv = document.getElementById('result');
            const generateBtn = document.getElementById('generateBtn');
            
            if (!prompt) {
                resultDiv.innerHTML = '<div class="error">请输入图像描述</div>';
                return;
            }
            
            // 禁用按钮并显示加载状态
            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 生成中...';
            resultDiv.innerHTML = '<div class="loading">正在生成图像，请稍候...</div>';
            
            try {
                // 构建Pollinations API URL
                const encodedPrompt = encodeURIComponent(prompt);
                const randomSeed = Math.floor(Math.random() * 1000000) + 1;
                const apiUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}?width=${width}&height=${height}&seed=${randomSeed}&nologo=true&enhance=true&safe=true`;
                
                console.log('API URL:', apiUrl);
                
                // 直接使用图像URL
                const img = new Image();
                img.onload = function() {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 图像生成成功！</div>
                        <div class="image-result">
                            <img src="${apiUrl}" alt="生成的图像" />
                            <p><strong>提示词:</strong> ${prompt}</p>
                            <p><strong>尺寸:</strong> ${width}x${height}</p>
                            <p><strong>种子:</strong> ${randomSeed}</p>
                            <p><a href="${apiUrl}" target="_blank">在新窗口中查看</a></p>
                        </div>
                    `;
                };
                
                img.onerror = function() {
                    resultDiv.innerHTML = '<div class="error">❌ 图像加载失败，请重试</div>';
                };
                
                img.src = apiUrl;
                
            } catch (error) {
                console.error('生成图像错误:', error);
                resultDiv.innerHTML = `<div class="error">❌ 生成失败: ${error.message}</div>`;
            } finally {
                // 恢复按钮状态
                generateBtn.disabled = false;
                generateBtn.textContent = '🎨 生成图像';
            }
        }
        
        // 页面加载完成后自动测试
        window.onload = function() {
            console.log('Pollinations API 测试页面已加载');
        };
    </script>
</body>
</html>
